# Agency API Routes Testing Guide

## Prerequisites
1. Start the services:
```bash
# Start infrastructure
docker compose up -d postgres redis rabbitmq mongodb-chat minio

# Start core services
docker compose up -d audit-logging messaging-service auth-service auth-apigw

# Start agency services
docker compose up -d agency-service agency-apigw
```

2. Get authentication token:
```bash
# Login to get JWT token
curl -X POST http://localhost:4006/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

## Agency Routes

### 1. Create Agency
```bash
curl -X POST http://agency-api.localhost/api/agencies \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "agencyName": "Test Agency",
    "agencyLogo": "https://example.com/logo.png",
    "address": "123 Main St, City, Country",
    "agencySize": "Medium",
    "about": "A test agency for educational services",
    "primaryContactNumber": "+1234567890",
    "country": "USA",
    "state": "California",
    "city": "Los Angeles",
    "postalCode": "90210",
    "agencyRegistrationNumber": "REG123456",
    "websiteUrl": "https://testagency.com",
    "email": "<EMAIL>"
  }'
```

### 2. List Agencies
```bash
curl -X GET "http://agency-api.localhost/api/agencies?page=1&pageSize=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 3. Get Agency by ID
```bash
curl -X GET http://agency-api.localhost/api/agencies/1 \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 4. Update Agency
```bash
curl -X PUT http://agency-api.localhost/api/agencies/1 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "agencyName": "Updated Test Agency",
    "about": "Updated description"
  }'
```

### 5. Delete Agency
```bash
curl -X DELETE http://agency-api.localhost/api/agencies/1 \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Agency Branch Routes

### 1. Create Agency Branches
```bash
curl -X POST http://agency-api.localhost/api/agency-branches \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "agencyId": "1",
    "data": [
      {
        "branchName": "Main Branch",
        "address": "456 Branch St, City, Country",
        "country": "USA",
        "state": "California",
        "city": "San Francisco",
        "postalCode": "94102",
        "managerContactNumber": "+1987654321",
        "email": "<EMAIL>"
      }
    ]
  }'
```

### 2. List Agency Branches
```bash
curl -X GET "http://agency-api.localhost/api/agency-branches?agencyId=1&page=1&pageSize=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 3. Get Branch by ID
```bash
curl -X GET http://agency-api.localhost/api/agency-branches/1 \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 4. Update Agency Branches
```bash
curl -X PUT http://agency-api.localhost/api/agency-branches \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "agencyId": "1",
    "data": [
      {
        "id": "1",
        "branchName": "Updated Main Branch",
        "address": "456 Updated Branch St, City, Country",
        "country": "USA",
        "state": "California",
        "city": "San Francisco",
        "postalCode": "94102",
        "managerContactNumber": "+1987654321",
        "email": "<EMAIL>"
      }
    ]
  }'
```

### 5. Delete Branch
```bash
curl -X DELETE http://agency-api.localhost/api/agency-branches/1 \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Agency Contact Routes

### 1. Create Agency Contact
```bash
curl -X POST http://agency-api.localhost/api/agency-contacts \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "agencyId": "1",
    "ownerName": "John Doe",
    "ownerContactNumber": "+1234567890",
    "ownerAlternateContactNumber": "+1234567891",
    "ownerEmail": "<EMAIL>",
    "primaryPersonName": "Jane Smith",
    "primaryPersonDesignation": "Manager",
    "primaryContactNumber": "+1987654321",
    "primaryAlternateContactNumber": "+1987654322",
    "primaryEmail": "<EMAIL>"
  }'
```

### 2. List Agency Contacts
```bash
curl -X GET "http://agency-api.localhost/api/agency-contacts?agencyId=1&page=1&pageSize=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 3. Get Contact by ID
```bash
curl -X GET http://agency-api.localhost/api/agency-contacts/1 \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 4. Update Agency Contact
```bash
curl -X PUT http://agency-api.localhost/api/agency-contacts/1 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "ownerName": "John Updated Doe",
    "primaryPersonName": "Jane Updated Smith"
  }'
```

### 5. Delete Contact
```bash
curl -X DELETE http://agency-api.localhost/api/agency-contacts/1 \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Expected Responses

All successful responses should have this format:
```json
{
  "status": 200,
  "message": "Operation successful",
  "data": { /* response data */ },
  "error": null
}
```

Error responses:
```json
{
  "status": 400/401/403/404/500,
  "message": "Error message",
  "data": null,
  "error": {
    "code": "ERROR_CODE",
    "message": "Detailed error message",
    "details": "Stack trace or additional details",
    "validationErrors": []
  }
}
```

## Troubleshooting

1. **401 Unauthorized**: Check JWT token validity and permissions
2. **403 Forbidden**: User doesn't have required agency management permissions
3. **404 Not Found**: Resource doesn't exist
4. **500 Internal Server Error**: Check service logs

Check logs:
```bash
docker compose logs -f agency-apigw
docker compose logs -f agency-service
```
