#!/bin/bash

# Test script to verify proto fix
echo "=== Testing Proto Fix ==="
echo ""

echo "1. Building dto library..."
nx build dto
if [ $? -eq 0 ]; then
    echo "✅ DTO library built successfully"
else
    echo "❌ DTO library build failed"
    exit 1
fi
echo ""

echo "2. Building auth-service..."
nx build auth-service
if [ $? -eq 0 ]; then
    echo "✅ Auth service built successfully"
else
    echo "❌ Auth service build failed"
    exit 1
fi
echo ""

echo "3. Building auth-apigw..."
nx build auth-apigw
if [ $? -eq 0 ]; then
    echo "✅ Auth API gateway built successfully"
else
    echo "❌ Auth API gateway build failed"
    exit 1
fi
echo ""

echo "=== Proto Fix Verification Complete ==="
echo ""
echo "✅ All proto conflicts resolved"
echo "✅ Services build without errors"
echo "✅ Ready for testing employee API endpoints"
echo ""
echo "The duplicate 'DepartmentInfo' error has been fixed by:"
echo "1. Renaming employee-specific message types to avoid conflicts"
echo "2. Using EmployeeDepartmentData, EmployeeAddressData, etc."
echo "3. Keeping existing DepartmentInfo from common.proto for department hierarchy"
echo "4. Maintaining interface compatibility in API gateway and service layers"
