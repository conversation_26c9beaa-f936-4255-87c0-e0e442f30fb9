# Quick Employee API Tests

## 🔑 Get Access Token (Required First)

```bash
# Get access token
TOKEN=$(curl -s -X POST "http://localhost:4006/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "superadmin123", "ipAddress": "127.0.0.1"}' \
  | grep -o '"accessToken":"[^"]*' | cut -d'"' -f4)

echo "Token: $TOKEN"
```

## 🚀 Quick Tests

### 1. Create Employee (Comprehensive)
```bash
curl -X POST "http://localhost:4006/api/auth/employees" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "email": "<EMAIL>",
    "password": "Test123!",
    "firstName": "John",
    "lastName": "Doe",
    "phone": "******-0123",
    "dateOfBirth": "1990-05-15",
    "bloodGroup": "O+",
    "gender": "male",
    "joiningDate": "2024-01-15",
    "jobType": "full-time",
    "jobStatus": "active",
    "department": "Engineering",
    "designation": "Developer"
  }'
```

### 2. Create Employee (Legacy Name)
```bash
curl -X POST "http://localhost:4006/api/auth/employees" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "email": "<EMAIL>",
    "password": "Test456!",
    "name": "Jane Smith",
    "department": "Marketing"
  }'
```

### 3. Update Employee (Comprehensive)
```bash
curl -X PUT "http://localhost:4006/api/auth/employees/1" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "firstName": "Updated",
    "lastName": "Name",
    "designation": "Senior Developer",
    "workLocation": "Remote",
    "emergencyContactName": "Emergency Contact",
    "emergencyContactPhone": "******-9999"
  }'
```

### 4. Update Employee (Legacy Name)
```bash
curl -X PUT "http://localhost:4006/api/auth/employees/1" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "name": "Alice Johnson",
    "jobStatus": "inactive"
  }'
```

### 5. Get Employee
```bash
curl -X GET "http://localhost:4006/api/auth/employees/1" \
  -H "Authorization: Bearer $TOKEN"
```

### 6. List Employees
```bash
curl -X GET "http://localhost:4006/api/auth/employees" \
  -H "Authorization: Bearer $TOKEN"
```

## 🧪 Validation Tests

### Test Invalid Job Status
```bash
curl -X PUT "http://localhost:4006/api/auth/employees/1" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"jobStatus": "invalid_status"}'
```

### Test Invalid Date
```bash
curl -X PUT "http://localhost:4006/api/auth/employees/1" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"dateOfBirth": "invalid-date"}'
```

## 📋 Field Categories

### ✅ Personal Info
`firstName`, `lastName`, `email`, `phone`, `dateOfBirth`, `bloodGroup`, `gender`, `nationality`, `maritalStatus`

### ✅ Employment  
`joiningDate`, `jobType`, `jobStatus`, `employeeId`, `department`, `designation`, `reportingTo`, `workLocation`

### ✅ Address
`presentAddress`, `presentCountry`, `presentState`, `presentCity`, `presentPostalCode`, `permanentAddress`, `permanentCountry`, `permanentState`, `permanentCity`, `permanentPostalCode`

### ✅ Emergency Contact
`emergencyContactName`, `emergencyContactRelation`, `emergencyContactPhone`, `emergencyContactEmail`, `emergencyContactAddress`

### ✅ Security
`securityType`, `securityMaturity`, `securityBankCode`, `securityExpiryDate`

### ✅ Banking
`accountHolderName`, `accountNumber`, `bankName`, `branchName`, `routingNumber`, `accountType`

### ✅ Social Media
`linkedinUrl`, `twitterUrl`, `facebookUrl`, `instagramUrl`

## 🔧 Valid Values

**Job Types:** `full-time`, `part-time`, `contract`, `intern`  
**Job Statuses:** `active`, `inactive`, `terminated`, `on-leave`  
**Blood Groups:** `A+`, `A-`, `B+`, `B-`, `AB+`, `AB-`, `O+`, `O-`  
**Gender:** `male`, `female`, `other`  
**Marital Status:** `single`, `married`, `divorced`, `widowed`  
**Account Types:** `checking`, `savings`

## 🎯 One-Liner Complete Test

```bash
# Run the complete test script
./test-employee-endpoints.sh
```

## 📝 Expected Response Format

```json
{
  "success": true,
  "message": "Employee created/updated successfully",
  "employee": {
    "userId": 1,
    "firstName": "John",
    "lastName": "Doe",
    "jobType": "full-time",
    "jobStatus": "active",
    "dateOfBirth": "1990-05-15T00:00:00.000Z",
    "bloodGroup": "O+",
    "gender": "male",
    "createdAt": "...",
    "updatedAt": "..."
  }
}
```
