#!/bin/bash

echo "Testing Enhanced Department APIs with Flat Lists..."
echo "=================================================="

TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.CMUcrCW0pQ8PZflHcwcC2dyk7gBoa9_qNZwQq53R1RY"

echo ""
echo "Test 1: GET organizations/1/departments?includeHierarchy=true"
echo "============================================================"
curl -X GET "http://localhost:4006/api/auth/organizations/1/departments?includeHierarchy=true" \
  -H "Authorization: Bearer $TOKEN" | jq '.'

echo ""
echo "Test 2: GET org-departments?organizationId=1&parentId=1&includeChildren=true&page=1&limit=10"
echo "=========================================================================================="
curl -X GET "http://localhost:4006/api/auth/org-departments?organizationId=1&parentId=1&includeChildren=true&page=1&limit=10" \
  -H "Authorization: Bearer $TOKEN" | jq '.'

echo ""
echo "Test 3: GET org-departments?organizationId=1 (all departments flat)"
echo "=================================================================="
curl -X GET "http://localhost:4006/api/auth/org-departments?organizationId=1" \
  -H "Authorization: Bearer $TOKEN" | jq '.'

echo ""
echo "All tests completed!"
