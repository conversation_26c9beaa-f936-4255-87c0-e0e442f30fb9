# Employee Endpoints Test Guide

## Overview
This guide demonstrates how to test the updated employee endpoints that now support both the new nested structure and legacy flat structure for backward compatibility.

## Authentication
First, get an authentication token:

```bash
curl --location 'http://auth-api.localhost/api/auth/login' \
--header 'Content-Type: application/json' \
--data-raw '{
    "email": "<EMAIL>",
    "password": "SuperAdmin123!"
}'
```

Save the `accessToken` from the response for use in subsequent requests.

## 1. Create Employee - New Nested Structure

This is the new format that matches your curl command structure:

```bash
curl --location 'http://auth-api.localhost/api/auth/employees' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer YOUR_ACCESS_TOKEN' \
--data-raw '{
    "email": "<EMAIL>",
    "password": "TempPass123!",
    "firstName": "<PERSON>",
    "lastName": "Doe",
    "phone": "******-0123",
    "dateOfBirth": "1990-05-15",
    "bloodGroup": "O+",
    "gender": "male",
    "nationality": "American",
    "maritalStatus": "married",
    "joiningDate": "2024-01-15",
    "jobType": "full-time",
    "jobStatus": "active",
    "departmentInfo": {
        "employeeId": "EMP001",
        "department": "Engineering",
        "designation": "Senior Developer",
        "supervisor": "Jane Smith",
        "workLocation": "New York Office"
    },
    "presentAddress": {
        "presentAddress": "123 Main Street",
        "presentCountry": "USA",
        "presentState": "New York",
        "presentCity": "New York",
        "presentPostalCode": "10001"
    },
    "permanentAddress": {
        "permanentAddress": "456 Oak Avenue",
        "permanentCountry": "USA",
        "permanentState": "California",
        "permanentCity": "Los Angeles",
        "permanentPostalCode": "90210"
    },
    "emergencyContact": {
        "emergencyContactType": "Primary Contact",
        "emergencyContactName": "Mary Doe",
        "emergencyContactRelation": "spouse",
        "emergencyContactPhone": "******-0456",
        "emergencyContactEmail": "<EMAIL>",
        "emergencyContactAddress": "123 Main Street, New York, NY 10001"
    },
    "identityInfo": {
        "type": "NID",
        "country": "US",
        "number": "XXXX1100",
        "issueDate": "",
        "expiryDate": ""
    },
    "bankAccount": {
        "accountHolderName": "John Doe",
        "accountNumber": "**********",
        "bankName": "Chase Bank",
        "branchName": "Manhattan Branch",
        "routingNumber": "*********",
        "accountType": "checking"
    },
    "socialLinks": {
        "linkedinUrl": "https://linkedin.com/in/johndoe",
        "twitterUrl": "https://twitter.com/johndoe",
        "facebookUrl": "https://facebook.com/johndoe",
        "instagramUrl": "https://instagram.com/johndoe"
    }
}'
```

## 2. Create Employee - Legacy Flat Structure

This maintains backward compatibility:

```bash
curl --location 'http://auth-api.localhost/api/auth/employees' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer YOUR_ACCESS_TOKEN' \
--data-raw '{
    "email": "<EMAIL>",
    "password": "TempPass123!",
    "firstName": "Jane",
    "lastName": "Smith",
    "phone": "******-0124",
    "dateOfBirth": "1985-03-20",
    "bloodGroup": "A+",
    "gender": "female",
    "nationality": "Canadian",
    "maritalStatus": "single",
    "joiningDate": "2023-06-01",
    "jobType": "full-time",
    "jobStatus": "active",
    "employeeId": "EMP002",
    "department": "Marketing",
    "designation": "Marketing Manager",
    "workLocation": "Toronto Office",
    "presentAddressFlat": "789 Queen Street",
    "presentCountry": "Canada",
    "presentState": "Ontario",
    "presentCity": "Toronto",
    "presentPostalCode": "M5V 3A8",
    "emergencyContactName": "Bob Smith",
    "emergencyContactRelation": "brother",
    "emergencyContactPhone": "******-0789",
    "emergencyContactEmail": "<EMAIL>",
    "accountHolderName": "Jane Smith",
    "accountNumber": "**********",
    "bankName": "TD Bank",
    "branchName": "Queen Street Branch",
    "routingNumber": "*********",
    "accountType": "savings",
    "linkedinUrl": "https://linkedin.com/in/janesmith"
}'
```

## 3. Update Employee - New Nested Structure

```bash
curl --location --request PUT 'http://auth-api.localhost/api/auth/employees/1' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer YOUR_ACCESS_TOKEN' \
--data-raw '{
    "firstName": "John",
    "lastName": "Doe Updated",
    "phone": "******-0123",
    "departmentInfo": {
        "designation": "Lead Developer",
        "supervisor": "Alice Johnson"
    },
    "emergencyContact": {
        "emergencyContactPhone": "******-0999"
    },
    "bankAccount": {
        "accountType": "savings"
    }
}'
```

## 4. Get Employee

```bash
curl --location 'http://auth-api.localhost/api/auth/employees/1' \
--header 'Authorization: Bearer YOUR_ACCESS_TOKEN'
```

## 5. List All Employees

```bash
curl --location 'http://auth-api.localhost/api/auth/employees' \
--header 'Authorization: Bearer YOUR_ACCESS_TOKEN'
```

## Expected Response Structure

The response will include all fields in a flat structure for easy access:

```json
{
    "success": true,
    "message": "Employee created successfully",
    "employee": {
        "userId": 123,
        "firstName": "John",
        "lastName": "Doe",
        "phone": "******-0123",
        "dateOfBirth": "1990-05-15T00:00:00.000Z",
        "bloodGroup": "O+",
        "gender": "male",
        "nationality": "American",
        "maritalStatus": "married",
        "joiningDate": "2024-01-15T00:00:00.000Z",
        "jobType": "full-time",
        "jobStatus": "active",
        "employeeId": "EMP001",
        "department": "Engineering",
        "designation": "Senior Developer",
        "supervisor": "Jane Smith",
        "workLocation": "New York Office",
        "presentAddress": "123 Main Street",
        "presentCountry": "USA",
        "presentState": "New York",
        "presentCity": "New York",
        "presentPostalCode": "10001",
        "permanentAddress": "456 Oak Avenue",
        "permanentCountry": "USA",
        "permanentState": "California",
        "permanentCity": "Los Angeles",
        "permanentPostalCode": "90210",
        "emergencyContactType": "Primary Contact",
        "emergencyContactName": "Mary Doe",
        "emergencyContactRelation": "spouse",
        "emergencyContactPhone": "******-0456",
        "emergencyContactEmail": "<EMAIL>",
        "emergencyContactAddress": "123 Main Street, New York, NY 10001",
        "identityType": "NID",
        "identityCountry": "US",
        "identityNumber": "XXXX1100",
        "accountHolderName": "John Doe",
        "accountNumber": "**********",
        "bankName": "Chase Bank",
        "branchName": "Manhattan Branch",
        "routingNumber": "*********",
        "accountType": "checking",
        "linkedinUrl": "https://linkedin.com/in/johndoe",
        "twitterUrl": "https://twitter.com/johndoe",
        "facebookUrl": "https://facebook.com/johndoe",
        "instagramUrl": "https://instagram.com/johndoe",
        "createdAt": "2025-01-03T...",
        "updatedAt": "2025-01-03T..."
    }
}
```

## Key Features

1. **Nested Structure Support**: The API now accepts nested objects like `departmentInfo`, `presentAddress`, etc.
2. **Backward Compatibility**: Legacy flat fields are still supported
3. **Priority System**: Nested fields take priority over flat fields when both are provided
4. **Comprehensive Data**: All employee information is stored and returned in a single record
5. **Flexible Updates**: You can update specific nested sections without affecting others
