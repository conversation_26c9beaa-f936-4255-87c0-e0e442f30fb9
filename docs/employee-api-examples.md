# Employee API Endpoints - Curl Examples

## Authentication

First, get an access token:

```bash
# Login to get access token
curl -X POST "http://localhost:4006/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "superadmin123",
    "ipAddress": "127.0.0.1"
  }'
```

## 1. Create Employee - Comprehensive Data

```bash
curl -X POST "http://localhost:4006/api/auth/employees" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "email": "<EMAIL>",
    "password": "TempPass123!",
    "firstName": "John",
    "lastName": "Doe",
    "phone": "******-0123",
    "dateOfBirth": "1990-05-15",
    "bloodGroup": "O+",
    "gender": "male",
    "nationality": "American",
    "maritalStatus": "married",
    "joiningDate": "2024-01-15",
    "jobType": "full-time",
    "jobStatus": "active",
    "employeeId": "EMP001",
    "department": "Engineering",
    "designation": "Senior Developer",
    "reportingTo": "<PERSON>",
    "workLocation": "New York Office",
    "presentAddress": "123 Main Street",
    "presentCountry": "USA",
    "presentState": "New York",
    "presentCity": "New York",
    "presentPostalCode": "10001",
    "permanentAddress": "456 Oak Avenue",
    "permanentCountry": "USA",
    "permanentState": "California",
    "permanentCity": "Los Angeles",
    "permanentPostalCode": "90210",
    "emergencyContactName": "Mary Doe",
    "emergencyContactRelation": "spouse",
    "emergencyContactPhone": "******-0456",
    "emergencyContactEmail": "<EMAIL>",
    "emergencyContactAddress": "123 Main Street, New York, NY 10001",
    "securityType": "SSN",
    "securityMaturity": "permanent",
    "securityBankCode": "*********",
    "securityExpiryDate": "2030-12-31",
    "accountHolderName": "John Doe",
    "accountNumber": "**********",
    "bankName": "Chase Bank",
    "branchName": "Manhattan Branch",
    "routingNumber": "*********",
    "accountType": "checking",
    "linkedinUrl": "https://linkedin.com/in/johndoe",
    "twitterUrl": "https://twitter.com/johndoe",
    "facebookUrl": "https://facebook.com/johndoe",
    "instagramUrl": "https://instagram.com/johndoe"
  }'
```

## 2. Create Employee - Legacy Name Field

```bash
curl -X POST "http://localhost:4006/api/auth/employees" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "email": "<EMAIL>",
    "password": "TempPass456!",
    "name": "Sarah Wilson",
    "phone": "******-7890",
    "dateOfBirth": "1992-08-20",
    "bloodGroup": "A+",
    "gender": "female",
    "joiningDate": "2024-03-01",
    "jobType": "part-time",
    "jobStatus": "active",
    "department": "Marketing",
    "designation": "Marketing Specialist"
  }'
```

## 3. Create Employee - Minimal Required Fields

```bash
curl -X POST "http://localhost:4006/api/auth/employees" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "email": "<EMAIL>",
    "password": "TempPass789!",
    "firstName": "Mike",
    "lastName": "Brown"
  }'
```

## 4. Update Employee - Comprehensive Data

```bash
curl -X PUT "http://localhost:4006/api/auth/employees/1" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "firstName": "John",
    "lastName": "Smith",
    "email": "<EMAIL>",
    "phone": "******-0123",
    "dateOfBirth": "1990-05-15",
    "bloodGroup": "O+",
    "gender": "male",
    "nationality": "American",
    "maritalStatus": "married",
    "joiningDate": "2024-01-15",
    "jobType": "full-time",
    "jobStatus": "active",
    "employeeId": "EMP001",
    "department": "Engineering",
    "designation": "Senior Developer",
    "reportingTo": "Jane Doe",
    "workLocation": "New York Office",
    "presentAddress": "123 Main Street",
    "presentCountry": "USA",
    "presentState": "New York",
    "presentCity": "New York",
    "presentPostalCode": "10001",
    "permanentAddress": "456 Oak Avenue",
    "permanentCountry": "USA",
    "permanentState": "California",
    "permanentCity": "Los Angeles",
    "permanentPostalCode": "90210",
    "emergencyContactName": "Mary Smith",
    "emergencyContactRelation": "spouse",
    "emergencyContactPhone": "******-0456",
    "emergencyContactEmail": "<EMAIL>",
    "emergencyContactAddress": "123 Main Street, New York, NY 10001",
    "securityType": "SSN",
    "securityMaturity": "permanent",
    "securityBankCode": "*********",
    "securityExpiryDate": "2030-12-31",
    "accountHolderName": "John Smith",
    "accountNumber": "**********",
    "bankName": "Chase Bank",
    "branchName": "Manhattan Branch",
    "routingNumber": "*********",
    "accountType": "checking",
    "linkedinUrl": "https://linkedin.com/in/johnsmith",
    "twitterUrl": "https://twitter.com/johnsmith",
    "facebookUrl": "https://facebook.com/johnsmith",
    "instagramUrl": "https://instagram.com/johnsmith"
  }'
```

## 5. Update Employee - Legacy Name Field

```bash
curl -X PUT "http://localhost:4006/api/auth/employees/1" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "name": "Alice Johnson",
    "phone": "******-9999",
    "jobStatus": "inactive",
    "department": "Marketing"
  }'
```

## 6. Update Employee - Partial Update (Only Changed Fields)

```bash
curl -X PUT "http://localhost:4006/api/auth/employees/1" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "jobStatus": "active",
    "designation": "Lead Developer",
    "workLocation": "Remote"
  }'
```

## 7. Update Employee - Address Information Only

```bash
curl -X PUT "http://localhost:4006/api/auth/employees/1" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "presentAddress": "789 Broadway",
    "presentCity": "New York",
    "presentState": "New York",
    "presentPostalCode": "10003",
    "permanentAddress": "321 Sunset Blvd",
    "permanentCity": "Los Angeles",
    "permanentState": "California",
    "permanentPostalCode": "90028"
  }'
```

## 8. Update Employee - Emergency Contact Only

```bash
curl -X PUT "http://localhost:4006/api/auth/employees/1" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "emergencyContactName": "Robert Johnson",
    "emergencyContactRelation": "brother",
    "emergencyContactPhone": "******-1111",
    "emergencyContactEmail": "<EMAIL>",
    "emergencyContactAddress": "555 Pine Street, Seattle, WA 98101"
  }'
```

## 9. Update Employee - Bank Account Information

```bash
curl -X PUT "http://localhost:4006/api/auth/employees/1" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "accountHolderName": "John A. Smith",
    "accountNumber": "**********",
    "bankName": "Bank of America",
    "branchName": "Times Square Branch",
    "routingNumber": "*********",
    "accountType": "savings"
  }'
```

## 10. Update Employee - Social Media Profiles

```bash
curl -X PUT "http://localhost:4006/api/auth/employees/1" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "linkedinUrl": "https://linkedin.com/in/johnsmith-dev",
    "twitterUrl": "https://twitter.com/johnsmith_dev",
    "facebookUrl": "https://facebook.com/john.smith.dev",
    "instagramUrl": "https://instagram.com/johnsmith_developer"
  }'
```

## 11. Get Employee Details

```bash
curl -X GET "http://localhost:4006/api/auth/employees/1" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## 12. List All Employees

```bash
curl -X GET "http://localhost:4006/api/auth/employees" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## 13. Test Validation - Invalid Job Status

```bash
curl -X PUT "http://localhost:4006/api/auth/employees/1" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "jobStatus": "invalid_status"
  }'
```

## 14. Test Validation - Invalid Date Format

```bash
curl -X PUT "http://localhost:4006/api/auth/employees/1" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "dateOfBirth": "invalid-date",
    "joiningDate": "2024-13-45"
  }'
```

## 15. Test Edge Case - Single Name

```bash
curl -X PUT "http://localhost:4006/api/auth/employees/1" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "name": "Madonna"
  }'
```

## 16. Test Edge Case - Multiple Names

```bash
curl -X PUT "http://localhost:4006/api/auth/employees/1" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "name": "Jean-Claude Van Damme Jr."
  }'
```

## Field Reference

### Personal Information Fields

- `firstName`, `lastName`, `email`, `phone`
- `dateOfBirth`, `bloodGroup`, `gender`, `nationality`, `maritalStatus`

### Employment Information Fields

- `joiningDate`, `jobType`, `jobStatus`, `employeeId`
- `department`, `designation`, `reportingTo`, `workLocation`

### Address Information Fields

- `presentAddress`, `presentCountry`, `presentState`, `presentCity`, `presentPostalCode`
- `permanentAddress`, `permanentCountry`, `permanentState`, `permanentCity`, `permanentPostalCode`

### Emergency Contact Fields

- `emergencyContactName`, `emergencyContactRelation`, `emergencyContactPhone`
- `emergencyContactEmail`, `emergencyContactAddress`

### Security Information Fields

- `securityType`, `securityMaturity`, `securityBankCode`, `securityExpiryDate`

### Bank Account Information Fields

- `accountHolderName`, `accountNumber`, `bankName`, `branchName`
- `routingNumber`, `accountType`

### Social Profile Fields

- `linkedinUrl`, `twitterUrl`, `facebookUrl`, `instagramUrl`

### Legacy Fields (for backward compatibility)

- `name` (automatically split into firstName/lastName)
- `phone` (ignored in current implementation)
- `agencyId`, `orgId`, `addresses`, `emergencyContacts`, `identityDocs`, `bankAccounts`

## Valid Values

### Job Types

- `"full-time"`, `"part-time"`, `"contract"`, `"intern"`

### Job Statuses

- `"active"`, `"inactive"`, `"terminated"`, `"on-leave"`

### Blood Groups

- `"A+"`, `"A-"`, `"B+"`, `"B-"`, `"AB+"`, `"AB-"`, `"O+"`, `"O-"`

### Gender

- `"male"`, `"female"`, `"other"`

### Marital Status

- `"single"`, `"married"`, `"divorced"`, `"widowed"`

### Account Types

- `"checking"`, `"savings"`

## Response Format

### Success Response

```json
{
  "success": true,
  "message": "Employee created/updated successfully",
  "employee": {
    "userId": 1,
    "firstName": "John",
    "lastName": "Smith",
    "joiningDate": "2024-01-15T00:00:00.000Z",
    "jobType": "full-time",
    "jobStatus": "active",
    "dateOfBirth": "1990-05-15T00:00:00.000Z",
    "bloodGroup": "O+",
    "gender": "male",
    "createdAt": "Thu Jul 03 2025 09:23:52 GMT+0000 (Coordinated Universal Time)",
    "updatedAt": "Thu Jul 03 2025 09:23:52 GMT+0000 (Coordinated Universal Time)"
  }
}
```

### Error Response

```json
{
  "success": false,
  "message": "Invalid job status. Must be one of: active, inactive, terminated, on-leave"
}
```
