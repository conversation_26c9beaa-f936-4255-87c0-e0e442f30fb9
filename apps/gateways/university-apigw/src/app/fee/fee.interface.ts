import { Observable } from 'rxjs';

// ---------- Nested Entities ----------
export interface IntakeFee {
  id: string;
  name: string;
  startDate: string;
  endDate: string;
  createdAt: string;
}

export interface ProgramLevelIntakeFee {
  id: string;
  feeTitle: string;
  tuitionFee: number;
  applicationFee: number;
  applicationFeeChargedByUniversity: number;
  applicationFeeChargedToStudent: number;
  paymentDueInDays: number;
  feeEffectiveDate: string;
  applicationYearStart: string;
  applicationYearEnd: string;
  isRefundableToStudent: boolean;
  isVisibleToStudent: boolean;
  isActive: boolean;
}

export interface ProgramLevelIntake {
  id: string;
  universityId: string;
  programLevelId: string;
  intake: IntakeFee;
  fees: ProgramLevelIntakeFee[];
}

export interface ProgramLevelFeeInfo {
  id: string;
  programLevelName: string;
  durationNumber: number;
  durationType: string;
  createdAt: string;
  programLevelIntakes: ProgramLevelIntake[]; // Updated field
}

// ---------- Request DTOs ----------
export interface CreateProgramLevelIntakeFeeRequest {
  universityId: number;
  userId?: string;
  userRole?: string;
  feeTitle: string;
  programLevelId: number;
  intakeIds: number[];
  applicationYearStart: string;
  applicationYearEnd: string;
  tuitionFee: number;
  applicationFee: number;
  applicationFeeChargedByUniversity: number;
  applicationFeeChargedToStudent: number;
  paymentDueInDays: number;
  feeEffectiveDate: string;
  isActive: boolean;
  isRefundableToStudent: boolean;
  isVisibleToStudent: boolean;
}

export interface UpdateProgramLevelIntakeFeeRequest {
  id: string;
  universityId: number;
  userId?: string;
  userRole?: string;
  feeTitle: string;
  programLevelId: number;
  intakeId: number;
  applicationYearStart: string;
  applicationYearEnd: string;
  tuitionFee: number;
  applicationFee: number;
  applicationFeeChargedByUniversity: number;
  applicationFeeChargedToStudent: number;
  paymentDueInDays: number;
  feeEffectiveDate: string;
  isActive: boolean;
  isRefundableToStudent: boolean;
  isVisibleToStudent: boolean;
}

export interface GetProgramLevelIntakeFeeRequest {
  id: string;
}

export interface ListProgramLevelIntakeFeeByUniversityRequest {
  universityId: number;
  page: number;
  pageSize: number;
}

export interface DeleteProgramLevelIntakeFeeRequest {
  id: string;
  userId?: string;
}

// ---------- Response DTOs ----------
export interface ErrorResponse {
  validationErrors?: string[];
  code?: string;
  message: string;
  details?: string;
}

export interface ProgramLevelIntakeFeeResponse {
  status: number;
  message: string;
  data: ProgramLevelIntakeFee;
  error: ErrorResponse | null;
}

export interface ProgramLevelIntakeFeeListResponse {
  status: number;
  message: string;
  data: ProgramLevelFeeInfo[];
  error: ErrorResponse | null;
}

export interface DeleteProgramLevelIntakeFeeResponse {
  status: number;
  message: string;
  success: boolean;
  error: ErrorResponse | null;
}

// ---------- gRPC Service Interface ----------
export interface ProgramLevelIntakeFeeGrpcService {
  createProgramLevelIntakeFee(data: CreateProgramLevelIntakeFeeRequest): Observable<ProgramLevelIntakeFeeListResponse>;
  updateProgramLevelIntakeFee(data: UpdateProgramLevelIntakeFeeRequest): Observable<ProgramLevelIntakeFeeResponse>;
  getProgramLevelIntakeFee(data: GetProgramLevelIntakeFeeRequest): Observable<ProgramLevelIntakeFeeResponse>;
  listProgramLevelIntakeFees(data: ListProgramLevelIntakeFeeByUniversityRequest): Observable<ProgramLevelIntakeFeeListResponse>;
  deleteProgramLevelIntakeFee(data: DeleteProgramLevelIntakeFeeRequest): Observable<DeleteProgramLevelIntakeFeeResponse>;
}
