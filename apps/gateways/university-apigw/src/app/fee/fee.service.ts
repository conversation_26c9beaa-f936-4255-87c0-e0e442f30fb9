import { Injectable, Logger } from '@nestjs/common';
import { Observable } from 'rxjs';
import {
  CreateProgramLevelIntakeFeeRequest,
  UpdateProgramLevelIntakeFeeRequest,
  GetProgramLevelIntakeFeeRequest,
  DeleteProgramLevelIntakeFeeRequest,
  ListProgramLevelIntakeFeeByUniversityRequest,
  ProgramLevelIntakeFeeResponse,
  ProgramLevelIntakeFeeListResponse,
  DeleteProgramLevelIntakeFeeResponse,
  ProgramLevelIntakeFeeGrpcService,
} from './fee.interface';
import { UniversityClientService } from '../app.service';

@Injectable()
export class FeeService {
  private readonly logger = new Logger(FeeService.name);

  constructor(private readonly universityClient: UniversityClientService) {}

  private get grpcService(): ProgramLevelIntakeFeeGrpcService {
    return this.universityClient.getService<ProgramLevelIntakeFeeGrpcService>('UniversityService');
  }

  createFee(data: CreateProgramLevelIntakeFeeRequest): Observable<ProgramLevelIntakeFeeListResponse> {
    this.logger.log(`Creating fee entry for university ID: ${data.universityId}`);
    return this.grpcService.createProgramLevelIntakeFee(data);
  }

  updateFee(data: UpdateProgramLevelIntakeFeeRequest): Observable<ProgramLevelIntakeFeeResponse> {
    this.logger.log(`Updating fee with ID: ${data.id}`);
    return this.grpcService.updateProgramLevelIntakeFee(data);
  }

  getFee(data: GetProgramLevelIntakeFeeRequest): Observable<ProgramLevelIntakeFeeResponse> {
    this.logger.log(`Fetching fee with ID: ${data.id}`);
    return this.grpcService.getProgramLevelIntakeFee(data);
  }

  listFees(data: ListProgramLevelIntakeFeeByUniversityRequest): Observable<ProgramLevelIntakeFeeListResponse> {
    this.logger.log(`Listing fees for university ID: ${data.universityId}`);
    return this.grpcService.listProgramLevelIntakeFees(data);
  }

  deleteFee(data: DeleteProgramLevelIntakeFeeRequest): Observable<DeleteProgramLevelIntakeFeeResponse> {
    this.logger.log(`Deleting fee with ID: ${data.id}`);
    return this.grpcService.deleteProgramLevelIntakeFee(data);
  }
}
