import { Observable } from 'rxjs';

export interface Timestamp {
  seconds: number;
  nanos: number;
}

export interface CreateUniversityRequest {
  userId?: string; // Updated to string to match proto
  userRole?:string;
  name: string;
  about: string;
  type: string;
  website: string;
  foundedOn: Timestamp | string; // Accept both for flexibility
  dliNumber: string;
  address: string;
  countryId: number;
  state: string;
  city: string;
  postalCode: string;
  logo?: string; // ✅ NEW
}

export interface GetUniversityRequest {
  id: number;
}

export interface UpdateUniversityRequest {
  id: number;
  name?: string;
  about?: string;
  type?: string;
  website?: string;
  foundedOn?: Timestamp | string;
  dliNumber?: string;
  address?: string;
  countryId?: number;
  state?: string;
  city?: string;
  postalCode?: string;
  logo?: string; // ✅ NEW
  userId?: string; // ✅ NEW
  userRole?: string; // ✅ NEW
}

export interface DeleteUniversityRequest {
  id: number;
  userId?: string; // ✅ NEW
}

export interface UniversityResponse {
  id: number;
  name: string;
  about: string;
  type: string;
  website: string;
  foundedOn: string;
  dliNumber: string;
  address: string;
  countryId: number;
  state: string;
  city: string;
  postalCode: string;
  logo: string; // ✅ NEW
  createdAt: string;
  updatedAt: string;
}

export interface ListUniversitiesRequest {
  page?: number;
  limit?: number;
  isActive?: boolean;
}

export interface ListUniversitiesResponse {
  status: number;
  message: string;
  data: {
    total: number;
    page: number;
    limit: number;
    universities: UniversityResponse[];
  };
  error: any;
}

export interface UniversityGrpcService {
  createUniversity(data: CreateUniversityRequest): Observable<UniversityResponse>;
  getUniversity(data: GetUniversityRequest): Observable<UniversityResponse>;
  updateUniversity(data: UpdateUniversityRequest): Observable<UniversityResponse>;
  deleteUniversity(data: DeleteUniversityRequest): Observable<{ success: boolean }>;
  listUniversities(data: ListUniversitiesRequest): Observable<ListUniversitiesResponse>;
}
