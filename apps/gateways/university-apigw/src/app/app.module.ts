import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AppController } from './app.controller';
import { UniversityClientService } from './app.service';
import { UniversityModule } from './university/university.module';
import { CountryModule } from './country/country.module';
import { IntakeModule } from './intake/intake.module';
import { ProgramLevelModule } from './program-lavel/program.module';
import { FieldsOfStudyModule } from './field-of-study/field-of-study.module';
import { CommissionModule } from './commission/commission.module';
import { PaymentDetailsModule } from './payment/payment.module';
import { UniversityContactModule } from './university-contact/university-contact.module';
import { UniversityGeneralModule } from './general/university-general.module';
import { FeeModule } from './fee/fee.module';
import { CourseModule } from './course/course.module';
import { CampusModule } from './campus/campus.module';
import { AlumniModule } from './alumni/alumni.module';
import { DummyModule } from './dummy/dummy.module';
import { AuthenticationModule } from '@apply-goal-backend/auth';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    AuthenticationModule.forRoot({
      secret: process.env.JWT_SECRET || 'secret',
      expiresIn: '1h',
      refreshExpiresIn: '7d',
    }),
    UniversityModule,
    CountryModule,
    IntakeModule,
    ProgramLevelModule,
    CommissionModule,
    FieldsOfStudyModule,
    PaymentDetailsModule,
    UniversityContactModule,
    UniversityGeneralModule,
    FeeModule,
    CourseModule,
    CampusModule,
    AlumniModule,
    DummyModule

  ],
  controllers: [AppController],
  providers: [UniversityClientService],
  exports: [UniversityClientService],
})
export class AppModule {}
