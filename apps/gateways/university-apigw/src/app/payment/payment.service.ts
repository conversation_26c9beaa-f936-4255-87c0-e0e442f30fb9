import { Injectable, Logger } from '@nestjs/common';
import {
  PaymentDetailsGrpcService,
  CreateBankDetailsRequest,
  UpdateBankDetailsRequest,
  DeleteBankDetailsRequest,
  GetBankDetailsRequest,
  ListBankDetailsRequest,
  CreateBankDetailsResponse,
  UpdateBankDetailsResponse,
  DeleteBankDetailsResponse,
  GetBankDetailsResponse,
  ListBankDetailsResponse,
} from './payment.interface';
import { Observable } from 'rxjs';
import { UniversityClientService } from '../app.service';

@Injectable()
export class PaymentDetailsService {
  private readonly logger = new Logger(PaymentDetailsService.name);

  constructor(private readonly universityClient: UniversityClientService) {}

  private get grpcService(): PaymentDetailsGrpcService {
    return this.universityClient.getService<PaymentDetailsGrpcService>('UniversityService');
  }

  create(data: CreateBankDetailsRequest): Observable<CreateBankDetailsResponse> {
    this.logger.log(`Creating bank details for accountName: ${data.accountName}`);
    return this.grpcService.createBankDetails(data);
  }

  update(data: UpdateBankDetailsRequest): Observable<UpdateBankDetailsResponse> {
    this.logger.log(`Updating bank details ID: ${data.id}`);
    return this.grpcService.updateBankDetails(data);
  }

  get(data: GetBankDetailsRequest): Observable<GetBankDetailsResponse> {
    this.logger.log(`Fetching bank details ID: ${data.universityId}`);
    return this.grpcService.getBankDetails({ universityId: data.universityId });
  }

  delete(data: DeleteBankDetailsRequest): Observable<DeleteBankDetailsResponse> {
    this.logger.log(`Deleting bank details ID: ${data.id}`);
    return this.grpcService.deleteBankDetails({ id: data.id });
  }

  list(data: ListBankDetailsRequest): Observable<ListBankDetailsResponse> {
    this.logger.log(`Listing bank details universityId:${data.universityId} page: ${data.page}, pageSize: ${data.pageSize}`);
    return this.grpcService.listBankDetails(data);
  }
}