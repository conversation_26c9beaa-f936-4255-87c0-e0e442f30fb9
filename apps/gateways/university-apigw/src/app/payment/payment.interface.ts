import { Observable } from 'rxjs';

// ---------- Entity ----------

export interface BankDetails {
  id: string;
  universityId: string; // <-- Added
  bankName: string;
  accountName: string;
  accountHolderName: string;
  ibanSwiftCode: string;
  taxId: string;
  vatNumber: string;
  createdAt: string;
  updatedAt: string;
}

// ---------- Request DTOs ----------

export interface CreateBankDetailsRequest {
  universityId: string; // <-- Added
  userId?: string;
  userRole?: string;
  bankName: string;
  accountName: string;
  accountHolderName: string;
  ibanSwiftCode: string;
  taxId: string;
  vatNumber: string;
}

export interface GetBankDetailsRequest {
  universityId: string;
}

export interface UpdateBankDetailsRequest {
  id: string;
  universityId: string; // <-- Added
  bankName: string;
  accountName: string;
  accountHolderName: string;
  ibanSwiftCode: string;
  taxId: string;
  vatNumber: string;
}

export interface DeleteBankDetailsRequest {
  id: string;
}

export interface ListBankDetailsRequest {
  universityId:number;
  page: number;
  pageSize: number;
}

// ---------- Response DTOs ----------

export interface ErrorResponse {
  validationErrors?: any[];
  code?: string;
  message: string;
  details?: string;
}

export interface CreateBankDetailsResponse {
  status: number;
  message: string;
  data: BankDetails;
  error: ErrorResponse | null;
}

export interface GetBankDetailsResponse {
  status: number;
  message: string;
  data: BankDetails;
  error: ErrorResponse | null;
}

export interface UpdateBankDetailsResponse {
  status: number;
  message: string;
  data: BankDetails;
  error: ErrorResponse | null;
}

export interface DeleteBankDetailsResponse {
  status: number;
  message: string;
  success: boolean;
  error: ErrorResponse | null;
}

export interface ListBankDetailsResponse {
  status: number;
  message: string;
  data: BankDetails[];
  error: ErrorResponse | null;
}

// ---------- gRPC Service Interface ----------

export interface PaymentDetailsGrpcService {
  createBankDetails(data: CreateBankDetailsRequest): Observable<CreateBankDetailsResponse>;
  getBankDetails(data: GetBankDetailsRequest): Observable<GetBankDetailsResponse>;
  updateBankDetails(data: UpdateBankDetailsRequest): Observable<UpdateBankDetailsResponse>;
  deleteBankDetails(data: DeleteBankDetailsRequest): Observable<DeleteBankDetailsResponse>;
  listBankDetails(data: ListBankDetailsRequest): Observable<ListBankDetailsResponse>;
}
