import { Module } from '@nestjs/common';
import { PaymentDetailsController } from './payment.controller';
import { PaymentDetailsService } from './payment.service';
import { UniversityClientService } from '../app.service';

@Module({
  controllers: [PaymentDetailsController],
  providers: [PaymentDetailsService, UniversityClientService],
  exports: [PaymentDetailsService],
})
export class PaymentDetailsModule {}
