import { Observable } from 'rxjs';

export interface Alumni {
  id?: number;
  universityId: number;
  name: string;
  organizationName: string;
  designation: string;
  imageUrl?: string;
  isActive?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

// Individual alumni item used in bulk creation
export interface CreateAlumni {
  userId: string;
  universityId: number;
  name: string;
  organizationName: string;
  designation: string;
  imageUrl?: string;
}

// Bulk create request
export interface CreateAlumniRequest {
  userId: string;
  userRole: string;
  data: CreateAlumni[];
}

// Still support single alumni update using partial fields
export interface UpdateAlumniRequest extends Partial<CreateAlumni> {
  id: number;
  universityId: number;
  userId: string;
  userRole: string;
}

export interface GetAlumniRequest {
  id: number;
}

export interface DeleteAlumniRequest {
  id: number;
  userId: string;
}

export interface ListAlumniRequest {
  universityId: number;
  page?: number;
  pageSize?: number;
}

export interface ErrorResponse {
  code?: string;
  message?: string;
  details?: string;
}

export interface AlumniResponse {
  status: number;
  message: string;
  data: Alumni;
  error?: ErrorResponse;
}

export interface AlumniListResponse {
  status: number;
  message: string;
  data: Alumni[];
  count: number;
  error?: ErrorResponse;
}

export interface DeleteAlumniResponse {
  status: number;
  message: string;
  success: boolean;
  error?: ErrorResponse;
}

export interface AlumniGrpcService {
  CreateAlumni(data: CreateAlumniRequest): Observable<AlumniResponse>;
  UpdateAlumni(data: UpdateAlumniRequest): Observable<AlumniResponse>;
  GetAlumni(data: GetAlumniRequest): Observable<AlumniResponse>;
  DeleteAlumni(data: DeleteAlumniRequest): Observable<DeleteAlumniResponse>;
  GetAlumniList(data: ListAlumniRequest): Observable<AlumniListResponse>;
}
