import { Injectable, Logger } from '@nestjs/common';
import { Observable } from 'rxjs';
import {
  CreateAlumniRequest,
  UpdateAlumniRequest,
  GetAlumniRequest,
  DeleteAlumniRequest,
  ListAlumniRequest,
  AlumniResponse,
  AlumniListResponse,
  DeleteAlumniResponse,
  AlumniGrpcService,
} from './alumni.interface';
import { UniversityClientService } from '../app.service';

@Injectable()
export class AlumniService {
  private readonly logger = new Logger(AlumniService.name);

  constructor(private readonly universityClient: UniversityClientService) {}

  private get grpcService(): AlumniGrpcService {
    return this.universityClient.getService<AlumniGrpcService>('UniversityService');
  }

  createAlumni(data: CreateAlumniRequest): Observable<AlumniResponse> {
    this.logger.log(`Creating alumni: ${data.data?.length}`);
    return this.grpcService.CreateAlumni(data);
  }

  update<PERSON>lumni(data: UpdateAlumniRequest): Observable<AlumniResponse> {
    this.logger.log(`Updating alumni with ID: ${data.id}`);
    return this.grpcService.UpdateAlumni(data);
  }

  getAlumni(data: GetAlumniRequest): Observable<AlumniResponse> {
    this.logger.log(`Fetching alumni with ID: ${data.id}`);
    return this.grpcService.GetAlumni(data);
  }

  listAlumni(data: ListAlumniRequest): Observable<AlumniListResponse> {
    this.logger.log(`Listing alumni for university ID: ${data.universityId}`);
    return this.grpcService.GetAlumniList(data);
  }

  deleteAlumni(data: DeleteAlumniRequest): Observable<DeleteAlumniResponse> {
    this.logger.log(`Deleting alumni with ID: ${data.id}`);
    return this.grpcService.DeleteAlumni(data);
  }
}
