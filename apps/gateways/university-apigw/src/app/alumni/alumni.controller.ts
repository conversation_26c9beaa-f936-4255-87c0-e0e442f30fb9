import {
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Body,
  Query,
  Param,
  HttpStatus,
  HttpException,
  Logger,
} from '@nestjs/common';
import { firstValueFrom } from 'rxjs';
import {
  CreateAlumniRequest,
  UpdateAlumniRequest,
  ListAlumniRequest,
} from './alumni.interface';
import { AlumniService } from './alumni.service';
import { Permissions, Public } from '@apply-goal-backend/auth';
import { UserPermissions } from '@apply-goal-backend/common';
import { CurrentUser } from '@apply-goal-backend/auth';

@Controller('alumni')
export class AlumniController {
  private readonly logger = new Logger(AlumniController.name);

  constructor(private readonly alumniService: AlumniService) {}
  @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Post()
  async create(
    @Body() payload: CreateAlumniRequest,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[]
  ) {
    try {
      // Add userId and userRole
      payload.userId = String(userId);
      payload.userRole = roles[0];
      
      return await firstValueFrom(this.alumniService.createAlumni(payload));
    } catch (error) {
      this.logger.error('Create alumni failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Put(':id')
  async update(
    @Param('id') id: string, 
    @Body() payload: UpdateAlumniRequest,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[]
  ) {
    try {
      // Add userId and userRole
      payload.userId = String(userId);
      payload.userRole = roles[0];
      
      return await firstValueFrom(
        this.alumniService.updateAlumni({ ...payload, id: Number(id) })
      );
    } catch (error) {
      this.logger.error('Update alumni failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Get(':id')
  async get(@Param('id') id: string) {
    try {
      return await firstValueFrom(
        this.alumniService.getAlumni({ id: Number(id) })
      );
    } catch (error) {
      this.logger.error('Get alumni failed', error.stack);
      throw new HttpException(error.message, HttpStatus.NOT_FOUND);
    }
  }
  @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Get()
  async list(@Query() query: ListAlumniRequest) {
    try {
      const page = Number(query.page) > 0 ? Number(query.page) : 1;
      const pageSize = Number(query.pageSize) > 0 ? Number(query.pageSize) : 10;

      const response = await firstValueFrom(
        this.alumniService.listAlumni({
          universityId: Number(query.universityId),
          page,
          pageSize,
        })
      );
      return response;
    } catch (error) {
      this.logger.error('List alumni failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Delete(':id')
  async delete(
    @Param('id') id: string,
    @CurrentUser('id') userId: number
  ) {
    try {
      return await firstValueFrom(
        this.alumniService.deleteAlumni({ id: Number(id), userId: String(userId) })
      );
    } catch (error) {
      this.logger.error('Delete alumni failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
