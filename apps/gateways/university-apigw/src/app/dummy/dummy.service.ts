import { Injectable, Logger } from '@nestjs/common';
import { Observable } from 'rxjs';
import { UniversityClientService } from '../app.service';
import {
  DummyGrpcService,
  CreateDummyTestScoreRequest,
  CreateDummyTestScoreResponse,
  DeleteDummyTestScoreRequest,
  DeleteDummyTestScoreResponse,
  ListDummyTestScoresRequest,
  ListDummyTestScoresResponse,
  CreateDummyApplicationStepRequest,
  CreateDummyApplicationStepResponse,
  DeleteDummyApplicationStepRequest,
  DeleteDummyApplicationStepResponse,
  ListDummyApplicationStepsRequest,
  ListDummyApplicationStepsResponse,
  CreateDummyLectureLanguageRequest,
  CreateDummyLectureLanguageResponse,
  DeleteDummyLectureLanguageRequest,
  DeleteDummyLectureLanguageResponse,
  ListDummyLectureLanguagesRequest,
  ListDummyLectureLanguagesResponse,
  CreateDummySocialPlatformRequest,
  CreateDummySocialPlatformResponse,
  DeleteDummySocialPlatformRequest,
  DeleteDummySocialPlatformResponse,
  ListDummySocialPlatformsRequest,
  ListDummySocialPlatformsResponse,
} from './dummy.interface';

@Injectable()
export class DummyService {
  private readonly logger = new Logger(DummyService.name);

  constructor(private readonly universityClient: UniversityClientService) {}

  private get grpcService(): DummyGrpcService {
    return this.universityClient.getService<DummyGrpcService>('UniversityService');
  }

  // ===== Test Score Methods =====
  createTestScore(data: CreateDummyTestScoreRequest): Observable<CreateDummyTestScoreResponse> {
    this.logger.log(`Creating test score: ${data.title}`);
    return this.grpcService.createTestScore(data);
  }

  deleteTestScore(data: DeleteDummyTestScoreRequest): Observable<DeleteDummyTestScoreResponse> {
    this.logger.log(`Deleting test score with ID: ${data.id}`);
    return this.grpcService.deleteTestScore(data);
  }

  listTestScores(data: ListDummyTestScoresRequest): Observable<ListDummyTestScoresResponse> {
    this.logger.log('Listing test scores');
    return this.grpcService.listTestScores(data);
  }

  // ===== Application Step Methods =====
  createApplicationStep(data: CreateDummyApplicationStepRequest): Observable<CreateDummyApplicationStepResponse> {
    this.logger.log(`Creating application step: ${data.title}`);
    return this.grpcService.createApplicationStep(data);
  }

  deleteApplicationStep(data: DeleteDummyApplicationStepRequest): Observable<DeleteDummyApplicationStepResponse> {
    this.logger.log(`Deleting application step with ID: ${data.id}`);
    return this.grpcService.deleteApplicationStep(data);
  }

  listApplicationSteps(data: ListDummyApplicationStepsRequest): Observable<ListDummyApplicationStepsResponse> {
    this.logger.log('Listing application steps');
    return this.grpcService.listApplicationSteps(data);
  }

  // ===== Lecture Language Methods =====
  createLectureLanguage(data: CreateDummyLectureLanguageRequest): Observable<CreateDummyLectureLanguageResponse> {
    this.logger.log(`Creating lecture language with ${data.languages.length} languages`);
    return this.grpcService.createLectureLanguage(data);
  }

  deleteLectureLanguage(data: DeleteDummyLectureLanguageRequest): Observable<DeleteDummyLectureLanguageResponse> {
    this.logger.log(`Deleting lecture language with ID: ${data.id}`);
    return this.grpcService.deleteLectureLanguage(data);
  }

  listLectureLanguages(data: ListDummyLectureLanguagesRequest): Observable<ListDummyLectureLanguagesResponse> {
    this.logger.log('Listing lecture languages');
    return this.grpcService.listLectureLanguages(data);
  }

  // ===== Social Platform Methods =====
  createSocialPlatform(data: CreateDummySocialPlatformRequest): Observable<CreateDummySocialPlatformResponse> {
    this.logger.log(`Creating social platform with ${data.platforms.length} platforms`);
    return this.grpcService.createSocialPlatform(data);
  }

  deleteSocialPlatform(data: DeleteDummySocialPlatformRequest): Observable<DeleteDummySocialPlatformResponse> {
    this.logger.log(`Deleting social platform with ID: ${data.id}`);
    return this.grpcService.deleteSocialPlatform(data);
  }

  listSocialPlatforms(data: ListDummySocialPlatformsRequest): Observable<ListDummySocialPlatformsResponse> {
    this.logger.log('Listing social platforms');
    return this.grpcService.listSocialPlatforms(data);
  }
}
