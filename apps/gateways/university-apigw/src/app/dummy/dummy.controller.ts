import {
  Controller,
  Post,
  Get,
  Delete,
  Body,
  Query,
  Param,
  HttpStatus,
  HttpException,
  Logger,
} from '@nestjs/common';
import { firstValueFrom } from 'rxjs';
import {
  CreateDummyTestScoreRequest,
  DeleteDummyTestScoreRequest,
  ListDummyTestScoresRequest,
  CreateDummyApplicationStepRequest,
  DeleteDummyApplicationStepRequest,
  ListDummyApplicationStepsRequest,
  CreateDummyLectureLanguageRequest,
  DeleteDummyLectureLanguageRequest,
  ListDummyLectureLanguagesRequest,
  CreateDummySocialPlatformRequest,
  DeleteDummySocialPlatformRequest,
  ListDummySocialPlatformsRequest,
} from './dummy.interface';
import { DummyService } from './dummy.service';
import { Permissions } from '@apply-goal-backend/auth';
import { UserPermissions } from '@apply-goal-backend/common';
import { CurrentUser } from '@apply-goal-backend/auth';

@Controller('superadmin/dummyconfig')
export class DummyController {
  private readonly logger = new Logger(DummyController.name);

  constructor(private readonly dummyService: DummyService) {}

  // ===== Test Score Routes =====
  // @Permissions(UserPermissions.SUPER_ADMIN)
  @Post('test_score')
  async createTestScore(
    @Body() payload: CreateDummyTestScoreRequest,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[]
  ) {
    try {
      payload.userId = String(userId);
      payload.userRole = roles[0];
      
      return await firstValueFrom(this.dummyService.createTestScore(payload));
    } catch (error) {
      this.logger.error('Create test score failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // @Permissions(UserPermissions.SUPER_ADMIN)
  @Delete('test_score/:id')
  async deleteTestScore(
    @Param('id') id: string,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[]
  ) {
    try {
      return await firstValueFrom(
        this.dummyService.deleteTestScore({
          id: Number(id),
          userId: String(userId),
          userRole: roles[0],
        })
      );
    } catch (error) {
      this.logger.error('Delete test score failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // @Permissions(UserPermissions.SUPER_ADMIN)
  @Get('test_score')
  async listTestScores(@Query() query: ListDummyTestScoresRequest) {
    try {
      const page = Number(query.page) > 0 ? Number(query.page) : 1;
      const limit = Number(query.limit) > 0 ? Number(query.limit) : 10;

      return await firstValueFrom(
        this.dummyService.listTestScores({
          page,
          limit,
          search: query.search,
          includeInactive: query.includeInactive,
        })
      );
    } catch (error) {
      this.logger.error('List test scores failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // ===== Application Step Routes =====
  // @Permissions(UserPermissions.SUPER_ADMIN)
  @Post('application_step')
  async createApplicationStep(
    @Body() payload: CreateDummyApplicationStepRequest,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[]
  ) {
    try {
      payload.userId = String(userId);
      payload.userRole = roles[0];
      
      return await firstValueFrom(this.dummyService.createApplicationStep(payload));
    } catch (error) {
      this.logger.error('Create application step failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // @Permissions(UserPermissions.SUPER_ADMIN)
  @Delete('application_step/:id')
  async deleteApplicationStep(
    @Param('id') id: string,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[]
  ) {
    try {
      return await firstValueFrom(
        this.dummyService.deleteApplicationStep({
          id: Number(id),
          userId: String(userId),
          userRole: roles[0],
        })
      );
    } catch (error) {
      this.logger.error('Delete application step failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // @Permissions(UserPermissions.SUPER_ADMIN)
  @Get('application_step')
  async listApplicationSteps(@Query() query: ListDummyApplicationStepsRequest) {
    try {
      const page = Number(query.page) > 0 ? Number(query.page) : 1;
      const limit = Number(query.limit) > 0 ? Number(query.limit) : 10;

      return await firstValueFrom(
        this.dummyService.listApplicationSteps({
          page,
          limit,
          search: query.search,
          includeInactive: query.includeInactive,
        })
      );
    } catch (error) {
      this.logger.error('List application steps failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // ===== Lecture Language Routes =====
  // @Permissions(UserPermissions.SUPER_ADMIN)
  @Post('lecture_language')
  async createLectureLanguage(
    @Body() payload: CreateDummyLectureLanguageRequest,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[]
  ) {
    try {
      payload.userId = String(userId);
      payload.userRole = roles[0];

      return await firstValueFrom(this.dummyService.createLectureLanguage(payload));
    } catch (error) {
      this.logger.error('Create lecture language failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // @Permissions(UserPermissions.SUPER_ADMIN)
  @Delete('lecture_language/:id')
  async deleteLectureLanguage(
    @Param('id') id: string,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[]
  ) {
    try {
      return await firstValueFrom(
        this.dummyService.deleteLectureLanguage({
          id: Number(id),
          userId: String(userId),
          userRole: roles[0],
        })
      );
    } catch (error) {
      this.logger.error('Delete lecture language failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // @Permissions(UserPermissions.SUPER_ADMIN)
  @Get('lecture_language')
  async listLectureLanguages(@Query() query: ListDummyLectureLanguagesRequest) {
    try {
      const page = Number(query.page) > 0 ? Number(query.page) : 1;
      const limit = Number(query.limit) > 0 ? Number(query.limit) : 10;

      return await firstValueFrom(
        this.dummyService.listLectureLanguages({
          page,
          limit,
          includeInactive: query.includeInactive,
        })
      );
    } catch (error) {
      this.logger.error('List lecture languages failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // ===== Social Platform Routes =====
  // @Permissions(UserPermissions.SUPER_ADMIN)
  @Post('social_platform')
  async createSocialPlatform(
    @Body() payload: CreateDummySocialPlatformRequest,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[]
  ) {
    try {
      payload.userId = String(userId);
      payload.userRole = roles[0];

      return await firstValueFrom(this.dummyService.createSocialPlatform(payload));
    } catch (error) {
      this.logger.error('Create social platform failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // @Permissions(UserPermissions.SUPER_ADMIN)
  @Delete('social_platform/:id')
  async deleteSocialPlatform(
    @Param('id') id: string,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[]
  ) {
    try {
      return await firstValueFrom(
        this.dummyService.deleteSocialPlatform({
          id: Number(id),
          userId: String(userId),
          userRole: roles[0],
        })
      );
    } catch (error) {
      this.logger.error('Delete social platform failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // @Permissions(UserPermissions.SUPER_ADMIN)
  @Get('social_platform')
  async listSocialPlatforms(@Query() query: ListDummySocialPlatformsRequest) {
    try {
      const page = Number(query.page) > 0 ? Number(query.page) : 1;
      const limit = Number(query.limit) > 0 ? Number(query.limit) : 10;

      return await firstValueFrom(
        this.dummyService.listSocialPlatforms({
          page,
          limit,
          includeInactive: query.includeInactive,
        })
      );
    } catch (error) {
      this.logger.error('List social platforms failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
