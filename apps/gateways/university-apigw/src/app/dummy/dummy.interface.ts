import { Observable } from 'rxjs';

// ===== Test Score Interfaces =====
export interface DummyTestScore {
  id: number;
  title: string;
  modules: string[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateDummyTestScoreRequest {
  userId: string;
  userRole: string;
  title: string;
  modules: string[];
}

export interface CreateDummyTestScoreResponse {
  data: DummyTestScore;
  status: number;
  message: string;
  error?: any;
}

export interface DeleteDummyTestScoreRequest {
  id: number;
  userId: string;
  userRole: string;
}

export interface DeleteDummyTestScoreResponse {
  status: number;
  message: string;
  error?: any;
}

export interface ListDummyTestScoresRequest {
  page?: number;
  limit?: number;
  search?: string;
  includeInactive?: boolean;
}

export interface ListDummyTestScoresResponse {
  data: DummyTestScore[];
  total: number;
  status: number;
  message: string;
  error?: any;
}

// ===== Application Step Interfaces =====
export interface DummyApplicationStep {
  id: number;
  title: string;
  steps: string[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateDummyApplicationStepRequest {
  userId: string;
  userRole: string;
  title: string;
  steps: string[];
}

export interface CreateDummyApplicationStepResponse {
  data: DummyApplicationStep;
  status: number;
  message: string;
  error?: any;
}

export interface DeleteDummyApplicationStepRequest {
  id: number;
  userId: string;
  userRole: string;
}

export interface DeleteDummyApplicationStepResponse {
  status: number;
  message: string;
  error?: any;
}

export interface ListDummyApplicationStepsRequest {
  page?: number;
  limit?: number;
  search?: string;
  includeInactive?: boolean;
}

export interface ListDummyApplicationStepsResponse {
  data: DummyApplicationStep[];
  total: number;
  status: number;
  message: string;
  error?: any;
}

// ===== Lecture Language Interfaces =====
export interface DummyLectureLanguage {
  id: number;
  languages: string[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateDummyLectureLanguageRequest {
  userId: string;
  userRole: string;
  languages: string[];
}

export interface CreateDummyLectureLanguageResponse {
  data: DummyLectureLanguage;
  status: number;
  message: string;
  error?: any;
}

export interface DeleteDummyLectureLanguageRequest {
  id: number;
  userId: string;
  userRole: string;
}

export interface DeleteDummyLectureLanguageResponse {
  status: number;
  message: string;
  error?: any;
}

export interface ListDummyLectureLanguagesRequest {
  page?: number;
  limit?: number;
  includeInactive?: boolean;
}

export interface ListDummyLectureLanguagesResponse {
  data: DummyLectureLanguage[];
  total: number;
  status: number;
  message: string;
  error?: any;
}

// ===== Social Platform Interfaces =====
export interface DummySocialPlatform {
  id: number;
  platforms: string[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateDummySocialPlatformRequest {
  userId: string;
  userRole: string;
  platforms: string[];
}

export interface CreateDummySocialPlatformResponse {
  data: DummySocialPlatform;
  status: number;
  message: string;
  error?: any;
}

export interface DeleteDummySocialPlatformRequest {
  id: number;
  userId: string;
  userRole: string;
}

export interface DeleteDummySocialPlatformResponse {
  status: number;
  message: string;
  error?: any;
}

export interface ListDummySocialPlatformsRequest {
  page?: number;
  limit?: number;
  includeInactive?: boolean;
}

export interface ListDummySocialPlatformsResponse {
  data: DummySocialPlatform[];
  total: number;
  status: number;
  message: string;
  error?: any;
}

// ===== gRPC Service Interface =====
export interface DummyGrpcService {
  // Test Score methods
  createTestScore(data: CreateDummyTestScoreRequest): Observable<CreateDummyTestScoreResponse>;
  deleteTestScore(data: DeleteDummyTestScoreRequest): Observable<DeleteDummyTestScoreResponse>;
  listTestScores(data: ListDummyTestScoresRequest): Observable<ListDummyTestScoresResponse>;

  // Application Step methods
  createApplicationStep(data: CreateDummyApplicationStepRequest): Observable<CreateDummyApplicationStepResponse>;
  deleteApplicationStep(data: DeleteDummyApplicationStepRequest): Observable<DeleteDummyApplicationStepResponse>;
  listApplicationSteps(data: ListDummyApplicationStepsRequest): Observable<ListDummyApplicationStepsResponse>;

  // Lecture Language methods
  createLectureLanguage(data: CreateDummyLectureLanguageRequest): Observable<CreateDummyLectureLanguageResponse>;
  deleteLectureLanguage(data: DeleteDummyLectureLanguageRequest): Observable<DeleteDummyLectureLanguageResponse>;
  listLectureLanguages(data: ListDummyLectureLanguagesRequest): Observable<ListDummyLectureLanguagesResponse>;

  // Social Platform methods
  createSocialPlatform(data: CreateDummySocialPlatformRequest): Observable<CreateDummySocialPlatformResponse>;
  deleteSocialPlatform(data: DeleteDummySocialPlatformRequest): Observable<DeleteDummySocialPlatformResponse>;
  listSocialPlatforms(data: ListDummySocialPlatformsRequest): Observable<ListDummySocialPlatformsResponse>;
}
