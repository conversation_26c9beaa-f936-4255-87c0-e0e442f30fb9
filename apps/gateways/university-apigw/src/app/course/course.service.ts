import { Injectable, Logger } from '@nestjs/common';
import { Observable } from 'rxjs';
import {
  UpdateCourseRequest,
  GetCourseRequest,
  DeleteCourseRequest,
  ListCourseRequest,
  CourseResponse,
  CourseListResponse,
  DeleteCourseResponse,
  CourseGrpcService,
  Course,
} from './course.interface';
import { UniversityClientService } from '../app.service';

@Injectable()
export class CourseService {
  private readonly logger = new Logger(CourseService.name);

  constructor(private readonly universityClient: UniversityClientService) {}

  private get grpcService(): CourseGrpcService {
    return this.universityClient.getService<CourseGrpcService>('UniversityService');
  }

  createCourse(data: Course): Observable<CourseResponse> {
    console.log("Course Information",data)
    this.logger.log(`Creating course for university ID: ${data}`);
    return this.grpcService.createCourse(data);
  }

  updateCourse(data: UpdateCourseRequest): Observable<CourseResponse> {
    this.logger.log(`Updating course with ID: ${data.id}`);
    return this.grpcService.updateCourse(data);
  }

  getCourse(data: GetCourseRequest): Observable<CourseResponse> {
    this.logger.log(`Fetching course with ID: ${data.id}`);
    return this.grpcService.getCourse(data);
  }

  listCourses(data: ListCourseRequest): Observable<CourseListResponse> {
    this.logger.log(`Listing courses for university ID: ${data.universityId}`);
    return this.grpcService.listCourse(data);
  }

  deleteCourse(data: DeleteCourseRequest): Observable<DeleteCourseResponse> {
    this.logger.log(`Deleting course with ID: ${data.id}`);
    return this.grpcService.deleteCourse(data);
  }
}
