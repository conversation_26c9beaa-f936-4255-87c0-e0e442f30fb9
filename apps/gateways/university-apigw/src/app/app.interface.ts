import { Observable } from 'rxjs';

// Re-export all interfaces from university.interface
export * from './university/university.interface';

// Define base interfaces for the university service
export interface CreateCountryRequest {
  name: string;
  code: string;
  isActive?: boolean;
}

export interface CountryResponse {
  id: number;
  name: string;
  code: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface GetCountryRequest {
  id: number;
}

export interface UpdateCountryRequest {
  id: number;
  name?: string;
  code?: string;
  isActive?: boolean;
}

export interface DeleteCountryRequest {
  id: number;
}

export interface ListCountriesRequest {
  page?: number;
  limit?: number;
  isActive?: boolean;
}

export interface ListCountriesResponse {
  total: number;
  page: number;
  limit: number;
  countries: CountryResponse[];
}

// Course Study Field interfaces
export interface CreateCourseStudyFieldRequest {
  courseId: number;
  studyFieldId: number;
  isActive?: boolean;
}

export interface CourseStudyFieldResponse {
  id: number;
  courseId: number;
  studyFieldId: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface GetCourseStudyFieldRequest {
  id: number;
}

export interface UpdateCourseStudyFieldRequest {
  id: number;
  isActive?: boolean;
}

export interface DeleteCourseStudyFieldRequest {
  id: number;
}

export interface DeleteCourseStudyFieldResponse {
  success: boolean;
}

export interface ListCourseStudyFieldsRequest {
  page?: number;
  limit?: number;
  courseId?: number;
  studyFieldId?: number;
  isActive?: boolean;
}

export interface ListCourseStudyFieldsResponse {
  total: number;
  page: number;
  limit: number;
  courseStudyFields: CourseStudyFieldResponse[];
}

export interface GetCourseStudyFieldsByCourseRequest {
  courseId: number;
}

export interface GetCourseStudyFieldsByStudyFieldRequest {
  studyFieldId: number;
}

// Define the main service interface
export interface UniversityService {
  // Country methods
  createCountry(data: CreateCountryRequest): Observable<CountryResponse>;
  getCountry(data: GetCountryRequest): Observable<CountryResponse>;
  updateCountry(data: UpdateCountryRequest): Observable<CountryResponse>;
  deleteCountry(data: DeleteCountryRequest): Observable<CountryResponse>;
  listCountries(data: ListCountriesRequest): Observable<ListCountriesResponse>;

  // Course Study Field methods
  createCourseStudyField(data: CreateCourseStudyFieldRequest): Observable<CourseStudyFieldResponse>;
  getCourseStudyField(data: GetCourseStudyFieldRequest): Observable<CourseStudyFieldResponse>;
  updateCourseStudyField(data: UpdateCourseStudyFieldRequest): Observable<CourseStudyFieldResponse>;
  deleteCourseStudyField(data: DeleteCourseStudyFieldRequest): Observable<DeleteCourseStudyFieldResponse>;
  listCourseStudyFields(data: ListCourseStudyFieldsRequest): Observable<ListCourseStudyFieldsResponse>;
  getCourseStudyFieldsByCourse(data: GetCourseStudyFieldsByCourseRequest): Observable<ListCourseStudyFieldsResponse>;
  getCourseStudyFieldsByStudyField(data: GetCourseStudyFieldsByStudyFieldRequest): Observable<ListCourseStudyFieldsResponse>;
}