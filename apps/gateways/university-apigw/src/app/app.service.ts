import { Injectable, OnModuleInit } from '@nestjs/common';
import { Client, ClientGrpc } from '@nestjs/microservices';
import { Transport } from '@nestjs/microservices';
import { join } from 'path';
import { Observable } from 'rxjs';

@Injectable()
export class UniversityClientService implements OnModuleInit {
  @Client({
    transport: Transport.GRPC,
    options: {
      package: 'university',
      protoPath: join(process.cwd(), 'libs/shared/dto/src/lib/university/university.proto'),
      // url: process.env.NODE_ENV === 'production' ? 'university-service:50059' : 'localhost:50059',
      url:'university-service:50059',
      loader: {
        keepCase: true,
        longs: String,
        enums: String,
        defaults: true,
        oneofs: true,
      },
    }
  })
  private readonly client: ClientGrpc;

  private universityService: any;

  onModuleInit() {
    this.universityService = this.client.getService('UniversityService');
  }

  // Expose the gRPC service for other components to use
  getService<T extends object>(serviceName: string): T {
    return this.client.getService<T>(serviceName);
  }
  
  getData(): { message: string } {
    return { message: 'Welcome to university-apigw!' };
  }
}
