import { Module } from '@nestjs/common';
import { UniversityContactController } from './university-contact.controller';
import { UniversityContactService } from './university-contact.service';
import { UniversityClientService } from '../app.service';

@Module({
  controllers: [UniversityContactController],
  providers: [UniversityContactService, UniversityClientService],
  exports: [UniversityContactService],
})
export class UniversityContactModule {}
