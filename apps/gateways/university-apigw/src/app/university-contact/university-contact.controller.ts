import {
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Body,
  Query,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import {
  CreateUniversityContactRequest,
  UpdateUniversityContactRequest,
  DeleteUniversityContactRequest,
  GetUniversityContactRequest,
  ListUniversityContactRequest,
} from './university-contact.interface';
import { UniversityContactService } from './university-contact.service';
import { firstValueFrom } from 'rxjs';
import {
  Permissions,
  Public
} from '@apply-goal-backend/auth';
import { UserPermissions } from '@apply-goal-backend/common';

@Controller('university-contact')
export class UniversityContactController {
  private readonly logger = new Logger(UniversityContactController.name);

  constructor(private readonly contactService: UniversityContactService) {}

 @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Post()
  async create(@Body() body: CreateUniversityContactRequest) {
    try {
      return await firstValueFrom(this.contactService.create(body));
    } catch (error) {
      this.logger.error('Create university contact failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Put()
  async update(@Body() body: UpdateUniversityContactRequest) {
    try {
      return await firstValueFrom(this.contactService.update(body));
    } catch (error) {
      this.logger.error('Update university contact failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
 @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Delete()
  async delete(@Query() query: DeleteUniversityContactRequest) {
    try {
      return await firstValueFrom(this.contactService.delete(query));
    } catch (error) {
      this.logger.error('Delete university contact failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Get('one')
  async get(@Query() query: GetUniversityContactRequest) {
    try {
      return await firstValueFrom(this.contactService.get(query));
    } catch (error) {
      this.logger.error('Get university contact failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Get()
  async list(@Query() query: ListUniversityContactRequest) {
    try {
      const { universityId, page, pageSize } = query;

      // Fallback defaults if page or pageSize is 0 or undefined
      const pageNum = Number(page) > 0 ? Number(page) : 1;
      const size = Number(pageSize) > 0 ? Number(pageSize) : 10;

      const payload = {
        universityId,
        page: pageNum,
        pageSize: size,
      };

      return await firstValueFrom(this.contactService.list(payload));
    } catch (error) {
      this.logger.error('List university contacts failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
