import { Injectable, Logger } from '@nestjs/common';
import {
  CreateProgramLevelRequest,
  UpdateProgramLevelRequest,
  GetProgramLevelRequest,
  DeleteProgramLevelRequest,
  ListProgramLevelsRequest,
  ProgramLevelResponse,
  DeleteProgramLevelResponse,
  ProgramLevelListResponse,
  ProgramLevelGrpcService,
} from './programLevel.interface';
import { Observable } from 'rxjs';
import { UniversityClientService } from '../app.service';

@Injectable()
export class ProgramLevelService {
  private readonly logger = new Logger(ProgramLevelService.name);

  constructor(private readonly universityClient: UniversityClientService) {}

  createProgramLevel(data: CreateProgramLevelRequest): Observable<ProgramLevelResponse> {
    this.logger.log(`Creating program level for university ID: ${data.universityId}`);
    return this.universityClient
      .getService<ProgramLevelGrpcService>('UniversityService')
      .createProgramLevel(data);
  }

  updateProgramLevel(data: UpdateProgramLevelRequest): Observable<ProgramLevelResponse> {
    this.logger.log(`Updating program level ID: ${data.id}`);
    return this.universityClient
      .getService<ProgramLevelGrpcService>('UniversityService')
      .updateProgramLevel(data);
  }

  getProgramLevel(data: GetProgramLevelRequest): Observable<ProgramLevelResponse> {
    this.logger.log(`Fetching program level ID: ${data.id}`);
    return this.universityClient
      .getService<ProgramLevelGrpcService>('UniversityService')
      .getProgramLevel(data);
  }

  deleteProgramLevel(data: DeleteProgramLevelRequest): Observable<DeleteProgramLevelResponse> {
    this.logger.log(`Deleting program level ID: ${data.id}`);
    return this.universityClient
      .getService<ProgramLevelGrpcService>('UniversityService')
      .deleteProgramLevel(data);
  }

  listProgramLevels(data: ListProgramLevelsRequest): Observable<ProgramLevelListResponse> {
    this.logger.log(`Listing program levels for university ID: ${data.universityId}`);
    return this.universityClient
      .getService<ProgramLevelGrpcService>('UniversityService')
      .listProgramLevelByUniversity(data);
  }
}