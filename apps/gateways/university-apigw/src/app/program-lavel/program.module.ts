import { Module } from '@nestjs/common';
import {  ProgramLevelController } from './programLevel.controller';
import { ProgramLevelService } from './programlevel.service';
import { UniversityClientService } from '../app.service';

@Module({
  controllers: [ProgramLevelController],
  providers: [ProgramLevelService, UniversityClientService],
  exports: [ProgramLevelService],
})
export class ProgramLevelModule {}
