import {
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Body,
  Query,
  Param,
  HttpStatus,
  HttpException,
  Logger,
} from '@nestjs/common';
import { firstValueFrom } from 'rxjs';
import {
  CreateCampusRequest,
  UpdateCampusRequest,
  ListCampusRequest,
  CampusGrpcService,
} from './campus.interface';
import { CampusService } from './campus.service';
import { Permissions, Public } from '@apply-goal-backend/auth';
import { UserPermissions } from '@apply-goal-backend/common';
import { CurrentUser } from '@apply-goal-backend/auth';

@Controller('campuses')
export class CampusController {
  private readonly logger = new Logger(CampusController.name);

  constructor(private readonly campusService: CampusService) {}
  @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Post()
  async create(
    @Body() payload: CreateCampusRequest,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[]
  ) {
    try {
      // Add userId and userRole
      payload.userId = String(userId);
      payload.userRole = roles[0];
      
      return await firstValueFrom(this.campusService.createCampus(payload));
    } catch (error) {
      this.logger.error('Create campus failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Put(':id')
  async update(
    @Param('id') id: string, 
    @Body() payload: UpdateCampusRequest,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[]
  ) {
    try {
      // Add userId and userRole
      payload.userId = String(userId);
      payload.userRole = roles[0];
      
      return await firstValueFrom(
        this.campusService.updateCampus({ ...payload, id: Number(id) })
      );
    } catch (error) {
      this.logger.error('Update campus failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Get(':id')
  async get(@Param('id') id: string) {
    try {
      return await firstValueFrom(
        this.campusService.getCampus({ id: Number(id) })
      );
    } catch (error) {
      this.logger.error('Get campus failed', error.stack);
      throw new HttpException(error.message, HttpStatus.NOT_FOUND);
    }
  }
  @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Get()
  async list(@Query() query: ListCampusRequest) {
    try {
      const page = Number(query.page) > 0 ? Number(query.page) : 1;
      const pageSize = Number(query.pageSize) > 0 ? Number(query.pageSize) : 10;
      return await firstValueFrom(
        this.campusService.listCampuses({
          universityId: query.universityId,
          page,
          pageSize,
        })
      );
    } catch (error) {
      this.logger.error('List campuses failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Delete(':id')
  async delete(
    @Param('id') id: string,
    @CurrentUser('id') userId: number
  ) {
    try {
      return await firstValueFrom(
        this.campusService.deleteCampus({ id: Number(id), userId: String(userId) })
      );
    } catch (error) {
      this.logger.error('Delete campus failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
