import { Injectable, Logger } from '@nestjs/common';
import { Observable } from 'rxjs';
import {
  CreateCampusRequest,
  UpdateCampusRequest,
  GetCampusRequest,
  DeleteCampusRequest,
  ListCampusRequest,
  CampusResponse,
  CampusListResponse,
  DeleteCampusResponse,
  CampusGrpcService,
} from './campus.interface';
import { UniversityClientService } from '../app.service';

@Injectable()
export class CampusService {
  private readonly logger = new Logger(CampusService.name);

  constructor(private readonly universityClient: UniversityClientService) {}

  private get grpcService(): CampusGrpcService {
    return this.universityClient.getService<CampusGrpcService>('UniversityService');
  }

  createCampus(data: CreateCampusRequest): Observable<CampusResponse> {
    this.logger.log(`Creating campus: ${data.campusName}`);
    console.log("fullPayload",data)
    return this.grpcService.createCampus(data);
  }

  updateCampus(data: UpdateCampusRequest): Observable<CampusResponse> {
    this.logger.log(`Updating campus with ID: ${data.id}`);
    return this.grpcService.updateCampus(data);
  }

  getCampus(data: GetCampusRequest): Observable<CampusResponse> {
    this.logger.log(`Fetching campus with ID: ${data.id}`);
    return this.grpcService.getCampus(data);
  }

  listCampuses(data: ListCampusRequest): Observable<CampusListResponse> {
    this.logger.log(`Listing campuses for university ID: ${data.universityId}`);
    return this.grpcService.getCampusList(data);
  }

  deleteCampus(data: DeleteCampusRequest): Observable<DeleteCampusResponse> {
    this.logger.log(`Deleting campus with ID: ${data.id}`);
    return this.grpcService.deleteCampus(data);
  }
}
