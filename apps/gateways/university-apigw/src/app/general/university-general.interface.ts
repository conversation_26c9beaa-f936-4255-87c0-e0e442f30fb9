import { Observable } from 'rxjs';

export interface UniversityGeneral {
  id: string;
  universityId: string;
  tuitionFeeDiscount: boolean;
  financialAidAcceptance: boolean;
  scholarshipOpportunity: boolean;
  accommodationStatus: boolean;
  employmentOpportunities: boolean;
  activeEnrollment: boolean;
  universityAgreement: string[];
  universityFeatures: string[];
  universityProspectus: string[];
  createdAt: string;
  updatedAt: string;
}

export interface CreateUniversityGeneralRequest {
  universityId: string;
  userId?: string;
  userRole?: string;
  tuitionFeeDiscount: boolean;
  financialAidAcceptance: boolean;
  scholarshipOpportunity: boolean;
  accommodationStatus: boolean;
  employmentOpportunities: boolean;
  activeEnrollment: boolean;
  universityAgreement: string[];
  universityFeatures: string[];
  universityProspectus: string[];
}

export interface UpdateUniversityGeneralRequest extends CreateUniversityGeneralRequest {
  id: string;
}

export interface GetUniversityGeneralRequest {
  universityId: string;
}

export interface DeleteUniversityGeneralRequest {
  id: string;
  userId?: string;
}

export interface ErrorResponse {
  code?: string;
  message: string;
  details?: string;
  validationErrors?: any[];
}

export interface UniversityGeneralResponse {
  status: number;
  message: string;
  data: UniversityGeneral;
  error: ErrorResponse | null;
}

export interface UniversityGeneralListResponse {
  status: number;
  message: string;
  data: UniversityGeneral[];
  error: ErrorResponse | null;
}

export interface DeleteUniversityGeneralResponse {
  status: number;
  message: string;
  success: boolean;
  error: ErrorResponse | null;
}


export interface UniversityGeneralGrpcService {
  createUniversityGeneral(data: CreateUniversityGeneralRequest): Observable<UniversityGeneralResponse>;
  updateUniversityGeneral(data: UpdateUniversityGeneralRequest): Observable<UniversityGeneralResponse>;
  getUniversityGeneral(data: GetUniversityGeneralRequest): Observable<UniversityGeneralResponse>;
  deleteUniversityGeneral(data: DeleteUniversityGeneralRequest): Observable<DeleteUniversityGeneralResponse>;
}
