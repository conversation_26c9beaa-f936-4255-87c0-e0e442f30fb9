import { Module } from '@nestjs/common';
import { UniversityGeneralController } from './university-general.controller';
import { UniversityGeneralService } from './university-general.service';
import { UniversityClientService } from '../app.service';

@Module({
  controllers: [UniversityGeneralController],
  providers: [UniversityGeneralService, UniversityClientService],
  exports: [UniversityGeneralService],
})
export class UniversityGeneralModule {}
