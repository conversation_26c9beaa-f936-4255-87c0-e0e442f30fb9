import { Injectable, Logger } from '@nestjs/common';
import {
  UniversityGeneralGrpcService,
  CreateUniversityGeneralRequest,
  UpdateUniversityGeneralRequest,
  DeleteUniversityGeneralRequest,
  GetUniversityGeneralRequest,
  UniversityGeneralResponse,
  UniversityGeneralListResponse,
  DeleteUniversityGeneralResponse,
} from './university-general.interface';
import { Observable } from 'rxjs';
import { UniversityClientService } from '../app.service';

@Injectable()
export class UniversityGeneralService {
  private readonly logger = new Logger(UniversityGeneralService.name);

  constructor(private readonly universityClient: UniversityClientService) {}

  private get grpcService(): UniversityGeneralGrpcService {
    return this.universityClient.getService<UniversityGeneralGrpcService>('UniversityService');
  }

  create(data: CreateUniversityGeneralRequest): Observable<UniversityGeneralResponse> {
    return this.grpcService.createUniversityGeneral(data);
  }

  update(data: UpdateUniversityGeneralRequest): Observable<UniversityGeneralResponse> {
    return this.grpcService.updateUniversityGeneral(data);
  }

  get(data: GetUniversityGeneralRequest): Observable<UniversityGeneralResponse> {
    return this.grpcService.getUniversityGeneral(data);
  }

  delete(data: DeleteUniversityGeneralRequest): Observable<DeleteUniversityGeneralResponse> {
    return this.grpcService.deleteUniversityGeneral(data);
  }
}
