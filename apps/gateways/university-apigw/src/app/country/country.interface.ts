import { Observable } from 'rxjs';

export interface Country {
  id: number;
  universityId: number;
  countryName: string;
  code?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateCountryRequest {
  universityId: number;
  userId?: string;
  userRole?: string;
  countryNames: string[];
  isActive?: boolean;
}

export interface CreateCountryListResponse {
  status: number;
  message: string;
  data: Country[] | null;
  error: ErrorResponse | null;
}

export interface UpdateCountryRequest {
  universityId: number;
  userId?: string;
  userRole?: string;
  countryNames: string[];
}

export interface UpdateCountryResponse {
  status: number;
  message: string;
  data: Country[]; // Only updated country list
  error: ErrorResponse | null;
}

export interface GetCountryRequest {
  id: number;
  userId?: string;
}

export interface CountryResponse {
  status: number;
  message: string;
  data: Country;
  error: ErrorResponse | null;
}

export interface DeleteCountryRequest {
  id: number;
  universityId: number;
  userId: string;
}

export interface DeleteResponse {
  status: number;
  message: string;
  success: boolean;
  error: ErrorResponse | null;
}

export interface ListCountriesRequest {
  universityId: number;
  page?: number;
  limit?: number;
  isActive?: boolean;
}

export interface CountryListResponse {
  status: number;
  message: string;
  data: Country[];
  error: ErrorResponse | null;
}

export interface ErrorResponse {
  validationErrors: any[];
  code: string;
  message: string;
  details: string;
}

export interface CountryGrpcService {
  createCountry(data: CreateCountryRequest): Observable<CreateCountryListResponse>;
  getCountry(data: GetCountryRequest): Observable<CountryResponse>;
  updateCountry(data: UpdateCountryRequest): Observable<UpdateCountryResponse>;
  deleteCountry(data: DeleteCountryRequest): Observable<DeleteResponse>;
  listCountries(data: ListCountriesRequest): Observable<CountryListResponse>;
}
