// country.service.ts
import { Injectable, Logger } from '@nestjs/common';
import { Observable } from 'rxjs';
import { UniversityClientService } from '../app.service';
import {
  CountryGrpcService,
  CreateCountryRequest,
  CountryResponse,
  GetCountryRequest,
  UpdateCountryRequest,
  DeleteCountryRequest,
  ListCountriesRequest,
  CountryListResponse,
  DeleteResponse,
  CreateCountryListResponse,
  UpdateCountryResponse,
} from './country.interface';

@Injectable()
export class CountryService {
  private readonly logger = new Logger(CountryService.name);

  constructor(private readonly universityClient: UniversityClientService) {}

  createCountry(
    data: CreateCountryRequest
  ): Observable<CreateCountryListResponse> {
    return this.universityClient
      .getService<CountryGrpcService>('UniversityService')
      .createCountry(data);
  }

  getCountry(id: number, userId: string = '1'): Observable<CountryResponse> {
    return this.universityClient
      .getService<CountryGrpcService>('UniversityService')
      .getCountry({ id, userId });
  }

updateCountry(
  data: UpdateCountryRequest,
): Observable<UpdateCountryResponse> {
  return this.universityClient
    .getService<CountryGrpcService>('UniversityService')
    .updateCountry(data);
}

  deleteCountry(id: number, userId: string = '1',universityId:number): Observable<DeleteResponse> {
    return this.universityClient
      .getService<CountryGrpcService>('UniversityService')
      .deleteCountry({ id, universityId, userId });
  }

  listCountriesByUniversity(
    universityId: number,
    page = 1,
    limit = 10
  ): Observable<CountryListResponse> {
    return this.universityClient
      .getService<CountryGrpcService>('UniversityService')
      .listCountries({ universityId, page, limit });
  }
}
