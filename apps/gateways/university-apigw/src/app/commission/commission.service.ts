// commission.service.ts
import { Injectable, Logger } from '@nestjs/common';
import { Observable } from 'rxjs';
import { UniversityClientService } from '../app.service';
import {
  CommissionGrpcService,
  CreateCommissionRequest,
  CreateCommissionResponse,
  CommissionResponse,
  GetCommissionRequest,
  UpdateCommissionRequest,
  UpdateCommissionResponse,
  DeleteCommissionRequest,
  GetCommissionsListRequest,
  CommissionListResponse,
  DeleteResponse,
} from './commission.interface';

@Injectable()
export class CommissionService {
  private readonly logger = new Logger(CommissionService.name);

  constructor(private readonly universityClient: UniversityClientService) {}

  createCommission(
    data: CreateCommissionRequest
  ): Observable<CreateCommissionResponse> {
    return this.universityClient
      .getService<CommissionGrpcService>('UniversityService')
      .createCommission(data);
  }

  getCommission(universityId: string, userId: string = '1'): Observable<CommissionResponse> {
    return this.universityClient
      .getService<CommissionGrpcService>('UniversityService')
      .getCommission({ universityId, userId });
  }

  updateCommission(
    data: UpdateCommissionRequest,
  ): Observable<CreateCommissionResponse> {
    return this.universityClient
      .getService<CommissionGrpcService>('UniversityService')
      .updateCommission(data);
  }

  deleteCommission(
    commissionId: string, 
    universityId: string, 
    userId: string = '1'
  ): Observable<DeleteResponse> {
    return this.universityClient
      .getService<CommissionGrpcService>('UniversityService')
      .deleteCommission({ commissionId, universityId, userId });
  }

  listCommissions(
    universityId: string,
    userId: string,
    page = 1,
    pageSize = 10
  ): Observable<CommissionListResponse> {
    return this.universityClient
      .getService<CommissionGrpcService>('UniversityService')
      .listCommissions({ universityId, userId, page, pageSize });
  }
}