import { Observable } from 'rxjs';

export interface StudentBaseCommission {
  studentBaseCommissionId?: string;
  studentNumber: number;
  commission: string; // e.g., "20%"
}

export interface CommissionData {
  programCommissionId?: string;
  commissionName: string;
  programLevelId: number;
  commissionType: string;
  commissionValue: string;
  startDate: string;
  endDate: string;
  studentBaseCommission: StudentBaseCommission[];
  status?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface UniversityCommission {
  universityId: string;
  userId: string;
  currency: string;
  paymentFrequency: string;
  paymentTerm: string;
  paymentMethod: string;
  commissionPayoutCycle: string;
  commissionPeriod: string;
  commission: CommissionData[];
  createdAt?: string;
  updatedAt?: string;
}

export interface CreateCommissionRequest {
  universityId: string;
  userRole: string;
  userId: string;
  currency: string;
  paymentFrequency: string;
  paymentTerm: string;
  paymentMethod: string;
  commissionPayoutCycle: string;
  commissionPeriod: string;
  commission: CommissionData[];
}

export interface CreateCommissionResponse {
  status: number;
  message: string;
  data: UniversityCommission | null;
  error: ErrorResponse | null;
}

export interface UpdateCommissionRequest {
  commissionId: string;
  universityId: string;
  userId: string;
  userRole: string;
  currency: string;
  paymentFrequency: string;
  paymentTerm: string;
  paymentMethod: string;
  commissionPayoutCycle: string;
  commissionPeriod: string;
  commission: CommissionData[];
}

export interface UpdateCommissionResponse {
  status: number;
  message: string;
  data: UniversityCommission | null;
  error: ErrorResponse | null;
}

export interface GetCommissionsListRequest {
  universityId: string;
  userId: string;
  page?: number;
  pageSize?: number;
}

export interface CommissionListResponse {
  status: number;
  message: string;
  data: UniversityCommission[];
  error: ErrorResponse | null;
}

export interface GetCommissionRequest {
  universityId: string;
  userId?: string;
}

export interface CommissionResponse {
  status: number;
  message: string;
  data: UniversityCommission | null;
  error: ErrorResponse | null;
}

export interface DeleteCommissionRequest {
  commissionId: string;
  universityId: string;
  userId?: string;
}

export interface DeleteResponse {
  status: number;
  message: string;
  success: boolean;
  error: ErrorResponse | null;
}

export interface ErrorResponse {
  validationErrors: any[];
  code: string;
  message: string;
  details: string;
}

export interface CommissionGrpcService {
  createCommission(data: CreateCommissionRequest): Observable<CreateCommissionResponse>;
  getCommission(data: GetCommissionRequest): Observable<CreateCommissionResponse>;
  updateCommission(data: UpdateCommissionRequest): Observable<UpdateCommissionResponse>;
  deleteCommission(data: DeleteCommissionRequest): Observable<DeleteResponse>;
  listCommissions(data: GetCommissionsListRequest): Observable<CommissionListResponse>;
}
