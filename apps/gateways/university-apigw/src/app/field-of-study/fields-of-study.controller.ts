import {
  Controller,
  Post,
  Put,
  Delete,
  Get,
  Body,
  Query,
  Logger,
  HttpException,
  HttpStatus,
  Param,
} from '@nestjs/common';
import {
  CreateFieldOfStudyRequest,
  UpdateFieldOfStudyRequest,
  DeleteFieldOfStudyRequest,
  GetFieldOfStudyRequest,
  ListFieldOfStudyRequest,
} from './field-of-study.interface';
import { FieldsOfStudyService } from './fields-of-study.service';
import { firstValueFrom } from 'rxjs';
import { Permissions, Public } from '@apply-goal-backend/auth';
import { UserPermissions } from '@apply-goal-backend/common';
import { CurrentUser } from '@apply-goal-backend/auth';

@Controller('fields-of-study')
export class FieldsOfStudyController {
  private readonly logger = new Logger(FieldsOfStudyController.name);

  constructor(private readonly fieldsOfStudyService: FieldsOfStudyService) {}
  // @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Public()
  @Post()
  async create(
    @Body() body: CreateFieldOfStudyRequest,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[]
  ) {
    try {
      // Add userId and userRole
      body.userId = String(userId);
      body.userRole = roles[0];

      return await firstValueFrom(this.fieldsOfStudyService.createField(body));
    } catch (error) {
      this.logger.error('Create field of study failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  // @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Public()
  @Put()
  async update(
    @Body() body: UpdateFieldOfStudyRequest,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[]
  ) {
    try {
      // Add userId and userRole
      body.userId = String(userId);
      body.userRole = roles[0];

      return await firstValueFrom(this.fieldsOfStudyService.updateField(body));
    } catch (error) {
      this.logger.error('Update field of study failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  // @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Public()
  @Delete(':id')
  async delete(@Param('id') id: number, @CurrentUser('id') userId: number) {
    try {
      return await firstValueFrom(
        this.fieldsOfStudyService.deleteField({ id, userId: String(userId) })
      );
    } catch (error) {
      this.logger.error('Delete field of study failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  // @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Public()
  @Get()
  async list(@Query() query: ListFieldOfStudyRequest) {
    try {
      this.logger.log(
        `Listing fields of study for university ID: ${query.universityId}`
      );
      return await firstValueFrom(this.fieldsOfStudyService.listFields(query));
    } catch (error) {
      this.logger.error('List fields of study failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  // @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Public()
  @Get('one')
  async get(@Query() query: GetFieldOfStudyRequest) {
    try {
      this.logger.log(`Getting field of study ID: ${query.id}`);
      return await firstValueFrom(this.fieldsOfStudyService.getField(query));
    } catch (error) {
      this.logger.error('Get field of study failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
