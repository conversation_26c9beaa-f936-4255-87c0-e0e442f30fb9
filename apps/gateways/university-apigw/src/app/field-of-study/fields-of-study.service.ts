import { Injectable, Logger } from '@nestjs/common';
import {
  FieldsOfStudyGrpcService,
  CreateFieldOfStudyRequest,
  UpdateFieldOfStudyRequest,
  DeleteFieldOfStudyRequest,
  GetFieldOfStudyRequest,
  ListFieldOfStudyRequest,
  FieldOfStudyResponse,
  DeleteFieldOfStudyResponse,
  ListFieldOfStudyResponse,
} from './field-of-study.interface';
import { Observable } from 'rxjs';
import { UniversityClientService } from '../app.service';

@Injectable()
export class FieldsOfStudyService {
  private readonly logger = new Logger(FieldsOfStudyService.name);

  constructor(private readonly universityClient: UniversityClientService) {}

  createField(data: CreateFieldOfStudyRequest): Observable<ListFieldOfStudyResponse> {
    this.logger.log(`Creating fields of study for university ID: ${data.universityId}, Names: [${data.names.join(', ')}]`);
    return this.universityClient.getService<FieldsOfStudyGrpcService>('UniversityService').createFieldOfStudy(data);
  }

  updateField(data: UpdateFieldOfStudyRequest): Observable<ListFieldOfStudyResponse> {
    this.logger.log(`Updating fields of study for university ID: ${data.universityId}, Names: [${data.names.join(', ')}]`);
    return this.universityClient.getService<FieldsOfStudyGrpcService>('UniversityService').updateFieldOfStudy(data);
  }

  deleteField(data: DeleteFieldOfStudyRequest): Observable<DeleteFieldOfStudyResponse> {
    this.logger.log(`Deleting field of study ID: ${data.id}`);
    return this.universityClient.getService<FieldsOfStudyGrpcService>('UniversityService').deleteFieldOfStudy(data);
  }

  getField(data: GetFieldOfStudyRequest): Observable<FieldOfStudyResponse> {
    this.logger.log(`Getting field of study ID: ${data.id}`);
    return this.universityClient.getService<FieldsOfStudyGrpcService>('UniversityService').getFieldOfStudy(data);
  }

  listFields(data: ListFieldOfStudyRequest): Observable<ListFieldOfStudyResponse> {
    this.logger.log(`Listing fields of study for university ID: ${data.universityId}`);
    return this.universityClient.getService<FieldsOfStudyGrpcService>('UniversityService').listFieldOfStudyByUniversity(data);
  }
}