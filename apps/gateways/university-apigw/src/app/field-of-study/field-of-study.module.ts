import { Module } from '@nestjs/common';
import { FieldsOfStudyController } from './fields-of-study.controller';
import { FieldsOfStudyService } from './fields-of-study.service';
import { UniversityClientService } from '../app.service';

@Module({
  controllers: [FieldsOfStudyController],
  providers: [FieldsOfStudyService, UniversityClientService],
  exports: [FieldsOfStudyService],
})
export class FieldsOfStudyModule {}
