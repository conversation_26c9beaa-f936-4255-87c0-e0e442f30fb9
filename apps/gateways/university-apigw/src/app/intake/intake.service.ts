import { Injectable, Logger } from '@nestjs/common';
import {
  CreateIntakeRequest,
  UpdateIntakeRequest,
  DeleteIntakeRequest,
  ListIntakesRequest,
  IntakeGrpcService,
  IntakeResponse,
  CreateIntakesResponse,
  DeleteIntakeResponse,
  ListIntakesResponse,
} from './intake.interface';
import { Observable } from 'rxjs';
import { UniversityClientService } from '../app.service';

@Injectable()
export class IntakeService {
  private readonly logger = new Logger(IntakeService.name);

  constructor(private readonly universityClient: UniversityClientService) {}

  createIntake(data: CreateIntakeRequest): Observable<CreateIntakesResponse> {
    this.logger.log(`Creating intakes for university ID: ${data.universityId}`);
    return this.universityClient.getService<IntakeGrpcService>('UniversityService').createIntake(data);
  }

  updateIntake(data: UpdateIntakeRequest): Observable<IntakeResponse> {
    this.logger.log(`Updating intakes for university ID: ${data.universityId}`);
    return this.universityClient.getService<IntakeGrpcService>('UniversityService').updateIntake(data);
  }

  deleteIntake(data: DeleteIntakeRequest): Observable<DeleteIntakeResponse> {
    this.logger.log(`Deleting intake ID: ${data.intakeId}`);
    return this.universityClient.getService<IntakeGrpcService>('UniversityService').deleteIntake(data);
  }

  listIntakes(data: ListIntakesRequest): Observable<ListIntakesResponse> {
    this.logger.log(`Listing intakes for university ID: ${data.universityId}`);
    return this.universityClient.getService<IntakeGrpcService>('UniversityService').listIntakesByUniversity(data);
  }
}
