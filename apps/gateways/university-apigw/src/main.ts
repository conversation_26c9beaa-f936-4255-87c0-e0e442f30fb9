import { Logger } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app/app.module';
import express from 'express';
import { healthRouter } from './routes/health.routes';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { join } from 'path';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Enable CORS for frontend applications
  app.enableCors({
    origin: ['http://localhost:3002'], // Add your frontend origins here
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
    allowedHeaders: 'Content-Type, Authorization',
    credentials: true,
  });

  // Add express middleware for health routes
  app.use(express.json());
  app.use('/health', healthRouter);
  
  // Setup gRPC Microservice connection to university-service
  app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.GRPC,
    options: {
      package: 'university',
      protoPath: join(process.cwd(), 'libs/shared/dto/src/lib/university/university.proto'),
      url: 'university-apigw:50059', // This is correct for client connection
      maxReceiveMessageLength: 50 * 1024 * 1024,
      maxSendMessageLength: 50 * 1024 * 1024,
      loader: {
        longs: Number,
        enums: String,
        defaults: true,
        arrays: true,
        objects: true,
      },
    },
  });

  const globalPrefix = 'api';
  app.setGlobalPrefix(globalPrefix, {
    exclude: ['/api/metrics'], // Exclude metrics endpoint to avoid double prefix
  });
  
  const port = process.env.PORT || 4005;
  
  // Start microservices
  await app.startAllMicroservices();
  
  await app.listen(port, '0.0.0.0'); // Listen on all interfaces
  Logger.log(
    `🚀 Application is running on: http://localhost:${port}/${globalPrefix}`
  );
  Logger.log(`🌐 Domain access: http://university-api.localhost/${globalPrefix}`);
}

bootstrap();
