import { Logger } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app/app.module';
import express from 'express';
import { healthRouter } from './routes/health.routes';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { join } from 'path';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Enable CORS for frontend applications
  app.enableCors({
    origin: ['http://localhost:3002'], // Add your frontend origins here
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
    allowedHeaders: 'Content-Type, Authorization',
    credentials: true,
  });

  // HTTP Middleware (Express)
  app.use(express.json());
  app.use('/health', healthRouter);

  const globalPrefix = 'api';
  app.setGlobalPrefix(globalPrefix, {
    exclude: ['/api/metrics'], // Exclude metrics endpoint to avoid double prefix
  });

  const port = process.env.PORT || 4008;

  // Start HTTP server
  await app.listen(port, '0.0.0.0'); // Listen on all interfaces

  Logger.log(`🚀 Application is running on: http://localhost:${port}/${globalPrefix}`);
  Logger.log(`🌐 Domain access: http://agency-api.localhost/${globalPrefix}`);
}

bootstrap();
