import { Observable } from 'rxjs';

export interface AgencyBranch {
  id: string;
  agencyId: string;
  branchName: string;
  address: string;
  country: string;
  state: string;
  city: string;
  postalCode: string;
  managerContactNumber: string;
  email: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateAgencyBranch {
  branchName: string;
  address: string;
  country: string;
  state: string;
  city: string;
  postalCode: string;
  managerContactNumber: string;
  email: string;
}

export interface CreateAgencyBranchRequest {
  agencyId: string;
  userId: string;
  userRole: string;
  data: CreateAgencyBranch[];
}

export interface CreateAgencyBranchResponse {
  status: number;
  message: string;
  data: AgencyBranch[];
  error: any;
}

export interface UpdateAgencyBranchRequest {
  agencyId: string;
  userId: string;
  userRole: string;
  data: AgencyBranch[]; // must include `id` in each object
}

export interface UpdateAgencyBranchResponse {
  status: number;
  message: string;
  data: AgencyBranch[];
  error: any;
}

export interface GetAgencyBranchRequest {
  id: string;
}

export interface GetAgencyBranchResponse {
  status: number;
  message: string;
  data: AgencyBranch;
  error: any;
}

export interface DeleteAgencyBranchRequest {
  id: string;
}

export interface DeleteAgencyBranchResponse {
  status: number;
  message: string;
  error: any;
}

export interface ListAgencyBranchRequest {
  agencyId: string;
  page?: number;
  pageSize?: number;
}

export interface ListAgencyBranchResponse {
  status: number;
  message: string;
  data: {
    total: number;
    page: number;
    pageSize: number;
    branches: AgencyBranch[];
  };
  error: any;
}

export interface AgencyBranchGrpcService {
  createAgencyBranch(
    data: CreateAgencyBranchRequest
  ): Observable<CreateAgencyBranchResponse>;

  updateAgencyBranch(
    data: UpdateAgencyBranchRequest
  ): Observable<UpdateAgencyBranchResponse>;

  getAgencyBranch(
    data: GetAgencyBranchRequest
  ): Observable<GetAgencyBranchResponse>;

  deleteAgencyBranch(
    data: DeleteAgencyBranchRequest
  ): Observable<DeleteAgencyBranchResponse>;

  listAgencyBranches(
    data: ListAgencyBranchRequest
  ): Observable<ListAgencyBranchResponse>;
}
