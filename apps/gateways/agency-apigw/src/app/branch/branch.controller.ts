import {
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Body,
  Param,
  Query,
  HttpStatus,
  HttpException,
  Logger,
} from '@nestjs/common';
import { firstValueFrom } from 'rxjs';
import {
  CreateAgencyBranchRequest,
  GetAgencyBranchRequest,
  UpdateAgencyBranchRequest,
  DeleteAgencyBranchRequest,
  ListAgencyBranchRequest,
} from './branch.interface';
import { BranchService } from './branch.service';
import { Permissions } from '@apply-goal-backend/auth';
import { UserPermissions } from '@apply-goal-backend/common';
import { CurrentUser } from '@apply-goal-backend/auth';

@Controller('agency-branches')
export class BranchController {
  private readonly logger = new Logger(BranchController.name);

  constructor(private readonly branchService: BranchService) {}

  @Permissions(UserPermissions.AGENCY_MANAGEMENT)
  @Post()
  async createAgencyBranch(
    @Body() payload: CreateAgencyBranchRequest,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[]
  ) {
    try {
      payload.userId = String(userId);
      payload.userRole = roles[0];
      
      return await firstValueFrom(this.branchService.createAgencyBranch(payload));
    } catch (error) {
      this.logger.error('Create agency branch failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Permissions(UserPermissions.AGENCY_MANAGEMENT)
  @Get(':id')
  async getAgencyBranch(@Param('id') id: string) {
    try {
      return await firstValueFrom(
        this.branchService.getAgencyBranch({ id })
      );
    } catch (error) {
      this.logger.error('Get agency branch failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Permissions(UserPermissions.AGENCY_MANAGEMENT)
  @Put()
  async updateAgencyBranch(
    @Body() payload: UpdateAgencyBranchRequest,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[]
  ) {
    try {
      payload.userId = String(userId);
      payload.userRole = roles[0];
      
      return await firstValueFrom(this.branchService.updateAgencyBranch(payload));
    } catch (error) {
      this.logger.error('Update agency branch failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Permissions(UserPermissions.AGENCY_MANAGEMENT)
  @Delete(':id')
  async deleteAgencyBranch(@Param('id') id: string) {
    try {
      return await firstValueFrom(
        this.branchService.deleteAgencyBranch({ id })
      );
    } catch (error) {
      this.logger.error('Delete agency branch failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Permissions(UserPermissions.AGENCY_MANAGEMENT)
  @Get()
  async listAgencyBranches(@Query() query: ListAgencyBranchRequest) {
    try {
      const page = Number(query.page) > 0 ? Number(query.page) : 1;
      const pageSize = Number(query.pageSize) > 0 ? Number(query.pageSize) : 10;

      return await firstValueFrom(
        this.branchService.listAgencyBranches({
          agencyId: query.agencyId,
          page,
          pageSize,
        })
      );
    } catch (error) {
      this.logger.error('List agency branches failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
