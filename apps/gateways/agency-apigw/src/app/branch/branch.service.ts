import { Injectable, Logger } from '@nestjs/common';
import { Observable } from 'rxjs';
import { AgencyClientService } from '../app.service';
import {
  AgencyBranchGrpcService,
  CreateAgencyBranchRequest,
  CreateAgencyBranchResponse,
  GetAgencyBranchRequest,
  GetAgencyBranchResponse,
  UpdateAgencyBranchRequest,
  UpdateAgencyBranchResponse,
  DeleteAgencyBranchRequest,
  DeleteAgencyBranchResponse,
  ListAgencyBranchRequest,
  ListAgencyBranchResponse,
} from './branch.interface';

@Injectable()
export class BranchService {
  private readonly logger = new Logger(BranchService.name);

  constructor(private readonly agencyClient: AgencyClientService) {}

  private get grpcService(): AgencyBranchGrpcService {
    return this.agencyClient.getService<AgencyBranchGrpcService>('AgencyService');
  }

  createAgencyBranch(data: CreateAgencyBranchRequest): Observable<CreateAgencyBranchResponse> {
    this.logger.log(`Creating agency branches for agency: ${data.agencyId}`);
    return this.grpcService.createAgencyBranch(data);
  }

  getAgencyBranch(data: GetAgencyBranchRequest): Observable<GetAgencyBranchResponse> {
    this.logger.log(`Getting agency branch with ID: ${data.id}`);
    return this.grpcService.getAgencyBranch(data);
  }

  updateAgencyBranch(data: UpdateAgencyBranchRequest): Observable<UpdateAgencyBranchResponse> {
    this.logger.log(`Updating agency branches for agency: ${data.agencyId}`);
    return this.grpcService.updateAgencyBranch(data);
  }

  deleteAgencyBranch(data: DeleteAgencyBranchRequest): Observable<DeleteAgencyBranchResponse> {
    this.logger.log(`Deleting agency branch with ID: ${data.id}`);
    return this.grpcService.deleteAgencyBranch(data);
  }

  listAgencyBranches(data: ListAgencyBranchRequest): Observable<ListAgencyBranchResponse> {
    this.logger.log(`Listing agency branches for agency: ${data.agencyId}`);
    return this.grpcService.listAgencyBranches(data);
  }
}
