import { Injectable, OnModuleInit } from '@nestjs/common';
import { Client, ClientGrpc, Transport } from '@nestjs/microservices';
import { join } from 'path';

@Injectable()
export class AppService {
  getData(): { message: string } {
    return { message: 'Hello API' };
  }
}

@Injectable()
export class AgencyClientService implements OnModuleInit {
  @Client({
    transport: Transport.GRPC,
    options: {
      package: 'agency',
      protoPath: join(process.cwd(), 'libs/shared/dto/src/lib/agency/agency.proto'),
      url: 'agency-service:50060',
      loader: {
        keepCase: true,
        longs: String,
        enums: String,
        defaults: true,
        oneofs: true,
      },
    }
  })
  private readonly client: ClientGrpc;

  private agencyService: any;

  onModuleInit() {
    this.agencyService = this.client.getService('AgencyService');
  }

  getService<T>(serviceName: string): T {
    return this.agencyService as T;
  }
}
