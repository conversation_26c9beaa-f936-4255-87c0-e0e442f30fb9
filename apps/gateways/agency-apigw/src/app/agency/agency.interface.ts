import { Observable } from 'rxjs';

// ======================= Entity =======================
export interface Agency {
  id: string;
  agencyName: string;
  agencyLogo: string;
  address: string;
  agencySize: string;
  about: string;
  primaryContactNumber: string;
  country: string;
  state: string;
  city: string;
  postalCode: string;
  agencyRegistrationNumber: string;
  websiteUrl: string;
  email: string;
  createdAt: string;
  updatedAt: string;
}

// ======================= Error =======================
export interface ValidationError {
  field: string;
  message: string;
  constraint: string;
}

export interface ErrorResponse {
  code: string;
  message: string;
  details: string;
  validationErrors?: ValidationError[];
}

// ======================= Create =======================
export interface CreateAgencyRequest {
  agencyName: string;
  agencyLogo: string;
  address: string;
  agencySize: string;
  about: string;
  primaryContactNumber: string;
  country: string;
  state: string;
  city: string;
  postalCode: string;
  agencyRegistrationNumber: string;
  websiteUrl: string;
  email: string;
  // User creation fields
  ownerFirstName?: string;
  ownerLastName?: string;
  password?: string;
  userId: string;
  userRole: string;
}

export interface CreateAgencyResponse {
  status: number;
  message: string;
  data: Agency;
  error: ErrorResponse;
}

// ======================= Get =======================
export interface GetAgencyRequest {
  id: string;
}

export interface GetAgencyResponse {
  status: number;
  message: string;
  data: Agency;
  error: ErrorResponse;
}

// ======================= Update =======================
export interface UpdateAgencyRequest {
  id: string;
  agencyName?: string;
  agencyLogo?: string;
  address?: string;
  agencySize?: string;
  about?: string;
  primaryContactNumber?: string;
  country?: string;
  state?: string;
  city?: string;
  postalCode?: string;
  agencyRegistrationNumber?: string;
  websiteUrl?: string;
  email?: string;
  userId?: string;
  userRole?: string;
}

export interface UpdateAgencyResponse {
  status: number;
  message: string;
  data: Agency;
  error: ErrorResponse;
}

// ======================= Delete =======================
export interface DeleteAgencyRequest {
  id: string;
}

export interface DeleteAgencyResponse {
  status: number;
  message: string;
  error: ErrorResponse;
}

// ======================= List =======================
export interface ListAgencyRequest {
  page?: number;
  pageSize?: number;
  search?: string;
}

export interface ListAgencyResponse {
  status: number;
  message: string;
  data: Agency[];
  error: ErrorResponse;
}

// ======================= gRPC Service =======================
export interface AgencyGrpcService {
  createAgency(data: CreateAgencyRequest): Observable<CreateAgencyResponse>;
  getAgency(data: GetAgencyRequest): Observable<GetAgencyResponse>;
  updateAgency(data: UpdateAgencyRequest): Observable<UpdateAgencyResponse>;
  deleteAgency(data: DeleteAgencyRequest): Observable<DeleteAgencyResponse>;
  listAgencies(data: ListAgencyRequest): Observable<ListAgencyResponse>;
}
