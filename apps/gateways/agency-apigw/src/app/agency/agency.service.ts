import { Injectable, Logger } from '@nestjs/common';
import { Observable } from 'rxjs';
import { AgencyClientService } from '../app.service';
import {
  AgencyGrpcService,
  CreateAgencyRequest,
  CreateAgencyResponse,
  GetAgencyRequest,
  GetAgencyResponse,
  UpdateAgencyRequest,
  UpdateAgencyResponse,
  DeleteAgencyRequest,
  DeleteAgencyResponse,
  ListAgencyRequest,
  ListAgencyResponse,
} from './agency.interface';

@Injectable()
export class AgencyService {
  private readonly logger = new Logger(AgencyService.name);

  constructor(private readonly agencyClient: AgencyClientService) {}

  private get grpcService(): AgencyGrpcService {
    return this.agencyClient.getService<AgencyGrpcService>('AgencyService');
  }

  createAgency(data: CreateAgencyRequest): Observable<CreateAgencyResponse> {
    this.logger.log(`Creating agency: ${data.agencyName}`);
    return this.grpcService.createAgency(data);
  }

  getAgency(data: GetAgencyRequest): Observable<GetAgencyResponse> {
    this.logger.log(`Getting agency with ID: ${data.id}`);
    return this.grpcService.getAgency(data);
  }

  updateAgency(data: UpdateAgencyRequest): Observable<UpdateAgencyResponse> {
    this.logger.log(`Updating agency with ID: ${data.id}`);
    return this.grpcService.updateAgency(data);
  }

  deleteAgency(data: DeleteAgencyRequest): Observable<DeleteAgencyResponse> {
    this.logger.log(`Deleting agency with ID: ${data.id}`);
    return this.grpcService.deleteAgency(data);
  }

  listAgencies(data: ListAgencyRequest): Observable<ListAgencyResponse> {
    this.logger.log('Listing agencies');
    return this.grpcService.listAgencies(data);
  }
}
