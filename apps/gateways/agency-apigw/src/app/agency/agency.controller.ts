import {
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Body,
  Param,
  Query,
  HttpStatus,
  HttpException,
  Logger,
} from '@nestjs/common';
import { firstValueFrom } from 'rxjs';
import {
  CreateAgencyRequest,
  GetAgencyRequest,
  UpdateAgencyRequest,
  DeleteAgencyRequest,
  ListAgencyRequest,
} from './agency.interface';
import { AgencyService } from './agency.service';
import { Permissions } from '@apply-goal-backend/auth';
import { UserPermissions } from '@apply-goal-backend/common';
import { CurrentUser } from '@apply-goal-backend/auth';

@Controller('agencies')
export class AgencyController {
  private readonly logger = new Logger(AgencyController.name);

  constructor(private readonly agencyService: AgencyService) {}

  @Permissions(UserPermissions.AM_ONBOARD_NEW_AGENCY_PARTNERS)
  @Post()
  async createAgency(
    @Body() payload: CreateAgencyRequest,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[]
  ) {
    try {
      payload.userId = String(userId);
      payload.userRole = roles[0];
      
      return await firstValueFrom(this.agencyService.createAgency(payload));
    } catch (error) {
      this.logger.error('Create agency failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Permissions(UserPermissions.AM_TRACK_AGENCY_PERFORMANCE)
  @Get(':id')
  async getAgency(@Param('id') id: string) {
    try {
      return await firstValueFrom(
        this.agencyService.getAgency({ id })
      );
    } catch (error) {
      this.logger.error('Get agency failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Permissions(UserPermissions.AM_ONBOARD_NEW_AGENCY_PARTNERS)
  @Put(':id')
  async updateAgency(
    @Param('id') id: string,
    @Body() payload: UpdateAgencyRequest,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[]
  ) {
    try {
      payload.id = id;
      payload.userId = String(userId);
      payload.userRole = roles[0];
      
      return await firstValueFrom(this.agencyService.updateAgency(payload));
    } catch (error) {
      this.logger.error('Update agency failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Permissions(UserPermissions.AM_ONBOARD_NEW_AGENCY_PARTNERS)
  @Delete(':id')
  async deleteAgency(@Param('id') id: string) {
    try {
      return await firstValueFrom(
        this.agencyService.deleteAgency({ id })
      );
    } catch (error) {
      this.logger.error('Delete agency failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Permissions(UserPermissions.AM_TRACK_AGENCY_PERFORMANCE)
  @Get()
  async listAgencies(@Query() query: ListAgencyRequest) {
    try {
      const page = Number(query.page) > 0 ? Number(query.page) : 1;
      const pageSize = Number(query.pageSize) > 0 ? Number(query.pageSize) : 10;

      return await firstValueFrom(
        this.agencyService.listAgencies({
          page,
          pageSize,
          search: query.search,
        })
      );
    } catch (error) {
      this.logger.error('List agencies failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
