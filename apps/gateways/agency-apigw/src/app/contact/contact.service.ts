import { Injectable, Logger } from '@nestjs/common';
import { Observable } from 'rxjs';
import { AgencyClientService } from '../app.service';
import {
  AgencyContactGrpcService,
  CreateAgencyContactRequest,
  CreateAgencyContactResponse,
  GetAgencyContactRequest,
  GetAgencyContactResponse,
  UpdateAgencyContactRequest,
  UpdateAgencyContactResponse,
  DeleteAgencyContactRequest,
  DeleteAgencyContactResponse,
  ListAgencyContactRequest,
  ListAgencyContactResponse,
} from './agency-contact.interface';

@Injectable()
export class ContactService {
  private readonly logger = new Logger(ContactService.name);

  constructor(private readonly agencyClient: AgencyClientService) {}

  private get grpcService(): AgencyContactGrpcService {
    return this.agencyClient.getService<AgencyContactGrpcService>('AgencyService');
  }

  createAgencyContact(data: CreateAgencyContactRequest): Observable<CreateAgencyContactResponse> {
    this.logger.log(`Creating agency contact for agency: ${data.agencyId}`);
    return this.grpcService.createAgencyContact(data);
  }

  getAgencyContact(data: GetAgencyContactRequest): Observable<GetAgencyContactResponse> {
    this.logger.log(`Getting agency contact with ID: ${data.id}`);
    return this.grpcService.getAgencyContact(data);
  }

  updateAgencyContact(data: UpdateAgencyContactRequest): Observable<UpdateAgencyContactResponse> {
    this.logger.log(`Updating agency contact with ID: ${data.id}`);
    return this.grpcService.updateAgencyContact(data);
  }

  deleteAgencyContact(data: DeleteAgencyContactRequest): Observable<DeleteAgencyContactResponse> {
    this.logger.log(`Deleting agency contact with ID: ${data.id}`);
    return this.grpcService.deleteAgencyContact(data);
  }

  listAgencyContacts(data: ListAgencyContactRequest): Observable<ListAgencyContactResponse> {
    this.logger.log(`Listing agency contacts for agency: ${data.agencyId}`);
    return this.grpcService.listAgencyContacts(data);
  }
}
