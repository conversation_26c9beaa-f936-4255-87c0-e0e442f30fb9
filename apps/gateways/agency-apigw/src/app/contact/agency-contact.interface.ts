import { Observable } from 'rxjs';

export interface AgencyContact {
  id: string;
  agencyId: string;

  ownerName: string;
  ownerContactNumber: string;
  ownerAlternateContactNumber: string;
  ownerEmail: string;

  primaryPersonName: string;
  primaryPersonDesignation: string;
  primaryContactNumber: string;
  primaryAlternateContactNumber: string;
  primaryEmail: string;

  createdAt: string;
  updatedAt: string;
}

export interface CreateAgencyContactRequest {
  agencyId: string;
  userId: string;
  userRole: string;

  ownerName: string;
  ownerContactNumber: string;
  ownerAlternateContactNumber: string;
  ownerEmail: string;

  primaryPersonName: string;
  primaryPersonDesignation: string;
  primaryContactNumber: string;
  primaryAlternateContactNumber: string;
  primaryEmail: string;
}

export interface UpdateAgencyContactRequest {
  id: string;
  userId: string;
  userRole: string;

  ownerName?: string;
  ownerContactNumber?: string;
  ownerAlternateContactNumber?: string;
  ownerEmail?: string;

  primaryPersonName?: string;
  primaryPersonDesignation?: string;
  primaryContactNumber?: string;
  primaryAlternateContactNumber?: string;
  primaryEmail?: string;
}

export interface GetAgencyContactRequest {
  id: string;
}

export interface DeleteAgencyContactRequest {
  id: string;
}

export interface ListAgencyContactRequest {
  agencyId: string;
  page?: number;
  pageSize?: number;
}

export interface CreateAgencyContactResponse {
  status: number;
  message: string;
  data: AgencyContact;
  error?: ErrorResponse;
}

export interface GetAgencyContactResponse {
  status: number;
  message: string;
  data: AgencyContact;
  error?: ErrorResponse;
}

export interface UpdateAgencyContactResponse {
  status: number;
  message: string;
  data: AgencyContact;
  error?: ErrorResponse;
}

export interface DeleteAgencyContactResponse {
  status: number;
  message: string;
  error?: ErrorResponse;
}

export interface ListAgencyContactResponse {
  status: number;
  message: string;
  data: AgencyContact[];
  error: ErrorResponse;
}

export interface ErrorResponse {
  code: string;
  message: string;
  details: string;
  validationErrors?: ValidationError[];
}

export interface ValidationError {
  field: string;
  message: string;
  constraint: string;
}

export interface AgencyContactGrpcService {
  createAgencyContact(data: CreateAgencyContactRequest): Observable<CreateAgencyContactResponse>;
  getAgencyContact(data: GetAgencyContactRequest): Observable<GetAgencyContactResponse>;
  updateAgencyContact(data: UpdateAgencyContactRequest): Observable<UpdateAgencyContactResponse>;
  deleteAgencyContact(data: DeleteAgencyContactRequest): Observable<DeleteAgencyContactResponse>;
  listAgencyContacts(data: ListAgencyContactRequest): Observable<ListAgencyContactResponse>;
}
