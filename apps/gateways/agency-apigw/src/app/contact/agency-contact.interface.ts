import { Observable } from 'rxjs';

export interface AgencyContact {
  id: string;
  agencyId: string;

  ownerName: string;
  ownerContactNumber: string;
  ownerAlternateContactNumber: string;
  ownerEmail: string;

  primaryPersonName: string;
  primaryPersonDesignation: string;
  primaryContactNumber: string;
  primaryAlternateContactNumber: string;
  primaryEmail: string;

  createdAt: string;
  updatedAt: string;
}

export interface CreateAgencyContactRequest {
  agencyId: string;
  userId: string;
  userRole: string;

  ownerName: string;
  ownerContactNumber: string;
  ownerAlternateContactNumber: string;
  ownerEmail: string;

  primaryPersonName: string;
  primaryPersonDesignation: string;
  primaryContactNumber: string;
  primaryAlternateContactNumber: string;
  primaryEmail: string;
}

export interface UpdateAgencyContactRequest {
  id: string;
  userId: string;
  userRole: string;

  ownerName?: string;
  ownerContactNumber?: string;
  ownerAlternateContactNumber?: string;
  ownerEmail?: string;

  primaryPersonName?: string;
  primaryPersonDesignation?: string;
  primaryContactNumber?: string;
  primaryAlternateContactNumber?: string;
  primaryEmail?: string;
}

export interface GetAgencyContactRequest {
  id: string;
}

export interface DeleteAgencyContactRequest {
  id: string;
}

export interface ListAgencyContactRequest {
  agencyId: string;
  page?: number;
  pageSize?: number;
}

export interface AgencyContactResponse {
  status: number;
  message: string;
  data: AgencyContact;
  error: ErrorResponse;
}

export interface ListAgencyContactResponse {
  status: number;
  message: string;
  data: AgencyContact[];
  error: ErrorResponse;
}

export interface ErrorResponse {
  code: string;
  message: string;
  details: string;
  validationErrors?: ValidationError[];
}

export interface ValidationError {
  field: string;
  message: string;
  constraint: string;
}

export interface AgencyContactGrpcService {
  createAgencyContact(data: CreateAgencyContactRequest): Observable<AgencyContactResponse>;
  getAgencyContact(data: GetAgencyContactRequest): Observable<AgencyContactResponse>;
  updateAgencyContact(data: UpdateAgencyContactRequest): Observable<AgencyContactResponse>;
  deleteAgencyContact(data: DeleteAgencyContactRequest): Observable<{ status: number; message: string; error: ErrorResponse }>;
  listAgencyContacts(data: ListAgencyContactRequest): Observable<ListAgencyContactResponse>;
}
