import {
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Body,
  Param,
  Query,
  HttpStatus,
  HttpException,
  Logger,
} from '@nestjs/common';
import { firstValueFrom } from 'rxjs';
import {
  CreateAgencyContactRequest,
  GetAgencyContactRequest,
  UpdateAgencyContactRequest,
  DeleteAgencyContactRequest,
  ListAgencyContactRequest,
} from './agency-contact.interface';
import { ContactService } from './contact.service';
import { Permissions } from '@apply-goal-backend/auth';
import { UserPermissions } from '@apply-goal-backend/common';
import { CurrentUser } from '@apply-goal-backend/auth';

@Controller('agency-contacts')
export class ContactController {
  private readonly logger = new Logger(ContactController.name);

  constructor(private readonly contactService: ContactService) {}

  @Permissions(UserPermissions.AGENCY_MANAGEMENT)
  @Post()
  async createAgencyContact(
    @Body() payload: CreateAgencyContactRequest,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[]
  ) {
    try {
      payload.userId = String(userId);
      payload.userRole = roles[0];
      
      return await firstValueFrom(this.contactService.createAgencyContact(payload));
    } catch (error) {
      this.logger.error('Create agency contact failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Permissions(UserPermissions.AGENCY_MANAGEMENT)
  @Get(':id')
  async getAgencyContact(@Param('id') id: string) {
    try {
      return await firstValueFrom(
        this.contactService.getAgencyContact({ id })
      );
    } catch (error) {
      this.logger.error('Get agency contact failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Permissions(UserPermissions.AGENCY_MANAGEMENT)
  @Put(':id')
  async updateAgencyContact(
    @Param('id') id: string,
    @Body() payload: UpdateAgencyContactRequest,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[]
  ) {
    try {
      payload.id = id;
      payload.userId = String(userId);
      payload.userRole = roles[0];
      
      return await firstValueFrom(this.contactService.updateAgencyContact(payload));
    } catch (error) {
      this.logger.error('Update agency contact failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Permissions(UserPermissions.AGENCY_MANAGEMENT)
  @Delete(':id')
  async deleteAgencyContact(@Param('id') id: string) {
    try {
      return await firstValueFrom(
        this.contactService.deleteAgencyContact({ id })
      );
    } catch (error) {
      this.logger.error('Delete agency contact failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Permissions(UserPermissions.AGENCY_MANAGEMENT)
  @Get()
  async listAgencyContacts(@Query() query: ListAgencyContactRequest) {
    try {
      const page = Number(query.page) > 0 ? Number(query.page) : 1;
      const pageSize = Number(query.pageSize) > 0 ? Number(query.pageSize) : 10;

      return await firstValueFrom(
        this.contactService.listAgencyContacts({
          agencyId: query.agencyId,
          page,
          pageSize,
        })
      );
    } catch (error) {
      this.logger.error('List agency contacts failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
