import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService, AgencyClientService } from './app.service';
import { AgencyModule } from './agency/agency.module';
import { BranchModule } from './branch/branch.module';
import { ContactModule } from './contact/contact.module';
import { AuthenticationModule } from '@apply-goal-backend/auth';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    AuthenticationModule.forRoot({
      secret: process.env.JWT_SECRET || 'secret',
      expiresIn: '1h',
      refreshExpiresIn: '7d',
    }),
    AgencyModule,
    BranchModule,
    ContactModule,
  ],
  controllers: [AppController],
  providers: [AppService, AgencyClientService],
  exports: [AgencyClientService],
})
export class AppModule {}
