import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AppController } from './app.controller';
import { StudentClientService } from './app.service';
import { StudentModule } from './students/student.module';

@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true }),
    StudentModule,
  ],
  controllers: [AppController],
  providers: [StudentClientService],
  exports: [StudentClientService],
})
export class AppModule {}
