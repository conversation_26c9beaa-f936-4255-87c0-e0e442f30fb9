import { Injectable, OnModuleInit } from '@nestjs/common';
import { Client, ClientGrpc, Transport } from '@nestjs/microservices';
import { join } from 'path';

@Injectable()
export class StudentClientService implements OnModuleInit {
  @Client({
    transport: Transport.GRPC,
    options: {
      package: 'student',
      protoPath: join(process.cwd(), 'libs/shared/dto/src/lib/student/student.proto'),
      url: process.env.NODE_ENV === 'production' ? 'student-service:50051' : 'localhost:50051',
      loader: {
        keepCase: true,
        longs: String,
        enums: String,
        defaults: true,
        oneofs: true,
      },
    },
  })
  private readonly client: ClientGrpc;

  private studentService: any;

  onModuleInit() {
    this.studentService = this.client.getService('StudentService');
  }

  getService<T extends object>(serviceName: string): T {
    return this.client.getService<T>(serviceName);
  }

  getData(): { message: string } {
    return { message: 'Welcome to student-apigw!' };
  }
}
