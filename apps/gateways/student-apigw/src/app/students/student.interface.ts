import { Observable } from 'rxjs';

// Utility types
export interface SocialLink {
  title: string;
  url: string;
}

export interface Timestamp {
  seconds: number;
  nanos: number;
}

// Create
export interface CreateStudentRequest {
  name: string;
  firstName: string;
  nativeName: string;
  email: string;
  dateOfBirth: string; // or use Timestamp
  gender: string;
  fatherName: string;
  motherName: string;
  nationalId: string;
  passportNumber: string;
  maritalStatus: string;
  spouseName: string;
  spousePassportNumber: string;

  presentAddress: string;
  presentCountry: string;
  presentState: string;
  presentCity: string;
  presentPostalCode: string;

  permanentAddress: string;
  permanentCountry: string;
  permanentState: string;
  permanentCity: string;
  permanentPostalCode: string;

  sponsorName: string;
  relationship: string;
  phoneNumber: string;
  guardianNumber: string;

  preferredSubjects: string[];
  preferredCountries: string[];
  socialLinks: SocialLink[];

  reference: string;
  note: string;
}

// Read
export interface GetStudentRequest {
  id: string;
}

// Update
export interface UpdateStudentPayload {
  name?: string;
  firstName?: string;
  nativeName?: string;
  email?: string;
  dateOfBirth?: string;
  gender?: string;
  fatherName?: string;
  motherName?: string;
  nationalId?: string;
  passportNumber?: string;
  maritalStatus?: string;
  spouseName?: string;
  spousePassportNumber?: string;

  presentAddress?: string;
  presentCountry?: string;
  presentState?: string;
  presentCity?: string;
  presentPostalCode?: string;

  permanentAddress?: string;
  permanentCountry?: string;
  permanentState?: string;
  permanentCity?: string;
  permanentPostalCode?: string;

  sponsorName?: string;
  relationship?: string;
  phoneNumber?: string;
  guardianNumber?: string;

  preferredSubjects?: string[];
  preferredCountries?: string[];
  socialLinks?: SocialLink[];

  reference?: string;
  note?: string;
}

export interface UpdateStudentRequest {
  id: string;
  payload: UpdateStudentPayload;
}

// Delete
export interface DeleteStudentRequest {
  id: string;
}

export interface DeleteStudentResponse {
  success: boolean;
  message: string;
}

// Response
export interface StudentResponse {
  id: string;
  name: string;
  firstName: string;
  nativeName: string;
  email: string;
  dateOfBirth: string;
  gender: string;
  fatherName: string;
  motherName: string;
  nationalId: string;
  passportNumber: string;
  maritalStatus: string;
  spouseName: string;
  spousePassportNumber: string;

  presentAddress: string;
  presentCountry: string;
  presentState: string;
  presentCity: string;
  presentPostalCode: string;

  permanentAddress: string;
  permanentCountry: string;
  permanentState: string;
  permanentCity: string;
  permanentPostalCode: string;

  sponsorName: string;
  relationship: string;
  phoneNumber: string;
  guardianNumber: string;

  preferredSubjects: string[];
  preferredCountries: string[];
  socialLinks: SocialLink[];

  reference: string;
  note: string;

  createdAt: string;
  updatedAt: string;
}

// gRPC Service Interface
export interface StudentGrpcService {
  createStudent(data: CreateStudentRequest): Observable<StudentResponse>;
  getStudent(data: GetStudentRequest): Observable<StudentResponse>;
  updateStudent(data: UpdateStudentRequest): Observable<StudentResponse>;
  deleteStudent(data: DeleteStudentRequest): Observable<DeleteStudentResponse>;
}
