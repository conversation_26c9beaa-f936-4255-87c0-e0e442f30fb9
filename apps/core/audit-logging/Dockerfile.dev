# --------------------------------------
# Dev Dockerfile for audit-logging service
# NestJS + Nx + Hot Reload with pnpm
# --------------------------------------

FROM node:18-alpine

# Set working directory
WORKDIR /app

# Install curl for healthcheck and bash for debugging
RUN apk --no-cache add curl bash

# Install pnpm
RUN corepack enable && corepack prepare pnpm@8.15.1 --activate

# Set pnpm store directory
ENV PNPM_HOME="/root/.local/share/pnpm"
ENV PATH="${PATH}:${PNPM_HOME}"

# Copy only essential config files first to leverage caching
COPY pnpm-lock.yaml ./
COPY package.json ./
COPY nx.json ./
COPY tsconfig*.json ./
COPY eslint.config.mjs ./

# Copy project configuration
COPY apps/core/audit-logging/project.json ./apps/core/audit-logging/
COPY libs/*/project.json ./libs/
COPY libs/shared/health-check/package.json ./libs/shared/health-check/

# Enable pnpm workspace mode
COPY pnpm-workspace.yaml ./

# Install dependencies with store cache
RUN --mount=type=cache,id=pnpm,target=/root/.local/share/pnpm/store \
    pnpm install --frozen-lockfile

# Copy full source code
COPY apps/core/audit-logging ./apps/core/audit-logging
COPY libs ./libs

# Build shared libraries first
RUN pnpm nx run-many --target=build --projects=utils,health-check,auth,common,config,database,decorators,dto,interfaces,logging --parallel=3

# Add a healthcheck that verifies migrations have run
HEALTHCHECK --interval=5s --timeout=10s --start-period=30s --retries=3 \
  CMD curl -f http://localhost:3001/health || exit 1

# Set environment variable to control migration behavior
ENV RUN_MIGRATIONS=true

# Expose port
EXPOSE 3001

# Run in dev mode (with Nx hot-reloading)
CMD ["pnpm", "nx", "serve", "audit-logging", "--configuration=development", "--host=0.0.0.0"]
