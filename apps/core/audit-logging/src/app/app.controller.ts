import { Controller } from '@nestjs/common';
import { GrpcMethod, RpcException } from '@nestjs/microservices';
import { AppService } from './app.service';
import {
  CreateAuditLogRequest,
  GetAuditLogRequest,
  ListAuditLogsRequest,
  SearchAuditLogsRequest,
  ListAuditLogsResponse,
  AuditLogResponse,
} from './audit/interfaces/audit.interface';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @GrpcMethod('AuditService', 'CreateAuditLog')
  async createAuditLog(
    request: CreateAuditLogRequest
  ): Promise<AuditLogResponse> {
    return this.appService.createAuditLog(request);
  }

  @GrpcMethod('AuditService', 'GetAuditLog')
  async getAuditLog(request: GetAuditLogRequest): Promise<AuditLogResponse> {
    try {
      return await this.appService.getAuditLog(request);
    } catch (err) {
      throw new RpcException(err.message || 'Internal server error');
    }
  }

  @GrpcMethod('AuditService', 'ListAuditLogs')
  async listAuditLogs(
    request: ListAuditLogsRequest
  ): Promise<ListAuditLogsResponse> {
    return this.appService.listAuditLogs(request);
  }

  @GrpcMethod('AuditService', 'SearchAuditLogs')
  async searchAuditLogs(
    request: SearchAuditLogsRequest
  ): Promise<ListAuditLogsResponse> {
    return this.appService.searchAuditLogs(request);
  }
}
