'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    console.log('Running migration: create-employee-tables');

    // Create employee_personal table
    await queryInterface.createTable('employee_personal', {
      userId: {
        type: Sequelize.BIGINT,
        primaryKey: true,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      
      // Personal Information
      lastName: {
        type: Sequelize.STRING(100),
        allowNull: true,
      },
      firstName: {
        type: Sequelize.STRING(100),
        allowNull: true,
      },
      phone: {
        type: Sequelize.STRING(20),
        allowNull: true,
      },
      dateOfBirth: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      bloodGroup: {
        type: Sequelize.STRING(10),
        allowNull: true,
      },
      gender: {
        type: Sequelize.STRING(10),
        allowNull: true,
      },
      nationality: {
        type: Sequelize.STRING(100),
        allowNull: true,
      },
      maritalStatus: {
        type: Sequelize.STRING(50),
        allowNull: true,
      },

      // Employment Information
      joiningDate: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      jobType: {
        type: Sequelize.STRING(50),
        allowNull: true,
      },
      jobStatus: {
        type: Sequelize.STRING(50),
        allowNull: true,
      },

      // Department Information
      employeeId: {
        type: Sequelize.STRING(50),
        allowNull: true,
      },
      department: {
        type: Sequelize.STRING(100),
        allowNull: true,
      },
      designation: {
        type: Sequelize.STRING(100),
        allowNull: true,
      },
      supervisor: {
        type: Sequelize.STRING(100),
        allowNull: true,
      },
      workLocation: {
        type: Sequelize.STRING(200),
        allowNull: true,
      },

      // Present Address
      presentAddress: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      presentCountry: {
        type: Sequelize.STRING(100),
        allowNull: true,
      },
      presentState: {
        type: Sequelize.STRING(100),
        allowNull: true,
      },
      presentCity: {
        type: Sequelize.STRING(100),
        allowNull: true,
      },
      presentPostalCode: {
        type: Sequelize.STRING(20),
        allowNull: true,
      },

      // Permanent Address
      permanentAddress: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      permanentCountry: {
        type: Sequelize.STRING(100),
        allowNull: true,
      },
      permanentState: {
        type: Sequelize.STRING(100),
        allowNull: true,
      },
      permanentCity: {
        type: Sequelize.STRING(100),
        allowNull: true,
      },
      permanentPostalCode: {
        type: Sequelize.STRING(20),
        allowNull: true,
      },

      // Emergency Contact
      emergencyContactType: {
        type: Sequelize.STRING(50),
        allowNull: true,
      },
      emergencyContactName: {
        type: Sequelize.STRING(100),
        allowNull: true,
      },
      emergencyContactRelation: {
        type: Sequelize.STRING(50),
        allowNull: true,
      },
      emergencyContactPhone: {
        type: Sequelize.STRING(20),
        allowNull: true,
      },
      emergencyContactEmail: {
        type: Sequelize.STRING(100),
        allowNull: true,
      },
      emergencyContactAddress: {
        type: Sequelize.TEXT,
        allowNull: true,
      },

      // Identity Information
      identityType: {
        type: Sequelize.STRING(50),
        allowNull: true,
      },
      identityCountry: {
        type: Sequelize.STRING(100),
        allowNull: true,
      },
      identityNumber: {
        type: Sequelize.STRING(100),
        allowNull: true,
      },
      identityIssueDate: {
        type: Sequelize.STRING(50),
        allowNull: true,
      },
      identityExpiryDate: {
        type: Sequelize.STRING(50),
        allowNull: true,
      },

      // Bank Account Information
      accountHolderName: {
        type: Sequelize.STRING(100),
        allowNull: true,
      },
      accountNumber: {
        type: Sequelize.STRING(50),
        allowNull: true,
      },
      bankName: {
        type: Sequelize.STRING(100),
        allowNull: true,
      },
      branchName: {
        type: Sequelize.STRING(100),
        allowNull: true,
      },
      routingNumber: {
        type: Sequelize.STRING(50),
        allowNull: true,
      },
      accountType: {
        type: Sequelize.STRING(50),
        allowNull: true,
      },

      // Social Links
      linkedinUrl: {
        type: Sequelize.STRING(200),
        allowNull: true,
      },
      twitterUrl: {
        type: Sequelize.STRING(200),
        allowNull: true,
      },
      facebookUrl: {
        type: Sequelize.STRING(200),
        allowNull: true,
      },
      instagramUrl: {
        type: Sequelize.STRING(200),
        allowNull: true,
      },

      // Legacy fields
      agencyId: {
        type: Sequelize.BIGINT,
        allowNull: true,
      },
      orgId: {
        type: Sequelize.STRING(100),
        allowNull: true,
      },

      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });

    // Create employee_address table
    await queryInterface.createTable('employee_address', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.BIGINT,
      },
      userId: {
        type: Sequelize.BIGINT,
        allowNull: false,
        references: {
          model: 'employee_personal',
          key: 'userId',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      addressType: {
        type: Sequelize.STRING(20),
        allowNull: false,
      },
      addressLine: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      country: {
        type: Sequelize.STRING(100),
        allowNull: true,
      },
      state: {
        type: Sequelize.STRING(100),
        allowNull: true,
      },
      city: {
        type: Sequelize.STRING(100),
        allowNull: true,
      },
      postalCode: {
        type: Sequelize.STRING(50),
        allowNull: true,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });

    console.log('Migration completed: create-employee-tables');
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('employee_address');
    await queryInterface.dropTable('employee_personal');
  },
};
