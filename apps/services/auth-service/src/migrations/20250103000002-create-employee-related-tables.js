'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    console.log('Running migration: create-employee-related-tables');

    // Create employee_emergency_contact table
    await queryInterface.createTable('employee_emergency_contact', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.BIGINT,
      },
      userId: {
        type: Sequelize.BIGINT,
        allowNull: false,
        references: {
          model: 'employee_personal',
          key: 'userId',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      category: {
        type: Sequelize.STRING(50),
        allowNull: true,
      },
      name: {
        type: Sequelize.STRING(100),
        allowNull: true,
      },
      relationship: {
        type: Sequelize.STRING(50),
        allowNull: true,
      },
      address: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      phoneNumber: {
        type: Sequelize.STRING(20),
        allowNull: true,
      },
      email: {
        type: Sequelize.STRING(100),
        allowNull: true,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });

    // Create employee_identity_doc table
    await queryInterface.createTable('employee_identity_doc', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.BIGINT,
      },
      userId: {
        type: Sequelize.BIGINT,
        allowNull: false,
        references: {
          model: 'employee_personal',
          key: 'userId',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      docType: {
        type: Sequelize.STRING(50),
        allowNull: true,
      },
      nationality: {
        type: Sequelize.STRING(100),
        allowNull: true,
      },
      issueDate: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      expiryDate: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });

    // Create employee_bank_account table
    await queryInterface.createTable('employee_bank_account', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.BIGINT,
      },
      userId: {
        type: Sequelize.BIGINT,
        allowNull: false,
        references: {
          model: 'employee_personal',
          key: 'userId',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      accountHolder: {
        type: Sequelize.STRING(200),
        allowNull: true,
      },
      accountNumber: {
        type: Sequelize.STRING(100),
        allowNull: true,
      },
      bankName: {
        type: Sequelize.STRING(200),
        allowNull: true,
      },
      branchName: {
        type: Sequelize.STRING(200),
        allowNull: true,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });

    // Add indexes for better performance
    await queryInterface.addIndex('employee_personal', ['userId'], {
      name: 'idx_employee_personal_user_id',
    });

    await queryInterface.addIndex('employee_address', ['userId'], {
      name: 'idx_employee_address_user_id',
    });

    await queryInterface.addIndex('employee_emergency_contact', ['userId'], {
      name: 'idx_employee_emergency_contact_user_id',
    });

    await queryInterface.addIndex('employee_identity_doc', ['userId'], {
      name: 'idx_employee_identity_doc_user_id',
    });

    await queryInterface.addIndex('employee_bank_account', ['userId'], {
      name: 'idx_employee_bank_account_user_id',
    });

    console.log('Migration completed: create-employee-related-tables');
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('employee_bank_account');
    await queryInterface.dropTable('employee_identity_doc');
    await queryInterface.dropTable('employee_emergency_contact');
  },
};
