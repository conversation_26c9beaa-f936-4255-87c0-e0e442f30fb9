import { Test, TestingModule } from '@nestjs/testing';
import { EmployeeService } from './employee.service';
import { EmployeePersonal } from './models/employee-personal.model';
import { EmployeeDepartment } from './models/employee-department.model';
import { EmployeeAddress } from './models/employee-address.model';
import { EmployeeEmergencyContact } from './models/employee-emergency-contact.model';
import { EmployeeIdentityDoc } from './models/employee-identity-doc.model';
import { EmployeeBankAccount } from './models/employee-bank-account.model';
import { EmployeeSocialLinks } from './models/employee-social-links.model';
import { User } from '../user/model/user.model';
import { Role } from '../user/model/role.model';
import { UserRole } from '../user/model/user-role.model';
import { Department } from '../user/model/department.model';
import { UserDepartment } from '../user/model/user‐department.model';
import { Organization } from '../organization/organization.model';
import { AuditClientService } from '../audit/audit.service';
import { AppService } from '../app.service';
import { getModelToken } from '@nestjs/sequelize';

describe('EmployeeService', () => {
  let service: EmployeeService;
  let employeeModel: typeof EmployeePersonal;
  let employeeDepartmentModel: typeof EmployeeDepartment;

  // Mock data
  const mockEmployee = {
    userId: 1,
    firstName: 'John',
    lastName: 'Doe',
    phone: '******-0123',
    dateOfBirth: new Date('1990-01-01'),
    bloodGroup: 'O+',
    gender: 'male',
    nationality: 'American',
    maritalStatus: 'single',
    joiningDate: new Date('2024-01-01'),
    jobType: 'full-time',
    jobStatus: 'active',
    agencyId: 1,
    orgId: '1',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockEmployeeDepartment = {
    userId: 1,
    departmentId: 1,
    employeeId: 'EMP001',
    department: 'Engineering',
    designation: 'Senior Developer',
    supervisor: 'Jane Smith',
    workLocation: 'New York Office',
  };

  beforeEach(async () => {
    const mockEmployeeModel = {
      findByPk: jest.fn(),
      create: jest.fn(),
      findAll: jest.fn(),
    };

    const mockEmployeeDepartmentModel = {
      findOne: jest.fn(),
      create: jest.fn(),
    };

    const mockAuditService = {
      createAuditLog: jest.fn(),
    };

    const mockAppService = {
      trackAuthorization: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EmployeeService,
        {
          provide: getModelToken(EmployeePersonal),
          useValue: mockEmployeeModel,
        },
        {
          provide: getModelToken(EmployeeDepartment),
          useValue: mockEmployeeDepartmentModel,
        },
        {
          provide: getModelToken(EmployeeAddress),
          useValue: { findAll: jest.fn(), create: jest.fn() },
        },
        {
          provide: getModelToken(EmployeeEmergencyContact),
          useValue: { findAll: jest.fn(), create: jest.fn() },
        },
        {
          provide: getModelToken(EmployeeIdentityDoc),
          useValue: { findAll: jest.fn(), create: jest.fn() },
        },
        {
          provide: getModelToken(EmployeeBankAccount),
          useValue: { findAll: jest.fn(), create: jest.fn() },
        },
        {
          provide: getModelToken(EmployeeSocialLinks),
          useValue: { findAll: jest.fn(), create: jest.fn() },
        },
        {
          provide: getModelToken(User),
          useValue: { findByPk: jest.fn(), create: jest.fn(), sequelize: { transaction: jest.fn() } },
        },
        {
          provide: getModelToken(Role),
          useValue: { findOne: jest.fn(), create: jest.fn() },
        },
        {
          provide: getModelToken(UserRole),
          useValue: { create: jest.fn() },
        },
        {
          provide: getModelToken(Department),
          useValue: { findOne: jest.fn(), create: jest.fn() },
        },
        {
          provide: getModelToken(UserDepartment),
          useValue: { create: jest.fn() },
        },
        {
          provide: getModelToken(Organization),
          useValue: { findOne: jest.fn(), create: jest.fn(), findByPk: jest.fn() },
        },
        {
          provide: AuditClientService,
          useValue: mockAuditService,
        },
        {
          provide: AppService,
          useValue: mockAppService,
        },
      ],
    }).compile();

    service = module.get<EmployeeService>(EmployeeService);
    employeeModel = module.get<typeof EmployeePersonal>(getModelToken(EmployeePersonal));
    employeeDepartmentModel = module.get<typeof EmployeeDepartment>(getModelToken(EmployeeDepartment));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('mapToEmployeeInfo', () => {
    it('should map employee with department information correctly', async () => {
      // Mock the employee with basic info
      const mockEmployeeWithRelations = {
        ...mockEmployee,
        addresses: [],
        emergencyContacts: [],
        identityDocs: [],
        bankAccounts: [],
        socialLinks: [],
        employeeDepartment: null,
      };

      // Mock the department lookup
      employeeDepartmentModel.findOne = jest.fn().mockResolvedValue(mockEmployeeDepartment);

      const result = await service.mapToEmployeeInfo(mockEmployeeWithRelations as any);

      expect(result).toEqual({
        userId: BigInt(1),
        firstName: 'John',
        lastName: 'Doe',
        phone: '******-0123',
        dateOfBirth: mockEmployee.dateOfBirth.toISOString(),
        bloodGroup: 'O+',
        gender: 'male',
        nationality: 'American',
        maritalStatus: 'single',
        joiningDate: mockEmployee.joiningDate.toISOString(),
        jobType: 'full-time',
        jobStatus: 'active',
        employeeId: 'EMP001',
        department: 'Engineering',
        designation: 'Senior Developer',
        supervisor: 'Jane Smith',
        workLocation: 'New York Office',
        identityInfo: [],
        socialLinks: [],
        organizationId: undefined,
        createdAt: mockEmployee.createdAt,
        updatedAt: mockEmployee.updatedAt,
      });

      expect(employeeDepartmentModel.findOne).toHaveBeenCalledWith({
        where: { userId: 1 },
        include: [{ model: expect.anything(), as: 'departmentRef' }],
      });
    });

    it('should handle employee without department information', async () => {
      const mockEmployeeWithRelations = {
        ...mockEmployee,
        addresses: [],
        emergencyContacts: [],
        identityDocs: [],
        bankAccounts: [],
        socialLinks: [],
        employeeDepartment: null,
      };

      // Mock no department found
      employeeDepartmentModel.findOne = jest.fn().mockResolvedValue(null);

      const result = await service.mapToEmployeeInfo(mockEmployeeWithRelations as any);

      expect(result.employeeId).toBeUndefined();
      expect(result.department).toBeUndefined();
      expect(result.designation).toBeUndefined();
      expect(result.supervisor).toBeUndefined();
      expect(result.workLocation).toBeUndefined();
    });
  });
});
