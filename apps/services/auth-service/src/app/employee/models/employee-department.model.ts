import { BaseModel } from '@apply-goal-backend/database';
import {
  Column,
  DataType,
  ForeignKey,
  Table,
  BelongsTo,
} from 'sequelize-typescript';
import { EmployeePersonal } from './employee-personal.model';
import { Department } from '../../user/model/department.model';

@Table({ tableName: 'employee_department' })
export class EmployeeDepartment extends BaseModel {
  @ForeignKey(() => EmployeePersonal)
  @Column(DataType.BIGINT)
  userId!: number;

  @ForeignKey(() => Department)
  @Column(DataType.BIGINT)
  departmentId!: number;

  @Column(DataType.STRING(50))
  employeeId?: string;

  @Column(DataType.STRING(100))
  department?: string;

  @Column(DataType.STRING(100))
  designation?: string;

  @Column(DataType.STRING(100))
  supervisor?: string;

  @Column(DataType.STRING(200))
  workLocation?: string;

  @BelongsTo(() => EmployeePersonal)
  employee?: EmployeePersonal;

  @BelongsTo(() => Department)
  departmentRef?: Department;
}
