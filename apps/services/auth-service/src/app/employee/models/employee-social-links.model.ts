import { BaseModel } from '@apply-goal-backend/database';
import {
  Column,
  DataType,
  ForeignKey,
  Table,
  BelongsTo,
} from 'sequelize-typescript';
import { EmployeePersonal } from './employee-personal.model';

@Table({ tableName: 'employee_social_link' })
export class EmployeeSocialLinks extends BaseModel {
  @ForeignKey(() => EmployeePersonal)
  @Column(DataType.BIGINT)
  userId!: number;

  @Column(DataType.STRING(50))
  platform?: string;

  @Column(DataType.TEXT)
  url?: string;

  @BelongsTo(() => EmployeePersonal)
  employee?: EmployeePersonal;
}
