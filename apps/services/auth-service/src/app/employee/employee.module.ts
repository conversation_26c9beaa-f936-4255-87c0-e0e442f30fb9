import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { EmployeePersonal } from './models/employee-personal.model';
import { EmployeeAddress } from './models/employee-address.model';
import { EmployeeEmergencyContact } from './models/employee-emergency-contact.model';
import { EmployeeIdentityDoc } from './models/employee-identity-doc.model';
import { EmployeeBankAccount } from './models/employee-bank-account.model';
import { EmployeeService } from './employee.service';
import { EmployeeController } from './employee.controller';
import { AuditModule } from '../audit/audit.module';
import { AppService } from '../app.service';
import { MetricsModule } from '../metrics/metrics.module';
import { EmployeeSocialLinks } from './models/employee-social-links.model';
import { EmployeeDepartment } from './models/employee-department.model';

@Module({
  imports: [
    SequelizeModule.forFeature([
      EmployeePersonal,
      EmployeeAddress,
      EmployeeEmergencyContact,
      EmployeeIdentityDoc,
      EmployeeBankAccount,
      EmployeeSocialLinks,
      EmployeeDepartment,
    ]),
    AuditModule,
    MetricsModule,
  ],
  providers: [EmployeeService, AppService],
  controllers: [EmployeeController],
  exports: [SequelizeModule, EmployeeService],
})
export class EmployeeModule {}
