import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { User } from './model/user.model';
import { UserRole } from './model/user-role.model';
import { Role } from './model/role.model';
import { UserController } from './user.controller';
import { UserService } from './user.service';
import { AuditModule } from '../audit/audit.module';
import { Token } from '../auth/token.model';
import { SocialSite } from './model/social-site.model';
import { DatabaseModule } from '../migration/database.module';
import { Module as ModuleModel } from './model/module.model'; // Module
import { Feature } from './model/feature.model'; // Feature
import { SubFeature } from './model/sub-feature.model'; // SubFeature
import { RoleFeaturePermission } from './model/role-feature-permission.model';
import { RoleSubFeaturePermission } from './model/role-sub-feature-permission.model';
import { Department } from './model/department.model';
import { UserDepartment } from './model/user‐department.model';
import { DepartmentFeaturePermission } from './model/department-feature-permission.model';
import { DepartmentSubFeaturePermission } from './model/department-sub-feature-permission.model';

@Module({
  imports: [
    DatabaseModule,
    SequelizeModule.forFeature([
      Feature,
      SubFeature,
      Department,
      UserDepartment,
      DepartmentFeaturePermission,
      DepartmentSubFeaturePermission,
      ModuleModel,
      Role,
      RoleFeaturePermission,
      RoleSubFeaturePermission,
      User,
      UserRole,
      Token,
      SocialSite,
    ]),
    AuditModule,
  ],
  controllers: [UserController],
  providers: [UserService],
  exports: [SequelizeModule, UserService],
})
export class UserModule {}
