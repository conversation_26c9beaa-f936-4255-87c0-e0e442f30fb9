import {
  Column,
  Model,
  Table,
  DataType,
  ForeignKey,
  BelongsTo,
  PrimaryKey,
  AutoIncrement,
} from 'sequelize-typescript';
import { User } from './user.model';
import { BaseModel } from '@apply-goal-backend/database';

@Table({
  tableName: 'social_sites',
  timestamps: true,
})
export class SocialSite extends BaseModel {
  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  domain: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  url: string;

  @ForeignKey(() => User)
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    field: 'userId',
  })
  userId: bigint;

  @BelongsTo(() => User)
  user: User;
}
