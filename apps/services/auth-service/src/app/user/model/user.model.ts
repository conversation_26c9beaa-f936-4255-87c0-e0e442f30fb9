import {
  Column,
  Model,
  Table,
  DataType,
  HasMany,
  BelongsToMany,
  ForeignKey,
  BelongsTo,
  PrimaryKey,
  AutoIncrement,
} from 'sequelize-typescript';
import { Role } from './role.model';
import { UserRole } from './user-role.model';
import { SocialSite } from './social-site.model';
import { BaseModel } from '@apply-goal-backend/database';
import { Department } from './department.model';
import { UserDepartment } from './user‐department.model';

@Table({
  tableName: 'users',
  timestamps: true,
})
export class User extends BaseModel {
  @Column({
    type: DataType.STRING,
    allowNull: false,
    field: 'name',
  })
  name: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    unique: true,
  })
  email: string;

  @Column({
    type: DataType.STRING,
  })
  password: string;

  @Column(DataType.STRING)
  phone: string;

  @Column({
    type: DataType.STRING,
    field: 'socialLink',
  })
  socialLink: string;

  @ForeignKey(() => Role)
  @Column({
    type: DataType.BIGINT,
  })
  roleId: bigint;

  @BelongsTo(() => Role)
  role: Role;

  @Column(DataType.STRING)
  gender: string;

  @Column(DataType.STRING)
  nationality: string;

  @Column({
    type: DataType.STRING,
  })
  presentAddress: string;

  @Column({
    type: DataType.STRING,
  })
  presentCountry: string;

  @Column({
    type: DataType.STRING,
  })
  presentState: string;

  @Column({
    type: DataType.STRING,
  })
  presentCity: string;

  @Column({
    type: DataType.STRING,
  })
  presentAddressZipcode: string;

  @Column({
    type: DataType.STRING,
  })
  permanentAddress: string;

  @Column({
    type: DataType.STRING,
  })
  permanentCountry: string;

  @Column({
    type: DataType.STRING,
  })
  permanentState: string;

  @Column({
    type: DataType.STRING,
  })
  permanentCity: string;

  @Column({
    type: DataType.STRING,
  })
  permanentAddressZipcode: string;

  @Column({
    type: DataType.ENUM('active', 'inactive', 'pending', 'suspended'),
    field: 'status',
    defaultValue: 'inactive',
  })
  status: string;

  // ── User ↔ Department (M:N)
  @BelongsToMany(() => Department, {
    through: () => UserDepartment,
    foreignKey: 'userId',
    otherKey: 'departmentId',
    as: 'departments',
  })
  departments: Department[];
  @HasMany(() => SocialSite)
  socialSites: SocialSite[];

  @BelongsToMany(
    () => Role,
    () => UserRole,
    'userId', // FK in user_roles pointing at User
    'roleId'
  )
  roles: Role[];
}
