import { Injectable, OnModuleInit, Logger } from '@nestjs/common';
import { Client, ClientGrpc } from '@nestjs/microservices';
import { Transport } from '@nestjs/microservices';
import { join } from 'path';
import { Observable, throwError } from 'rxjs';
import { catchError, timeout, retry } from 'rxjs/operators';

interface AuditLog {
  id: string;
  userId: string;
  serviceName: string;
  action: string;
  resourceType: string;
  resourceId: string;
  description: string;
  metadata: { [key: string]: string };
  ipAddress: string;
  userAgent: string;
  createdAt: number;
}

interface CreateAuditLogRequest {
  userId: string;
  serviceName: string;
  action: string;
  resourceType: string;
  resourceId: string;
  description: string;
  metadata?: { [key: string]: string };
  ipAddress?: string;
  userAgent?: string;
}

interface GetAuditLogRequest {
  id: string;
}

interface AuditLogResponse {
  log: AuditLog;
}

interface AuditService {
  createAuditLog(request: CreateAuditLogRequest): Observable<AuditLogResponse>;
  getAuditLog(request: GetAuditLogRequest): Observable<AuditLogResponse>;
}

@Injectable()
export class AuditClientService implements OnModuleInit {
  private readonly logger = new Logger(AuditClientService.name);
  @Client({
    transport: Transport.GRPC,
    options: {
      package: 'audit',
      protoPath: join(
        process.cwd(),
        'libs/shared/dto/src/lib/audit/audit.proto'
      ),
      // url: 'localhost:50051',
      url: process.env.AUDIT_LOG_SERVICE_URL || 'audit-logging:50051',
      loader: {
        keepCase: true,
        longs: String,
        enums: String,
        defaults: true,
        oneofs: true,
      },
    },
  })
  private readonly client: ClientGrpc;

  private auditService: AuditService;

  onModuleInit() {
    this.auditService = this.client.getService<AuditService>('AuditService');
  }

  private handleError(operation = 'operation') {
    return (error: any) => {
      this.logger.error(`${operation} failed: ${error.message}`, error.stack);
      return throwError(() => error);
    };
  }

  createAuditLog(request: CreateAuditLogRequest): Observable<AuditLogResponse> {
    return this.auditService
      .createAuditLog(request)
      .pipe(
        timeout(5000),
        retry(3),
        catchError(this.handleError('createAuditLog'))
      );
  }

  getAuditLog(request: GetAuditLogRequest): Observable<AuditLogResponse> {
    return this.auditService
      .getAuditLog(request)
      .pipe(
        timeout(5000),
        retry(3),
        catchError(this.handleError('getAuditLog'))
      );
  }
}
