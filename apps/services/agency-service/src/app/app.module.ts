import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { MetricsController } from './metrics.controller';
import { AuditClientService } from './audit.service';
import { MonitoringModule } from '@apply-goal-backend/monitoring';
import { HealthController } from './health.controller';
// import { DatabaseModule } from '@apply-goal-backend/database';
import { SequelizeModule } from '@nestjs/sequelize';
import { AgencyModule } from './agency/agency.module';
import { BranchModule } from './branch/branch.module';
import { ContactModule } from './contact/contact.module';
import { Agency } from './agency/agency.model';
import { AgencyBranch } from './branch/agency-branch.model';
import { AgencyContact } from './contact/agency-contact.model';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    // DatabaseModule,
    SequelizeModule.forFeature([
      Agency,
      AgencyBranch,
      AgencyContact,
    ]),
    MonitoringModule.forRoot({
      metrics: {
        serviceName: 'agency-service',
        serviceVersion: '1.0.0',
        port: parseInt(process.env.METRICS_PORT || '5001', 10),
        path: '/metrics',
        labels: {
          environment: process.env.NODE_ENV || 'development'
        }
      },
      tracing: {
        serviceName: 'agency-service',
        jaegerEndpoint: 'http://jaeger:4318/v1/traces'
      }
    }),
    AgencyModule,
    BranchModule,
    ContactModule,
  ],
  controllers: [AppController, MetricsController, HealthController],
  providers: [AppService, AuditClientService],
})
export class AppModule {}
