import { Controller, Logger } from '@nestjs/common';
import { GrpcMethod } from '@nestjs/microservices';
import { BranchService } from './branch.service';

@Controller()
export class BranchGrpcController {
  private readonly logger = new Logger(BranchGrpcController.name);

  constructor(private readonly branchService: BranchService) {}

  @GrpcMethod('AgencyService', 'CreateAgencyBranch')
  async createAgencyBranch(data: any) {
    try {
      this.logger.log(`Creating agency branches for agency: ${data.agencyId}`);
      const result = await this.branchService.createAgencyBranch(data);
      
      return {
        status: 200,
        message: 'Agency branches created successfully',
        data: result,
        error: null,
      };
    } catch (error) {
      this.logger.error('Failed to create agency branch', error.stack);
      return {
        status: error.status || 500,
        message: 'Failed to create agency branch',
        data: [],
        error: {
          code: 'INTERNAL_ERROR',
          message: error.message,
          details: error.stack,
          validationErrors: [],
        },
      };
    }
  }

  @GrpcMethod('AgencyService', 'GetAgencyBranch')
  async getAgencyBranch(data: any) {
    try {
      this.logger.log(`Getting agency branch with ID: ${data.id}`);
      const result = await this.branchService.getAgencyBranch(data.id);
      
      return {
        status: 200,
        message: 'Agency branch retrieved successfully',
        data: result,
        error: null,
      };
    } catch (error) {
      this.logger.error('Failed to get agency branch', error.stack);
      return {
        status: error.status || 500,
        message: 'Failed to get agency branch',
        data: null,
        error: {
          code: 'INTERNAL_ERROR',
          message: error.message,
          details: error.stack,
          validationErrors: [],
        },
      };
    }
  }

  @GrpcMethod('AgencyService', 'UpdateAgencyBranch')
  async updateAgencyBranch(data: any) {
    try {
      this.logger.log(`Updating agency branches for agency: ${data.agencyId}`);
      const result = await this.branchService.updateAgencyBranch(data);
      
      return {
        status: 200,
        message: 'Agency branches updated successfully',
        data: result,
        error: null,
      };
    } catch (error) {
      this.logger.error('Failed to update agency branch', error.stack);
      return {
        status: error.status || 500,
        message: 'Failed to update agency branch',
        data: [],
        error: {
          code: 'INTERNAL_ERROR',
          message: error.message,
          details: error.stack,
          validationErrors: [],
        },
      };
    }
  }

  @GrpcMethod('AgencyService', 'DeleteAgencyBranch')
  async deleteAgencyBranch(data: any) {
    try {
      this.logger.log(`Deleting agency branch with ID: ${data.id}`);
      await this.branchService.deleteAgencyBranch(data.id);
      
      return {
        status: 200,
        message: 'Agency branch deleted successfully',
        error: null,
      };
    } catch (error) {
      this.logger.error('Failed to delete agency branch', error.stack);
      return {
        status: error.status || 500,
        message: 'Failed to delete agency branch',
        error: {
          code: 'INTERNAL_ERROR',
          message: error.message,
          details: error.stack,
          validationErrors: [],
        },
      };
    }
  }

  @GrpcMethod('AgencyService', 'ListAgencyBranches')
  async listAgencyBranches(data: any) {
    try {
      this.logger.log(`Listing agency branches for agency: ${data.agencyId}`);
      const result = await this.branchService.listAgencyBranches(data);
      
      return {
        status: 200,
        message: 'Agency branches retrieved successfully',
        data: result.data,
        error: null,
      };
    } catch (error) {
      this.logger.error('Failed to list agency branches', error.stack);
      return {
        status: error.status || 500,
        message: 'Failed to list agency branches',
        data: [],
        error: {
          code: 'INTERNAL_ERROR',
          message: error.message,
          details: error.stack,
          validationErrors: [],
        },
      };
    }
  }
}
