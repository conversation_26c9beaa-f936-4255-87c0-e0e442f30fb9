import { Injectable, NotFoundException, Logger, InternalServerErrorException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { firstValueFrom } from 'rxjs';
import { Transaction } from 'sequelize';
import { AgencyBranch } from './agency-branch.model';
import { Agency } from '../agency/agency.model';
import { AuditClientService } from '../audit.service';

@Injectable()
export class BranchService {
  private readonly logger = new Logger(BranchService.name);

  constructor(
    @InjectModel(AgencyBranch)
    private readonly branchModel: typeof AgencyBranch,
    @InjectModel(Agency)
    private readonly agencyModel: typeof Agency,
    private readonly auditService: AuditClientService
  ) {}

  async createAgencyBranch(data: any) {
    const transaction = await this.branchModel.sequelize.transaction();

    try {
      // Verify agency exists
      const agency = await this.agencyModel.findByPk(data.agencyId, { transaction });
      if (!agency) {
        await transaction.rollback();
        throw new NotFoundException(`Agency with ID "${data.agencyId}" not found`);
      }

      const branches = [];
      for (const branchData of data.data) {
        try {
          const branch = await this.branchModel.create({
            agencyId: data.agencyId,
            branchName: branchData.branchName,
            address: branchData.address,
            country: branchData.country,
            state: branchData.state,
            city: branchData.city,
            postalCode: branchData.postalCode,
            managerContactNumber: branchData.managerContactNumber,
            email: branchData.email,
          }, { transaction });
          branches.push(branch);
        } catch (branchError) {
          await transaction.rollback();
          throw new InternalServerErrorException(
            `Failed to create branch "${branchData.branchName}": ${branchError.message}`
          );
        }
      }

      // Commit the transaction
      await transaction.commit();

      // Log audit
      try {
        const auditObservable = this.auditService.createAuditLog({
          userId: parseInt(data.userId) || 0,
          userRole: data.userRole || 'unknown',
          actions: 'CREATE_AGENCY_BRANCH',
          serviceName: 'agency-service',
          resourceType: 'AgencyBranch',
          resourceId: Number(branches[0]?.id || 0),
          description: `Created ${branches.length} agency branches for agency ${data.agencyId}`,
          metadata: {
            agencyId: data.agencyId,
            branchCount: branches.length.toString(),
          },
          source: 'agency-service',
        });
        await firstValueFrom(auditObservable);
      } catch (auditError) {
        this.logger.warn('Failed to log audit', auditError);
      }

      return branches;
    } catch (error) {
      // Ensure transaction is rolled back if not already done
      try {
        await transaction.rollback();
      } catch (rollbackError) {
        this.logger.warn('Transaction rollback failed (might already be finished)', rollbackError.message);
      }

      this.logger.error('Failed to create agency branch', error.stack);
      throw error;
    }
  }

  async getAgencyBranch(id: string) {
    try {
      const branch = await this.branchModel.findByPk(id, {
        include: [Agency],
      });

      if (!branch) {
        throw new NotFoundException('Agency branch not found');
      }

      return branch;
    } catch (error) {
      this.logger.error('Failed to get agency branch', error.stack);
      throw error;
    }
  }

  async updateAgencyBranch(data: any) {
    const transaction = await this.branchModel.sequelize.transaction();

    try {
      // Verify agency exists
      const agency = await this.agencyModel.findByPk(data.agencyId, { transaction });
      if (!agency) {
        await transaction.rollback();
        throw new NotFoundException(`Agency with ID "${data.agencyId}" not found`);
      }

      const updatedBranches = [];
      for (const branchData of data.data) {
        const branch = await this.branchModel.findByPk(branchData.id, { transaction });
        if (!branch) {
          await transaction.rollback();
          throw new NotFoundException(`Agency branch with ID "${branchData.id}" not found`);
        }

        // Verify branch belongs to the agency
        if (branch.agencyId !== parseInt(data.agencyId)) {
          await transaction.rollback();
          throw new NotFoundException(`Branch "${branchData.id}" does not belong to agency "${data.agencyId}"`);
        }

        try {
          await branch.update({
            branchName: branchData.branchName,
            address: branchData.address,
            country: branchData.country,
            state: branchData.state,
            city: branchData.city,
            postalCode: branchData.postalCode,
            managerContactNumber: branchData.managerContactNumber,
            email: branchData.email,
          }, { transaction });

          updatedBranches.push(branch);
        } catch (updateError) {
          await transaction.rollback();
          throw new InternalServerErrorException(
            `Failed to update branch "${branchData.branchName}": ${updateError.message}`
          );
        }
      }

      // Commit the transaction
      await transaction.commit();

      // Log audit
      try {
        const auditObservable = this.auditService.createAuditLog({
          userId: parseInt(data.userId) || 0,
          userRole: data.userRole || 'unknown',
          actions: 'UPDATE_AGENCY_BRANCH',
          serviceName: 'agency-service',
          resourceType: 'AgencyBranch',
          resourceId: Number(updatedBranches[0]?.id || 0),
          description: `Updated ${updatedBranches.length} agency branches for agency ${data.agencyId}`,
          metadata: {
            agencyId: data.agencyId,
            branchCount: updatedBranches.length.toString(),
          },
          source: 'agency-service',
        });
        await firstValueFrom(auditObservable);
      } catch (auditError) {
        this.logger.warn('Failed to log audit', auditError);
      }

      return updatedBranches;
    } catch (error) {
      // Ensure transaction is rolled back if not already done
      try {
        await transaction.rollback();
      } catch (rollbackError) {
        this.logger.warn('Transaction rollback failed (might already be finished)', rollbackError.message);
      }

      this.logger.error('Failed to update agency branch', error.stack);
      throw error;
    }
  }

  async deleteAgencyBranch(id: string) {
    try {
      const branch = await this.branchModel.findByPk(id);
      if (!branch) {
        throw new NotFoundException('Agency branch not found');
      }

      await branch.destroy();

      return { success: true };
    } catch (error) {
      this.logger.error('Failed to delete agency branch', error.stack);
      throw error;
    }
  }

  async listAgencyBranches(data: any) {
    try {
      const { agencyId, page = 1, pageSize = 10 } = data;
      const offset = (page - 1) * pageSize;

      // Verify agency exists
      const agency = await this.agencyModel.findByPk(agencyId);
      if (!agency) {
        throw new NotFoundException('Agency not found');
      }

      const { rows: branches, count: total } = await this.branchModel.findAndCountAll({
        where: { agencyId },
        limit: pageSize,
        offset,
        order: [['createdAt', 'DESC']],
      });

      return {
        data: branches,
        total,
        page,
        pageSize,
      };
    } catch (error) {
      this.logger.error('Failed to list agency branches', error.stack);
      throw error;
    }
  }
}
