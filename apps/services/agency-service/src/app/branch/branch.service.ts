import { Injectable, NotFoundException, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { AgencyBranch } from './agency-branch.model';
import { Agency } from '../agency/agency.model';
import { AuditClientService } from '../audit.service';

@Injectable()
export class BranchService {
  private readonly logger = new Logger(BranchService.name);

  constructor(
    @InjectModel(AgencyBranch)
    private readonly branchModel: typeof AgencyBranch,
    @InjectModel(Agency)
    private readonly agencyModel: typeof Agency,
    private readonly auditService: AuditClientService
  ) {}

  async createAgencyBranch(data: any) {
    try {
      // Verify agency exists
      const agency = await this.agencyModel.findByPk(data.agencyId);
      if (!agency) {
        throw new NotFoundException('Agency not found');
      }

      const branches = [];
      for (const branchData of data.data) {
        const branch = await this.branchModel.create({
          agencyId: data.agencyId,
          branchName: branchData.branchName,
          address: branchData.address,
          country: branchData.country,
          state: branchData.state,
          city: branchData.city,
          postalCode: branchData.postalCode,
          managerContactNumber: branchData.managerContactNumber,
          email: branchData.email,
        });
        branches.push(branch);
      }

      // Log audit
      await this.auditService.logAction({
        action: 'CREATE_AGENCY_BRANCH',
        entityType: 'AgencyBranch',
        entityId: branches.map(b => b.id).join(','),
        userId: data.userId,
        userRole: data.userRole,
        details: data,
      });

      return branches;
    } catch (error) {
      this.logger.error('Failed to create agency branch', error.stack);
      throw error;
    }
  }

  async getAgencyBranch(id: string) {
    try {
      const branch = await this.branchModel.findByPk(id, {
        include: [Agency],
      });

      if (!branch) {
        throw new NotFoundException('Agency branch not found');
      }

      return branch;
    } catch (error) {
      this.logger.error('Failed to get agency branch', error.stack);
      throw error;
    }
  }

  async updateAgencyBranch(data: any) {
    try {
      // Verify agency exists
      const agency = await this.agencyModel.findByPk(data.agencyId);
      if (!agency) {
        throw new NotFoundException('Agency not found');
      }

      const updatedBranches = [];
      for (const branchData of data.data) {
        const branch = await this.branchModel.findByPk(branchData.id);
        if (!branch) {
          throw new NotFoundException(`Agency branch with ID ${branchData.id} not found`);
        }

        await branch.update({
          branchName: branchData.branchName,
          address: branchData.address,
          country: branchData.country,
          state: branchData.state,
          city: branchData.city,
          postalCode: branchData.postalCode,
          managerContactNumber: branchData.managerContactNumber,
          email: branchData.email,
        });

        updatedBranches.push(branch);
      }

      // Log audit
      await this.auditService.logAction({
        action: 'UPDATE_AGENCY_BRANCH',
        entityType: 'AgencyBranch',
        entityId: updatedBranches.map(b => b.id).join(','),
        userId: data.userId,
        userRole: data.userRole,
        details: data,
      });

      return updatedBranches;
    } catch (error) {
      this.logger.error('Failed to update agency branch', error.stack);
      throw error;
    }
  }

  async deleteAgencyBranch(id: string) {
    try {
      const branch = await this.branchModel.findByPk(id);
      if (!branch) {
        throw new NotFoundException('Agency branch not found');
      }

      await branch.destroy();

      return { success: true };
    } catch (error) {
      this.logger.error('Failed to delete agency branch', error.stack);
      throw error;
    }
  }

  async listAgencyBranches(data: any) {
    try {
      const { agencyId, page = 1, pageSize = 10 } = data;
      const offset = (page - 1) * pageSize;

      // Verify agency exists
      const agency = await this.agencyModel.findByPk(agencyId);
      if (!agency) {
        throw new NotFoundException('Agency not found');
      }

      const { rows: branches, count: total } = await this.branchModel.findAndCountAll({
        where: { agencyId },
        limit: pageSize,
        offset,
        order: [['createdAt', 'DESC']],
      });

      return {
        data: branches,
        total,
        page,
        pageSize,
      };
    } catch (error) {
      this.logger.error('Failed to list agency branches', error.stack);
      throw error;
    }
  }
}
