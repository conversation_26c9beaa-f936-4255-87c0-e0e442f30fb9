import {
  Table,
  Column,
  DataType,
  Model,
  PrimaryKey,
  AutoIncrement,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { Agency } from '../agency/agency.model';

@Table({
  tableName: 'agency_branches',
  timestamps: true,
})
export class AgencyBranch extends BaseModel {
  @ForeignKey(() => Agency)
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  agencyId!: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  branchName!: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  address!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  country!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  state!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  city!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  postalCode!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  managerContactNumber!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  email!: string;

  // Associations
  @BelongsTo(() => Agency)
  agency!: Agency;
}
