import { Modu<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { BranchService } from './branch.service';
import { BranchGrpcController } from './branch.grpc.controller';
import { AgencyBranch } from './agency-branch.model';
import { Agency } from '../agency/agency.model';
import { AuditClientService } from '../audit.service';

@Module({
  imports: [SequelizeModule.forFeature([AgencyBranch, Agency])],
  controllers: [BranchGrpcController],
  providers: [BranchService, AuditClientService],
  exports: [BranchService],
})
export class BranchModule {}
