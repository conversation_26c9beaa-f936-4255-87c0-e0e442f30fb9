import { Modu<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { ContactService } from './contact.service';
import { ContactGrpcController } from './contact.grpc.controller';
import { AgencyContact } from './agency-contact.model';
import { Agency } from '../agency/agency.model';
import { AuditClientService } from '../audit.service';

@Module({
  imports: [SequelizeModule.forFeature([AgencyContact, Agency])],
  controllers: [ContactGrpcController],
  providers: [ContactService, AuditClientService],
  exports: [ContactService],
})
export class ContactModule {}
