import { Injectable, NotFoundException, Logger, InternalServerErrorException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { firstValueFrom } from 'rxjs';
import { Transaction } from 'sequelize';
import { AgencyContact } from './agency-contact.model';
import { Agency } from '../agency/agency.model';
import { AuditClientService } from '../audit.service';

@Injectable()
export class ContactService {
  private readonly logger = new Logger(ContactService.name);

  constructor(
    @InjectModel(AgencyContact)
    private readonly contactModel: typeof AgencyContact,
    @InjectModel(Agency)
    private readonly agencyModel: typeof Agency,
    private readonly auditService: AuditClientService
  ) {}

  async createAgencyContact(data: any) {
    const transaction = await this.contactModel.sequelize.transaction();

    try {
      // Verify agency exists
      const agency = await this.agencyModel.findByPk(data.agencyId, { transaction });
      if (!agency) {
        await transaction.rollback();
        throw new NotFoundException(`Agency with ID "${data.agencyId}" not found`);
      }

      try {
        const contact = await this.contactModel.create({
          agencyId: data.agencyId,
          ownerName: data.ownerName,
          ownerContactNumber: data.ownerContactNumber,
          ownerAlternateContactNumber: data.ownerAlternateContactNumber,
          ownerEmail: data.ownerEmail,
          primaryPersonName: data.primaryPersonName,
          primaryPersonDesignation: data.primaryPersonDesignation,
          primaryContactNumber: data.primaryContactNumber,
          primaryAlternateContactNumber: data.primaryAlternateContactNumber,
          primaryEmail: data.primaryEmail,
        }, { transaction });

        // Commit the transaction
        await transaction.commit();

        // Log audit separately (don't rollback if audit fails)
        try {
          const auditObservable = this.auditService.createAuditLog({
            userId: parseInt(data.userId) || 0,
            userRole: data.userRole || 'unknown',
            actions: 'CREATE_AGENCY_CONTACT',
            serviceName: 'agency-service',
            resourceType: 'AgencyContact',
            resourceId: Number(contact.id),
            description: `Created agency contact for agency ${data.agencyId}`,
            metadata: {
              agencyId: data.agencyId,
              ownerName: data.ownerName,
              primaryPersonName: data.primaryPersonName,
            },
            source: 'agency-service',
          });
          await firstValueFrom(auditObservable);
        } catch (auditError) {
          this.logger.warn('Failed to log audit (contact creation was successful)', auditError);
        }

        return contact;
      } catch (createError) {
        await transaction.rollback();
        throw new InternalServerErrorException(
          `Failed to create agency contact: ${createError.message}`
        );
      }
    } catch (error) {
      // Ensure transaction is rolled back if not already done
      try {
        await transaction.rollback();
      } catch (rollbackError) {
        this.logger.warn('Transaction rollback failed (might already be finished)', rollbackError.message);
      }

      this.logger.error('Failed to create agency contact', error.stack);
      throw error;
    }
  }

  async getAgencyContact(id: string) {
    try {
      const contact = await this.contactModel.findByPk(id, {
        include: [Agency],
      });

      if (!contact) {
        throw new NotFoundException('Agency contact not found');
      }

      return contact;
    } catch (error) {
      this.logger.error('Failed to get agency contact', error.stack);
      throw error;
    }
  }

  async updateAgencyContact(data: any) {
    const transaction = await this.contactModel.sequelize.transaction();

    try {
      const contact = await this.contactModel.findByPk(data.id, { transaction });
      if (!contact) {
        await transaction.rollback();
        throw new NotFoundException(`Agency contact with ID "${data.id}" not found`);
      }

      const updateData: any = {};
      if (data.ownerName) updateData.ownerName = data.ownerName;
      if (data.ownerContactNumber) updateData.ownerContactNumber = data.ownerContactNumber;
      if (data.ownerAlternateContactNumber) updateData.ownerAlternateContactNumber = data.ownerAlternateContactNumber;
      if (data.ownerEmail) updateData.ownerEmail = data.ownerEmail;
      if (data.primaryPersonName) updateData.primaryPersonName = data.primaryPersonName;
      if (data.primaryPersonDesignation) updateData.primaryPersonDesignation = data.primaryPersonDesignation;
      if (data.primaryContactNumber) updateData.primaryContactNumber = data.primaryContactNumber;
      if (data.primaryAlternateContactNumber) updateData.primaryAlternateContactNumber = data.primaryAlternateContactNumber;
      if (data.primaryEmail) updateData.primaryEmail = data.primaryEmail;

      try {
        await contact.update(updateData, { transaction });

        // Commit the transaction
        await transaction.commit();
      } catch (updateError) {
        await transaction.rollback();
        throw new InternalServerErrorException(
          `Failed to update agency contact: ${updateError.message}`
        );
      }

      // Log audit
      try {
        const auditObservable = this.auditService.createAuditLog({
          userId: parseInt(data.userId) || 0,
          userRole: data.userRole || 'unknown',
          actions: 'UPDATE_AGENCY_CONTACT',
          serviceName: 'agency-service',
          resourceType: 'AgencyContact',
          resourceId: Number(contact.id),
          description: `Updated agency contact ${contact.id}`,
          metadata: {
            agencyId: contact.agencyId.toString(),
          },
          source: 'agency-service',
        });
        await firstValueFrom(auditObservable);
      } catch (auditError) {
        this.logger.warn('Failed to log audit', auditError);
      }

      return contact;
    } catch (error) {
      // Ensure transaction is rolled back if not already done
      try {
        await transaction.rollback();
      } catch (rollbackError) {
        this.logger.warn('Transaction rollback failed (might already be finished)', rollbackError.message);
      }

      this.logger.error('Failed to update agency contact', error.stack);
      throw error;
    }
  }

  async deleteAgencyContact(id: string) {
    try {
      const contact = await this.contactModel.findByPk(id);
      if (!contact) {
        throw new NotFoundException('Agency contact not found');
      }

      await contact.destroy();

      return { success: true };
    } catch (error) {
      this.logger.error('Failed to delete agency contact', error.stack);
      throw error;
    }
  }

  async listAgencyContacts(data: any) {
    try {
      const { agencyId, page = 1, pageSize = 10 } = data;
      const offset = (page - 1) * pageSize;

      // Verify agency exists
      const agency = await this.agencyModel.findByPk(agencyId);
      if (!agency) {
        throw new NotFoundException('Agency not found');
      }

      const { rows: contacts, count: total } = await this.contactModel.findAndCountAll({
        where: { agencyId },
        limit: pageSize,
        offset,
        order: [['createdAt', 'DESC']],
      });

      return {
        data: contacts,
        total,
        page,
        pageSize,
      };
    } catch (error) {
      this.logger.error('Failed to list agency contacts', error.stack);
      throw error;
    }
  }
}
