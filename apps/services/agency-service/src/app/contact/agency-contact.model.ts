import {
  Table,
  Column,
  DataType,
  Model,
  PrimaryKey,
  AutoIncrement,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { Agency } from '../agency/agency.model';

@Table({
  tableName: 'agency_contacts',
  timestamps: true,
})
export class AgencyContact extends BaseModel {
  @ForeignKey(() => Agency)
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  agencyId!: number;

  // Agency Owner Information
  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  ownerName!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  ownerContactNumber!: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  ownerAlternateContactNumber!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  ownerEmail!: string;

  // Primary Contact Information
  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  primaryPersonName!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  primaryPersonDesignation!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  primaryContactNumber!: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  primaryAlternateContactNumber!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  primaryEmail!: string;

  // Associations
  @BelongsTo(() => Agency)
  agency!: Agency;
}
