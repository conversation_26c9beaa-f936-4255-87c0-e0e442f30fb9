import { Controller, Logger } from '@nestjs/common';
import { GrpcMethod } from '@nestjs/microservices';
import { ContactService } from './contact.service';

@Controller()
export class ContactGrpcController {
  private readonly logger = new Logger(ContactGrpcController.name);

  constructor(private readonly contactService: ContactService) {}

  @GrpcMethod('AgencyService', 'CreateAgencyContact')
  async createAgencyContact(data: any) {
    try {
      this.logger.log(`Creating agency contact for agency: ${data.agencyId}`);
      const result = await this.contactService.createAgencyContact(data);
      
      return {
        status: 200,
        message: 'Agency contact created successfully',
        data: result,
        error: null,
      };
    } catch (error) {
      this.logger.error('Failed to create agency contact', error.stack);
      return {
        status: error.status || 500,
        message: 'Failed to create agency contact',
        data: null,
        error: {
          code: 'INTERNAL_ERROR',
          message: error.message,
          details: error.stack,
          validationErrors: [],
        },
      };
    }
  }

  @GrpcMethod('AgencyService', 'GetAgencyContact')
  async getAgencyContact(data: any) {
    try {
      this.logger.log(`Getting agency contact with ID: ${data.id}`);
      const result = await this.contactService.getAgencyContact(data.id);
      
      return {
        status: 200,
        message: 'Agency contact retrieved successfully',
        data: result,
        error: null,
      };
    } catch (error) {
      this.logger.error('Failed to get agency contact', error.stack);
      return {
        status: error.status || 500,
        message: 'Failed to get agency contact',
        data: null,
        error: {
          code: 'INTERNAL_ERROR',
          message: error.message,
          details: error.stack,
          validationErrors: [],
        },
      };
    }
  }

  @GrpcMethod('AgencyService', 'UpdateAgencyContact')
  async updateAgencyContact(data: any) {
    try {
      this.logger.log(`Updating agency contact with ID: ${data.id}`);
      const result = await this.contactService.updateAgencyContact(data);
      
      return {
        status: 200,
        message: 'Agency contact updated successfully',
        data: result,
        error: null,
      };
    } catch (error) {
      this.logger.error('Failed to update agency contact', error.stack);
      return {
        status: error.status || 500,
        message: 'Failed to update agency contact',
        data: null,
        error: {
          code: 'INTERNAL_ERROR',
          message: error.message,
          details: error.stack,
          validationErrors: [],
        },
      };
    }
  }

  @GrpcMethod('AgencyService', 'DeleteAgencyContact')
  async deleteAgencyContact(data: any) {
    try {
      this.logger.log(`Deleting agency contact with ID: ${data.id}`);
      await this.contactService.deleteAgencyContact(data.id);
      
      return {
        status: 200,
        message: 'Agency contact deleted successfully',
        error: null,
      };
    } catch (error) {
      this.logger.error('Failed to delete agency contact', error.stack);
      return {
        status: error.status || 500,
        message: 'Failed to delete agency contact',
        error: {
          code: 'INTERNAL_ERROR',
          message: error.message,
          details: error.stack,
          validationErrors: [],
        },
      };
    }
  }

  @GrpcMethod('AgencyService', 'ListAgencyContacts')
  async listAgencyContacts(data: any) {
    try {
      this.logger.log(`Listing agency contacts for agency: ${data.agencyId}`);
      const result = await this.contactService.listAgencyContacts(data);
      
      return {
        status: 200,
        message: 'Agency contacts retrieved successfully',
        data: result.data,
        error: null,
      };
    } catch (error) {
      this.logger.error('Failed to list agency contacts', error.stack);
      return {
        status: error.status || 500,
        message: 'Failed to list agency contacts',
        data: [],
        error: {
          code: 'INTERNAL_ERROR',
          message: error.message,
          details: error.stack,
          validationErrors: [],
        },
      };
    }
  }
}
