import { Injectable, NotFoundException, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Op } from 'sequelize';
import { Agency } from './agency.model';
import { AuditClientService } from '../audit.service';

@Injectable()
export class AgencyService {
  private readonly logger = new Logger(AgencyService.name);

  constructor(
    @InjectModel(Agency)
    private readonly agencyModel: typeof Agency,
    private readonly auditService: AuditClientService
  ) {}

  async createAgency(data: any) {
    try {
      const agency = await this.agencyModel.create({
        agencyName: data.agencyName,
        agencyLogo: data.agencyLogo,
        address: data.address,
        agencySize: data.agencySize,
        about: data.about,
        primaryContactNumber: data.primaryContactNumber,
        country: data.country,
        state: data.state,
        city: data.city,
        postalCode: data.postalCode,
        agencyRegistrationNumber: data.agencyRegistrationNumber,
        websiteUrl: data.websiteUrl,
        email: data.email,
      });

      // Log audit
      await this.auditService.logAction({
        action: 'CREATE_AGENCY',
        entityType: 'Agency',
        entityId: agency.id.toString(),
        userId: data.userId,
        userRole: data.userRole,
        details: data,
      });

      return agency;
    } catch (error) {
      this.logger.error('Failed to create agency', error.stack);
      throw error;
    }
  }

  async getAgency(id: string) {
    try {
      const agency = await this.agencyModel.findByPk(id, {
        include: ['branches', 'contacts'],
      });

      if (!agency) {
        throw new NotFoundException('Agency not found');
      }

      return agency;
    } catch (error) {
      this.logger.error('Failed to get agency', error.stack);
      throw error;
    }
  }

  async updateAgency(data: any) {
    try {
      const agency = await this.agencyModel.findByPk(data.id);
      if (!agency) {
        throw new NotFoundException('Agency not found');
      }

      const updateData: any = {};
      if (data.agencyName) updateData.agencyName = data.agencyName;
      if (data.agencyLogo) updateData.agencyLogo = data.agencyLogo;
      if (data.address) updateData.address = data.address;
      if (data.agencySize) updateData.agencySize = data.agencySize;
      if (data.about) updateData.about = data.about;
      if (data.primaryContactNumber) updateData.primaryContactNumber = data.primaryContactNumber;
      if (data.country) updateData.country = data.country;
      if (data.state) updateData.state = data.state;
      if (data.city) updateData.city = data.city;
      if (data.postalCode) updateData.postalCode = data.postalCode;
      if (data.agencyRegistrationNumber) updateData.agencyRegistrationNumber = data.agencyRegistrationNumber;
      if (data.websiteUrl) updateData.websiteUrl = data.websiteUrl;
      if (data.email) updateData.email = data.email;

      await agency.update(updateData);

      // Log audit
      await this.auditService.logAction({
        action: 'UPDATE_AGENCY',
        entityType: 'Agency',
        entityId: agency.id.toString(),
        userId: data.userId,
        userRole: data.userRole,
        details: data,
      });

      return agency;
    } catch (error) {
      this.logger.error('Failed to update agency', error.stack);
      throw error;
    }
  }

  async deleteAgency(id: string) {
    try {
      const agency = await this.agencyModel.findByPk(id);
      if (!agency) {
        throw new NotFoundException('Agency not found');
      }

      await agency.destroy();

      return { success: true };
    } catch (error) {
      this.logger.error('Failed to delete agency', error.stack);
      throw error;
    }
  }

  async listAgencies(data: any) {
    try {
      const { page = 1, pageSize = 10, search } = data;
      const offset = (page - 1) * pageSize;

      const whereClause: any = {};
      
      if (search) {
        whereClause[Op.or] = [
          { agencyName: { [Op.iLike]: `%${search}%` } },
          { email: { [Op.iLike]: `%${search}%` } },
          { country: { [Op.iLike]: `%${search}%` } },
          { city: { [Op.iLike]: `%${search}%` } },
        ];
      }

      const { rows: agencies, count: total } = await this.agencyModel.findAndCountAll({
        where: whereClause,
        limit: pageSize,
        offset,
        order: [['createdAt', 'DESC']],
      });

      return {
        data: agencies,
        total,
        page,
        pageSize,
      };
    } catch (error) {
      this.logger.error('Failed to list agencies', error.stack);
      throw error;
    }
  }
}
