import {
  Injectable,
  NotFoundException,
  Logger,
  ConflictException,
  InternalServerErrorException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Op, Transaction } from 'sequelize';
import { firstValueFrom } from 'rxjs';
import { Agency } from './agency.model';
import { AuditClientService } from '../audit.service';
import { AuthClientService } from '../auth.service';

@Injectable()
export class AgencyService {
  private readonly logger = new Logger(AgencyService.name);

  constructor(
    @InjectModel(Agency) private agencyModel: typeof Agency,
    private auditService: AuditClientService,
    private authService: AuthClientService
  ) {}

  async createAgency(data: any) {
    const transaction = await this.agencyModel.sequelize.transaction();

    try {
      // Check if agency already exists
      const existing = await this.agencyModel.findOne({
        where: {
          [Op.or]: [
            { email: data.email },
            { agencyRegistrationNumber: data.agencyRegistrationNumber },
          ],
        },
        transaction,
      });

      if (existing) {
        await transaction.rollback();
        throw new ConflictException(
          `Agency with email "${data.email}" or registration number "${data.agencyRegistrationNumber}" already exists`
        );
      }

      // Create the agency within transaction
      const agency = await this.agencyModel.create({
        agencyName: data.agencyName,
        agencyLogo: data.agencyLogo,
        address: data.address,
        agencySize: data.agencySize,
        about: data.about,
        primaryContactNumber: data.primaryContactNumber,
        country: data.country,
        state: data.state,
        city: data.city,
        postalCode: data.postalCode,
        agencyRegistrationNumber: data.agencyRegistrationNumber,
        websiteUrl: data.websiteUrl,
        email: data.email,
      }, { transaction });

      // Create user for agency owner - MUST succeed for transaction to commit
      try {
        const userObservable = this.authService.createUser({
          firstName: data.ownerFirstName || 'Agency',
          lastName: data.ownerLastName || 'Owner',
          email: data.email,
          password: data.password || 'TempPassword123!',
          roles: ['AGENCY_OWNER'],
          permissions: [
            'AgencyManagement:Onboardnewagencypartners',
            'AgencyManagement:Trackagencyperformance',
            'AgencyManagement:Viewapplicationssubmittedbyagencies',
          ],
          isActive: true,
          userId: data.userId,
          userRole: data.userRole,
        });

        const userResult = await firstValueFrom(userObservable);

        // Check if user creation was successful
        if (!userResult || userResult.status !== 200) {
          throw new Error(userResult?.message || 'User creation failed');
        }

        this.logger.log(`Successfully created user for agency: ${data.email}`);
      } catch (authError) {
        this.logger.error('Auth service failed. Rolling back agency creation.', authError);
        await transaction.rollback();

        // Return specific error message from auth service
        const errorMessage = authError?.message || 'Failed to create user account for agency owner';
        throw new InternalServerErrorException(
          `Agency creation failed: ${errorMessage}. Please ensure the email is not already in use.`
        );
      }

      // Commit the transaction - both agency and user created successfully
      await transaction.commit();

      // Log audit separately (don't rollback if audit fails)
      try {
        const auditObservable = this.auditService.createAuditLog({
          userId: parseInt(data.userId) || 0,
          userRole: data.userRole || 'unknown',
          actions: 'CREATE_AGENCY',
          serviceName: 'agency-service',
          resourceType: 'Agency',
          resourceId: Number(agency.id),
          description: `Created agency: ${data.agencyName}`,
          metadata: {
            agencyName: data.agencyName,
            email: data.email,
          },
          source: 'agency-service',
        });
        await firstValueFrom(auditObservable);
      } catch (auditError) {
        this.logger.warn('Failed to log audit (agency creation was successful)', auditError);
      }

      return agency;
    } catch (error) {
      // Ensure transaction is rolled back if not already done
      try {
        await transaction.rollback();
      } catch (rollbackError) {
        // Transaction might already be rolled back or committed
        this.logger.warn('Transaction rollback failed (might already be finished)', rollbackError.message);
      }

      this.logger.error('Failed to create agency', error.stack);
      throw error;
    }
  }

  async getAgency(id: string) {
    try {
      const agency = await this.agencyModel.findByPk(id, {
        include: ['branches', 'contacts'],
      });

      if (!agency) {
        throw new NotFoundException('Agency not found');
      }

      return agency;
    } catch (error) {
      this.logger.error('Failed to get agency', error.stack);
      throw error;
    }
  }

  async updateAgency(data: any) {
    const transaction = await this.agencyModel.sequelize.transaction();

    try {
      const agency = await this.agencyModel.findByPk(data.id, { transaction });
      if (!agency) {
        await transaction.rollback();
        throw new NotFoundException(`Agency with ID "${data.id}" not found`);
      }

      // Check for conflicts if email or registration number is being updated
      if (data.email || data.agencyRegistrationNumber) {
        const conflictWhere: any = {
          id: { [Op.ne]: data.id }, // Exclude current agency
        };

        const orConditions = [];
        if (data.email && data.email !== agency.email) {
          orConditions.push({ email: data.email });
        }
        if (data.agencyRegistrationNumber && data.agencyRegistrationNumber !== agency.agencyRegistrationNumber) {
          orConditions.push({ agencyRegistrationNumber: data.agencyRegistrationNumber });
        }

        if (orConditions.length > 0) {
          conflictWhere[Op.or] = orConditions;

          const existing = await this.agencyModel.findOne({
            where: conflictWhere,
            transaction,
          });

          if (existing) {
            await transaction.rollback();
            throw new ConflictException(
              `Another agency already exists with the provided email or registration number`
            );
          }
        }
      }

      const updateData: any = {};
      if (data.agencyName) updateData.agencyName = data.agencyName;
      if (data.agencyLogo) updateData.agencyLogo = data.agencyLogo;
      if (data.address) updateData.address = data.address;
      if (data.agencySize) updateData.agencySize = data.agencySize;
      if (data.about) updateData.about = data.about;
      if (data.primaryContactNumber)
        updateData.primaryContactNumber = data.primaryContactNumber;
      if (data.country) updateData.country = data.country;
      if (data.state) updateData.state = data.state;
      if (data.city) updateData.city = data.city;
      if (data.postalCode) updateData.postalCode = data.postalCode;
      if (data.agencyRegistrationNumber)
        updateData.agencyRegistrationNumber = data.agencyRegistrationNumber;
      if (data.websiteUrl) updateData.websiteUrl = data.websiteUrl;
      if (data.email) updateData.email = data.email;

      await agency.update(updateData, { transaction });

      // Commit the transaction
      await transaction.commit();

      // Log audit
      try {
        const auditObservable = this.auditService.createAuditLog({
          userId: parseInt(data.userId) || 0,
          userRole: data.userRole || 'unknown',
          actions: 'UPDATE_AGENCY',
          serviceName: 'agency-service',
          resourceType: 'Agency',
          resourceId: Number(agency.id),
          description: `Updated agency: ${agency.agencyName}`,
          metadata: {
            agencyName: agency.agencyName,
          },
          source: 'agency-service',
        });
        await firstValueFrom(auditObservable);
      } catch (auditError) {
        this.logger.warn('Failed to log audit', auditError);
      }

      return agency;
    } catch (error) {
      // Ensure transaction is rolled back if not already done
      try {
        await transaction.rollback();
      } catch (rollbackError) {
        this.logger.warn('Transaction rollback failed (might already be finished)', rollbackError.message);
      }

      this.logger.error('Failed to update agency', error.stack);
      throw error;
    }
  }

  async deleteAgency(id: string) {
    try {
      const agency = await this.agencyModel.findByPk(id);
      if (!agency) {
        throw new NotFoundException('Agency not found');
      }

      await agency.destroy();

      return { success: true };
    } catch (error) {
      this.logger.error('Failed to delete agency', error.stack);
      throw error;
    }
  }

  async listAgencies(data: any) {
    try {
      const { page = 1, pageSize = 10, search } = data;
      const offset = (page - 1) * pageSize;

      const whereClause: any = {};

      if (search) {
        whereClause[Op.or] = [
          { agencyName: { [Op.iLike]: `%${search}%` } },
          { email: { [Op.iLike]: `%${search}%` } },
          { country: { [Op.iLike]: `%${search}%` } },
          { city: { [Op.iLike]: `%${search}%` } },
        ];
      }

      const { rows: agencies, count: total } =
        await this.agencyModel.findAndCountAll({
          where: whereClause,
          limit: pageSize,
          offset,
          order: [['createdAt', 'DESC']],
        });

      return {
        data: agencies,
        total,
        page,
        pageSize,
      };
    } catch (error) {
      this.logger.error('Failed to list agencies', error.stack);
      throw error;
    }
  }
}
