import {
  Injectable,
  NotFoundException,
  Logger,
  ConflictException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Op } from 'sequelize';
import { firstValueFrom } from 'rxjs';
import { Agency } from './agency.model';
import { AuditClientService } from '../audit.service';
import { AuthClientService } from '../auth.service';

@Injectable()
export class AgencyService {
  private readonly logger = new Logger(AgencyService.name);

  constructor(
    @InjectModel(Agency) private agencyModel: typeof Agency,
    private auditService: AuditClientService,
    private authService: AuthClientService
  ) {}

  async createAgency(data: any) {
    try {
      // Check if agency already exists
      const existing = await this.agencyModel.findOne({
        where: {
          [Op.or]: [
            { email: data.email },
            { agencyRegistrationNumber: data.agencyRegistrationNumber },
          ],
        },
      });

      if (existing) {
        throw new ConflictException('Agency already exists');
      }

      // Create the agency
      const agency = await this.agencyModel.create({
        agencyName: data.agencyName,
        agencyLogo: data.agencyLogo,
        address: data.address,
        agencySize: data.agencySize,
        about: data.about,
        primaryContactNumber: data.primaryContactNumber,
        country: data.country,
        state: data.state,
        city: data.city,
        postalCode: data.postalCode,
        agencyRegistrationNumber: data.agencyRegistrationNumber,
        websiteUrl: data.websiteUrl,
        email: data.email,
      });

      // Call auth service and rollback if it fails
      try {
        const userObservable = this.authService.createUser({
          firstName: data.ownerFirstName || 'Agency',
          lastName: data.ownerLastName || 'Owner',
          email: data.email,
          password: data.password || 'TempPassword123!',
          roles: ['AGENCY_OWNER'],
          permissions: [
            'AgencyManagement:Onboardnewagencypartners',
            'AgencyManagement:Trackagencyperformance',
            'AgencyManagement:Viewapplicationssubmittedbyagencies',
          ],
          isActive: true,
          userId: data.userId,
          userRole: data.userRole,
        });

        await firstValueFrom(userObservable);
      } catch (authError) {
        this.logger.error(
          'Auth service failed. Agency created but user creation failed.',
          authError
        );
        // Note: Agency is already created, but user creation failed
        // In a production system, you might want to implement compensation logic
      }

      // Log audit separately — no rollback if it fails
      try {
        const auditObservable = this.auditService.createAuditLog({
          userId: parseInt(data.userId) || 0,
          userRole: data.userRole || 'unknown',
          actions: 'CREATE_AGENCY',
          serviceName: 'agency-service',
          resourceType: 'Agency',
          resourceId: Number(agency.id),
          description: `Created agency: ${data.agencyName}`,
          metadata: {
            agencyName: data.agencyName,
            email: data.email,
          },
          source: 'agency-service',
        });
        await firstValueFrom(auditObservable);
      } catch (auditError) {
        this.logger.warn('Failed to log audit', auditError);
      }

      return agency;
    } catch (error) {
      this.logger.error('Failed to create agency', error.stack);
      throw error;
    }
  }

  async getAgency(id: string) {
    try {
      const agency = await this.agencyModel.findByPk(id, {
        include: ['branches', 'contacts'],
      });

      if (!agency) {
        throw new NotFoundException('Agency not found');
      }

      return agency;
    } catch (error) {
      this.logger.error('Failed to get agency', error.stack);
      throw error;
    }
  }

  async updateAgency(data: any) {
    try {
      const agency = await this.agencyModel.findByPk(data.id);
      if (!agency) {
        throw new NotFoundException('Agency not found');
      }

      const updateData: any = {};
      if (data.agencyName) updateData.agencyName = data.agencyName;
      if (data.agencyLogo) updateData.agencyLogo = data.agencyLogo;
      if (data.address) updateData.address = data.address;
      if (data.agencySize) updateData.agencySize = data.agencySize;
      if (data.about) updateData.about = data.about;
      if (data.primaryContactNumber)
        updateData.primaryContactNumber = data.primaryContactNumber;
      if (data.country) updateData.country = data.country;
      if (data.state) updateData.state = data.state;
      if (data.city) updateData.city = data.city;
      if (data.postalCode) updateData.postalCode = data.postalCode;
      if (data.agencyRegistrationNumber)
        updateData.agencyRegistrationNumber = data.agencyRegistrationNumber;
      if (data.websiteUrl) updateData.websiteUrl = data.websiteUrl;
      if (data.email) updateData.email = data.email;

      await agency.update(updateData);

      // Log audit
      try {
        const auditObservable = this.auditService.createAuditLog({
          userId: parseInt(data.userId) || 0,
          userRole: data.userRole || 'unknown',
          actions: 'UPDATE_AGENCY',
          serviceName: 'agency-service',
          resourceType: 'Agency',
          resourceId: Number(agency.id),
          description: `Updated agency: ${agency.agencyName}`,
          metadata: {
            agencyName: agency.agencyName,
          },
          source: 'agency-service',
        });
        await firstValueFrom(auditObservable);
      } catch (auditError) {
        this.logger.warn('Failed to log audit', auditError);
      }

      return agency;
    } catch (error) {
      this.logger.error('Failed to update agency', error.stack);
      throw error;
    }
  }

  async deleteAgency(id: string) {
    try {
      const agency = await this.agencyModel.findByPk(id);
      if (!agency) {
        throw new NotFoundException('Agency not found');
      }

      await agency.destroy();

      return { success: true };
    } catch (error) {
      this.logger.error('Failed to delete agency', error.stack);
      throw error;
    }
  }

  async listAgencies(data: any) {
    try {
      const { page = 1, pageSize = 10, search } = data;
      const offset = (page - 1) * pageSize;

      const whereClause: any = {};

      if (search) {
        whereClause[Op.or] = [
          { agencyName: { [Op.iLike]: `%${search}%` } },
          { email: { [Op.iLike]: `%${search}%` } },
          { country: { [Op.iLike]: `%${search}%` } },
          { city: { [Op.iLike]: `%${search}%` } },
        ];
      }

      const { rows: agencies, count: total } =
        await this.agencyModel.findAndCountAll({
          where: whereClause,
          limit: pageSize,
          offset,
          order: [['createdAt', 'DESC']],
        });

      return {
        data: agencies,
        total,
        page,
        pageSize,
      };
    } catch (error) {
      this.logger.error('Failed to list agencies', error.stack);
      throw error;
    }
  }
}
