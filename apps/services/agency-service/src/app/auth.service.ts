import { Injectable, OnModuleInit, Logger } from '@nestjs/common';
import { Client, ClientGrpc } from '@nestjs/microservices';
import { Transport } from '@nestjs/microservices';
import { join } from 'path';
import { Observable, throwError } from 'rxjs';
import { catchError, timeout, retry } from 'rxjs/operators';

interface CreateUserRequest {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  roles: string[];
  permissions: string[];
  isActive: boolean;
  userId?: string;
  userRole?: string;
}

interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  roles: string[];
  permissions: string[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface CreateUserResponse {
  status: number;
  message: string;
  data: User;
  error?: any;
}

interface AuthService {
  createUser(request: CreateUserRequest): Observable<CreateUserResponse>;
}

@Injectable()
export class AuthClientService implements OnModuleInit {
  private readonly logger = new Logger(AuthClientService.name);

  @Client({
    transport: Transport.GRPC,
    options: {
      package: 'auth',
      protoPath: join(process.cwd(), 'libs/shared/dto/src/lib/auth/auth.proto'),
      url: 'auth-service:50051',
      loader: {
        keepCase: true,
        longs: String,
        enums: String,
        defaults: true,
        oneofs: true,
      },
    },
  })
  private readonly client: ClientGrpc;

  private authService: AuthService;

  onModuleInit() {
    this.authService = this.client.getService<AuthService>('AuthService');
  }

  private handleError(operation = 'operation') {
    return (error: any) => {
      this.logger.error(`${operation} failed: ${error.message}`, error.stack);
      return throwError(() => error);
    };
  }

  createUser(request: CreateUserRequest): Observable<CreateUserResponse> {
    return this.authService.createUser(request).pipe(
      timeout(10000),
      retry(3),
      catchError(this.handleError('createUser'))
    );
  }
}
