import { Controller, Logger } from '@nestjs/common';
import { GrpcMethod } from '@nestjs/microservices';
import { CampusService } from './campus.service';

@Controller()
export class CampusGrpcController {
  private readonly logger = new Logger(CampusGrpcController.name);

  constructor(private readonly campusService: CampusService) {}

  @GrpcMethod('UniversityService', 'CreateCampus')
  async createCampus(data: any) {
    try {
      const campus = await this.campusService.create(data);
      return { status: 200, message: 'Campus created', data: campus };
    } catch (error) {
      return { status: 500, message: error.message, error: { details: error.message } };
    }
  }

  @GrpcMethod('UniversityService', 'UpdateCampus')
  async updateCampus(data: any) {
    try {
      const campus = await this.campusService.update(data.id, data);
      return { status: 200, message: 'Campus updated', data: campus };
    } catch (error) {
      return { status: 500, message: error.message, error: { details: error.message } };
    }
  }

  @GrpcMethod('UniversityService', 'GetCampus')
  async getCampus(data: { id: number }) {
    try {
      const campus = await this.campusService.findOne(data.id);
      return { status: 200, message: 'Campus fetched', data: campus };
    } catch (error) {
      return { status: 404, message: error.message, error: { details: error.message } };
    }
  }

  @GrpcMethod('UniversityService', 'DeleteCampus')
  async deleteCampus(data: { id: number }) {
    try {
      await this.campusService.remove(data.id);
      return { status: 200, message: 'Campus deleted', success: true };
    } catch (error) {
      return { status: 500, message: error.message, error: { details: error.message }, success: false };
    }
  }

  @GrpcMethod('UniversityService', 'GetCampusList')
  async listCampuses(data: { universityId?: number; page?: number; limit?: number }) {
    try {
      const result = await this.campusService.findAll(data.universityId, data.page, data.limit);
      console.log("=================================>",JSON.stringify(result))
       const finalResult = result.map((campus) => ({
      id: campus.id,
      campusName: campus.campusName,
      address: campus.address,
      countryId: campus.countryId,
      state: campus.state,
      city: campus.city,
      postalCode: campus.postalCode,
      contactNumber: campus.contactNumber,
      email: campus.email,
      programCourses: (campus.programLevels || []).map((pl) => ({
        programLevelId: pl.id,
        programLevelName: pl.programLevelName,
        courses: pl.courses || [],
      })),
    }));
    console.log("FinalResult===============================> ",finalResult)
      return { status: 200, message: 'Campuses fetched', data: finalResult };
    } catch (error) {
      return { status: 500, message: error.message, error: { details: error.message } };
    }
  }
}
