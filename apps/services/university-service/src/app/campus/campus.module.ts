import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { Campus } from './campus.model';
import { CampusProgramLevel } from './campus-program-level.model';
import { CampusService } from './campus.service';
import { CampusGrpcController } from './campus.grpc.controller';
import { ProgramLevel } from '../program-level/programLevel.model';
import { Country } from '../country/country.model';
import { Course } from '../course/course.model';

@Module({
  imports: [SequelizeModule.forFeature([Campus, ProgramLevel, CampusProgramLevel,Country,Course])],
  controllers: [CampusGrpcController],
  providers: [CampusService],
  exports: [CampusService],
})
export class CampusModule {}