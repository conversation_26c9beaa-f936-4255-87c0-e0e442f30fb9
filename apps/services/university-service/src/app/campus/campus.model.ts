import {
  Column,
  Model,
  Table,
  DataType,
  ForeignKey,
  BelongsTo,
  BelongsToMany,
  Unique,
} from 'sequelize-typescript';
import { University } from '../university/university.model';
import { CampusProgramLevel } from './campus-program-level.model';
import { ProgramLevel } from '../program-level/programLevel.model';
import { Country } from '../country/country.model';

@Table({ tableName: 'campuses', timestamps: true })
export class Campus extends Model {
  @Column({ type: DataType.INTEGER, autoIncrement: true, primaryKey: true })
  id: number;

  @ForeignKey(() => University)
  @Column({ type: DataType.INTEGER, allowNull: false })
  universityId: number;

  @Unique
  @Column({ type: DataType.STRING, allowNull: false })
  campusName: string;

  @Column({ type: DataType.STRING, allowNull: false })
  address: string;

  @ForeignKey(() => Country)
  @Column({ type: DataType.BIGINT, allowNull: false })
  countryId: number; // ❗ Make this number, not string

  @Column({ type: DataType.STRING, allowNull: false })
  state: string;

  @Column({ type: DataType.STRING, allowNull: false })
  city: string;

  @Column({ type: DataType.STRING, allowNull: false })
  postalCode: string;

  @Column({ type: DataType.STRING })
  contactNumber: string;

  @Column({ type: DataType.STRING })
  email: string;

  @BelongsTo(() => University)
  university: University;

  @BelongsToMany(() => ProgramLevel, () => CampusProgramLevel)
  programLevels: ProgramLevel[];

  @BelongsTo(() => Country) // ✅ Belongs to Country
  country: Country;
}
