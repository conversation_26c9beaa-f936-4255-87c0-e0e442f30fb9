import {
  Table,
  Model,
  ForeignKey,
  Column,
  DataType,
} from 'sequelize-typescript';
import { Campus } from './campus.model';
import { ProgramLevel } from '../program-level/programLevel.model';

@Table({ tableName: 'campus_program_levels', timestamps: false })
export class CampusProgramLevel extends Model {
  @ForeignKey(() => Campus)
  @Column({ type: DataType.INTEGER })
  campusId: number;

  @ForeignKey(() => ProgramLevel)
  @Column({ type: DataType.INTEGER })
  programLevelId: number;
}
