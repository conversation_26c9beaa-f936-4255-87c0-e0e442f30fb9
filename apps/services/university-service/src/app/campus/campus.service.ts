// campus.service.ts
import {
  Injectable,
  Logger,
  NotFoundException,
  InternalServerErrorException,
  BadRequestException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Sequelize } from 'sequelize-typescript';
import { Campus } from './campus.model';
import { ProgramLevel } from '../program-level/programLevel.model';
import { Course } from '../course/course.model';

@Injectable()
export class CampusService {
  private readonly logger = new Logger(CampusService.name);

  constructor(
    @InjectModel(Campus)
    private campusRepository: typeof Campus,

    private readonly sequelize: Sequelize
  ) {}

  async create(data: any): Promise<Campus> {
    const transaction = await this.sequelize.transaction();
    try {
      const { programLevelIds = [], ...campusData } = data;
      if (!data.countryId) {
        throw new BadRequestException('countryId is required');
      }
      if (!data.universityId) {
        throw new BadRequestException('universityId is required');
      }
      const campus = await this.campusRepository.create(campusData, {
        transaction,
      });

      if (programLevelIds.length) {
        await campus.$set('programLevels', programLevelIds, { transaction });
      }

      await transaction.commit();

      return await this.campusRepository.findByPk(campus.id, {
        include: ['programLevels'],
      });
    } catch (error) {
      await transaction.rollback();

      throw new InternalServerErrorException({
        message: 'Campus creation failed',
        details: error.message,
      });
    }
  }
  async findAll(universityId?: number, page = 1, limit = 10) {
    const where = universityId ? { universityId } : {};
    return this.campusRepository.findAll({
      where,
      offset: (page - 1) * limit,
      limit,
      order: [['createdAt', 'DESC']],
      include: [
        {
          model: ProgramLevel,
          as: 'programLevels',
          include: [
            {
              model: Course,
              as: 'courses',
              required: false,
            },
          ],
        },
      ],
    });
  }
  async update(id: number, data: any): Promise<Campus> {
    const transaction = await this.sequelize.transaction();
    try {
      const campus = await this.findOne(id);
      const { programLevelIds, ...updateData } = data;

      await campus.update(updateData, { transaction });

      if (programLevelIds) {
        await campus.$set('programLevels', programLevelIds, { transaction });
      }

      await transaction.commit();

      return await this.findOne(id); // reload with updated associations
    } catch (error) {
      await transaction.rollback();
      this.logger.error('Update campus failed', error.stack);
      throw new InternalServerErrorException('Campus update failed');
    }
  }

  async findOne(id: number): Promise<Campus> {
    const campus = await this.campusRepository.findByPk(id, {
      include: ['programLevels'],
    });
    if (!campus) throw new NotFoundException('Campus not found');
    return campus;
  }

  async remove(id: number): Promise<void> {
    const campus = await this.findOne(id);
    await campus.destroy();
  }
}
