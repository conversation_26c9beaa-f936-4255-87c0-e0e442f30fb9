// Updated gRPC Controller
import { <PERSON>, Lo<PERSON> } from '@nestjs/common';
import { GrpcMethod } from '@nestjs/microservices';
import { FieldOfStudyService } from './field-of-study.service';
import { AuditClientService } from '../audit.service';

@Controller()
export class FieldOfStudyGrpcController {
  private readonly logger = new Logger(FieldOfStudyGrpcController.name);

  constructor(
    private readonly service: FieldOfStudyService,
    private readonly auditService: AuditClientService
  ) {}

  @GrpcMethod('UniversityService', 'CreateFieldOfStudy')
  async create(data: { universityId: number; userId: string; names: string[] }) {
    try {
      const results = await this.service.create(data);

      // Log audit for each created field
      for (const result of results) {
        await this.auditService.logAction({
          action: 'CREATE_FIELD_OF_STUDY',
          entityType: 'FieldOfStudy',
          entityId: result.id.toString(),
          userId: data.userId,
          details: { ...data, name: result.name },
        });
      }

      return { status: 200, message: 'Fields of Study created', data: results, error: null };
    } catch (error) {
      this.logger.error(`Create failed: ${error.message}`, error.stack);
      return { status: 500, message: error.message, data: null, error: { details: error.message } };
    }
  }

  @GrpcMethod('UniversityService', 'UpdateFieldOfStudy')
  async update(data: { universityId: number; userId: string; names: string[] }) {
    try {
      const results = await this.service.update(data);

      // Log audit for each updated field
      for (const result of results) {
        await this.auditService.logAction({
          action: 'UPDATE_FIELD_OF_STUDY',
          entityType: 'FieldOfStudy',
          entityId: result.id.toString(),
          userId: data.userId,
          details: { ...data, name: result.name },
        });
      }

      return { status: 200, message: 'Fields of Study updated', data: results, error: null };
    } catch (error) {
      this.logger.error(`Update failed: ${error.message}`, error.stack);
      return { status: 500, message: error.message, data: null, error: { details: error.message } };
    }
  }

  @GrpcMethod('UniversityService', 'GetFieldOfStudy')
  async get(data: { id: number }) {
    try {
      const result = await this.service.findOne(data.id);
      return { status: 200, message: 'Field retrieved', data: result, error: null };
    } catch (error) {
      this.logger.error(`Get failed: ${error.message}`, error.stack);
      return { status: 500, message: error.message, data: null, error: { details: error.message } };
    }
  }

  @GrpcMethod('UniversityService', 'DeleteFieldOfStudy')
  async delete(data: { id: number; userId: string }) {
    try {
      const deletedData = await this.service.remove(data.id);

      await this.auditService.logAction({
        action: 'DELETE_FIELD_OF_STUDY',
        entityType: 'FieldOfStudy',
        entityId: data.id.toString(),
        userId: data.userId,
        details: deletedData,
      });

      return {
        status: 200,
        message: 'Fields of study is deleted successfully!',
        data: {
          id: deletedData.id.toString(),
          userId: data.userId,
          name: deletedData.name,
          status: deletedData.status,
          createdAt: deletedData.createdAt?.toISOString?.() || '',
          updatedAt: deletedData.updatedAt?.toISOString?.() || '',
        },
        error: null,
      };
    } catch (error) {
      this.logger.error(`Delete failed: ${error.message}`, error.stack);
      return {
        status: 500,
        message: error.message,
        data: null,
        error: { details: error.message },
      };
    }
  }

  @GrpcMethod('UniversityService', 'listFieldOfStudyByUniversity')
  async list(data: { universityId: number }) {
    try {
      const fields = await this.service.findAllByUniversity(data.universityId);
      return { status: 200, message: 'List retrieved', data: fields, error: null };
    } catch (error) {
      this.logger.error(`List failed: ${error.message}`, error.stack);
      return { status: 500, message: error.message, data: null, error: { details: error.message } };
    }
  }
}