import { Modu<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { FieldOfStudy } from './field-of-study.model';
import { FieldOfStudyService } from './field-of-study.service';
import { FieldOfStudyGrpcController } from './field-of-study.grpc.controller';
import { AuditClientService } from '../audit.service';

@Module({
  imports: [SequelizeModule.forFeature([FieldOfStudy])],
  controllers: [FieldOfStudyGrpcController],
  providers: [FieldOfStudyService,AuditClientService],
  exports: [FieldOfStudyService],
})
export class FieldOfStudyModule {}
