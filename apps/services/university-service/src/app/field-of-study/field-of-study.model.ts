import { Table, Column, DataType, ForeignKey, BelongsTo, HasMany } from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { University } from '../university/university.model';
import { Course } from '../course/course.model';

@Table({ tableName: 'fields_of_study' })
export class FieldOfStudy extends BaseModel {
  @ForeignKey(() => University)
  @Column(DataType.BIGINT)
  universityId!: number;

  @BelongsTo(() => University)
  university!: University;

  @Column(DataType.STRING)
  name!: string;

  @Column({ type: DataType.STRING, defaultValue: 'active' })
  status!: string;

    // ✅ One-to-many relationship with Course
  @HasMany(() => Course)
  courses!: Course[];
}
