import {
  Injectable,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { FieldOfStudy } from './field-of-study.model';
import { AppService } from '../app.service';

@Injectable()
export class FieldOfStudyService {
  constructor(
    @InjectModel(FieldOfStudy) private readonly model: typeof FieldOfStudy,
    private readonly appService: AppService
  ) {}

  async create(data: {
    universityId: number;
    userId: string;
    names: string[];
  }) {
    const start = Date.now();
    try {
      const results = [];
      
      for (const name of data.names) {
        // Check if field already exists
        const exists = await this.model.findOne({
          where: { name, universityId: data.universityId },
        });
        
        if (exists) {
          throw new ConflictException(`Field of Study '${name}' already exists`);
        }
        
        // Create new field
        const result = await this.model.create({
          universityId: data.universityId,
          userId: data.userId,
          name,
        });
        
        results.push(result);
      }

      this.appService.trackProcessingDuration(
        'field_of_study_create_success',
        (Date.now() - start) / 1000
      );
      return results;
    } catch (error) {
      this.appService.trackProcessingDuration(
        'field_of_study_create_error',
        (Date.now() - start) / 1000
      );
      throw error;
    }
  }

  async update(data: {
    universityId: number;
    userId: string;
    names: string[];
  }) {
    const start = Date.now();
    try {
      // First, delete all existing fields for this university
      await this.model.destroy({
        where: { universityId: data.universityId },
      });

      // Then create new fields with the provided names
      const results = [];
      for (const name of data.names) {
        const result = await this.model.create({
          universityId: data.universityId,
          userId: data.userId,
          name,
        });
        results.push(result);
      }

      this.appService.trackProcessingDuration(
        'field_of_study_update_success',
        (Date.now() - start) / 1000
      );
      return results;
    } catch (error) {
      this.appService.trackProcessingDuration(
        'field_of_study_update_error',
        (Date.now() - start) / 1000
      );
      throw error;
    }
  }

  async findOne(id: number) {
    return this.model.findByPk(id);
  }

  async remove(id: number) {
    const start = Date.now();
    const record = await this.model.findByPk(id);
    if (!record) throw new NotFoundException('Field of Study not found');
    await this.model.destroy({ where: { id } });

    this.appService.trackProcessingDuration(
      'field_of_study_delete_success',
      (Date.now() - start) / 1000
    );
    return record;
  }

  async findAllByUniversity(universityId: number) {
    return this.model.findAll({
      where: { universityId },
      order: [['createdAt', 'DESC']],
    });
  }
}