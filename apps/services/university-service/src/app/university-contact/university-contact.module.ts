import { Modu<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { UniversityContactDetails } from './university-contact.model';
import { UniversityContactService } from './university-contact.service';
import { UniversityContactGrpcController } from './university-contact.grpc.controller';
import { AuditClientService } from '../audit.service';
import { AppService } from '../app.service';

@Module({
  imports: [SequelizeModule.forFeature([UniversityContactDetails])],
  controllers: [UniversityContactGrpcController],
  providers: [UniversityContactService, AuditClientService],
  exports: [UniversityContactService],
})
export class UniversityContactModule {}
