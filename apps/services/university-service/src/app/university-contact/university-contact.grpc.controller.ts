import { <PERSON>, Logger } from '@nestjs/common';
import { GrpcMethod } from '@nestjs/microservices';
import { UniversityContactService } from './university-contact.service';
import { AuditClientService } from '../audit.service';

@Controller()
export class UniversityContactGrpcController {
  private readonly logger = new Logger(UniversityContactGrpcController.name);

  constructor(
    private readonly service: UniversityContactService,
    private readonly auditService: AuditClientService
  ) {}

  @GrpcMethod('UniversityService', 'CreateUniversityContact')
  async create(data: any) {
    try {
      const result = await this.service.create(data);
      await this.auditService.logAction({
        action: 'CREATE_UNIVERSITY_CONTACT',
        entityType: 'UniversityContact',
        entityId: result.id.toString(),
        userId: data.userId,
        details: result,
      });
      return { status: 200, message: 'Contact created', data: result, error: null };
    } catch (error) {
      this.logger.error('<PERSON><PERSON> failed', error.stack);
      return { status: 500, message: error.message, data: null, error: { details: error.message } };
    }
  }

  @GrpcMethod('UniversityService', 'UpdateUniversityContact')
  async update(data: any) {
    try {
      const result = await this.service.update(data);
      await this.auditService.logAction({
        action: 'UPDATE_UNIVERSITY_CONTACT',
        entityType: 'UniversityContact',
        entityId: result.id.toString(),
        userId: data.userId,
        details: result,
      });
      return { status: 200, message: 'Contact updated', data: result, error: null };
    } catch (error) {
      this.logger.error('Update failed', error.stack);
      return { status: 500, message: error.message, data: null, error: { details: error.message } };
    }
  }

  @GrpcMethod('UniversityService', 'GetUniversityContact')
  async get(data: { id: string }) {
    try {
      const result = await this.service.findOne(data.id);
      return { status: 200, message: 'Contact fetched', data: result, error: null };
    } catch (error) {
      this.logger.error('Get failed', error.stack);
      return { status: 500, message: error.message, data: null, error: { details: error.message } };
    }
  }

  @GrpcMethod('UniversityService', 'DeleteUniversityContact')
  async delete(data: { id: string; userId: string }) {
    try {
      const result = await this.service.remove(data.id);
      await this.auditService.logAction({
        action: 'DELETE_UNIVERSITY_CONTACT',
        entityType: 'UniversityContact',
        entityId: data.id,
        userId: data.userId,
        details: result,
      });
      return { status: 200, message: 'Contact deleted', success: true, error: null };
    } catch (error) {
      this.logger.error('Delete failed', error.stack);
      return { status: 500, message: error.message, success: false, error: { details: error.message } };
    }
  }

  @GrpcMethod('UniversityService', 'ListUniversityContact')
  async list(data: { universityId: number; page: number; pageSize: number }) {
    try {
      const result = await this.service.findAll(data);
      return { status: 200, message: 'University Contact information !.', data: result, error: null };
    } catch (error) {
      this.logger.error('List failed', error.stack);
      return { status: 500, message: error.message, data: null, error: { details: error.message } };
    }
  }
}
