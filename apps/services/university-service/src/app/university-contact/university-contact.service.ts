import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { UniversityContactDetails } from './university-contact.model';
import { AppService } from '../app.service';

@Injectable()
export class UniversityContactService {
  constructor(
    @InjectModel(UniversityContactDetails) private readonly model: typeof UniversityContactDetails,
    private readonly appService: AppService
  ) {}

  async create(data: any) {
    const record = await this.model.create(data);
    this.appService.trackProcessingDuration('contact_create_success', 0);
    return record;
  }

  async update(data: any) {
    const record = await this.model.findByPk(data.id);
    if (!record) throw new NotFoundException('University Contact not found');
    await record.update(data);
    this.appService.trackProcessingDuration('contact_update_success', 0);
    return record;
  }

  async findOne(id: string) {
    return this.model.findByPk(id);
  }

  async remove(id: string) {
    const record = await this.model.findByPk(id);
    if (!record) throw new NotFoundException('University Contact not found');
    await this.model.destroy({ where: { id } });
    this.appService.trackProcessingDuration('contact_delete_success', 0);
    return record;
  }

  async findAll({ page = 1, pageSize = 10, universityId }: { page: number; pageSize: number; universityId?: number }) {
    const whereClause = universityId ? { universityId } : {};
    return this.model.findAll({
      where: whereClause,
      offset: (page - 1) * pageSize,
      limit: pageSize,
      order: [['createdAt', 'DESC']],
    });
  }
}
