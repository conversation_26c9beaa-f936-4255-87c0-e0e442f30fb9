import { Table, Column, DataType, ForeignKey, BelongsTo } from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { University } from '../university/university.model';

@Table({ tableName: 'university_contact_details' })
export class UniversityContactDetails extends BaseModel {
  @ForeignKey(() => University)
  @Column(DataType.BIGINT)
  universityId!: number;

  @BelongsTo(() => University)
  university!: University;

  @Column(DataType.STRING)
  contactName!: string;

  @Column(DataType.STRING)
  designation!: string;

  @Column(DataType.STRING)
  email!: string;

  @Column(DataType.STRING)
  contactNumber!: string;

  @Column(DataType.STRING)
  alternativeEmail!: string;
}
