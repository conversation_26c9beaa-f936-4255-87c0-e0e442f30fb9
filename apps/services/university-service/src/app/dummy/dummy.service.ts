import { Injectable, NotFoundException, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Op } from 'sequelize';
import { DummyTestScore } from './dummy-test-score.model';
import { DummyApplicationStep } from './dummy-application-step.model';
import { DummyLectureLanguage } from './dummy-lecture-language.model';
import { DummySocialPlatform } from './dummy-social-platform.model';
import { AuditClientService } from '../audit.service';

@Injectable()
export class DummyService {
  private readonly logger = new Logger(DummyService.name);

  constructor(
    @InjectModel(DummyTestScore)
    private readonly testScoreModel: typeof DummyTestScore,
    @InjectModel(DummyApplicationStep)
    private readonly applicationStepModel: typeof DummyApplicationStep,
    @InjectModel(DummyLectureLanguage)
    private readonly lectureLanguageModel: typeof DummyLectureLanguage,
    @InjectModel(DummySocialPlatform)
    private readonly socialPlatformModel: typeof DummySocialPlatform,
    private readonly auditService: AuditClientService
  ) {}

  // ===== Test Score Methods =====
  async createTestScore(data: any) {
    try {
      const testScore = await this.testScoreModel.create({
        title: data.title,
        modules: data.modules,
        isActive: true,
      });

      // Log audit
      await this.auditService.logAction({
        userId: data.userId,
        userRole: data.userRole,
        action: 'CREATE',
        entityType: 'DummyTestScore',
        entityId: testScore.id.toString(),
        details: `Created test score: ${data.title}`,
        ipAddress: data.ipAddress,
        userAgent: data.userAgent
      });

      return testScore;
    } catch (error) {
      this.logger.error('Failed to create test score', error.stack);
      throw error;
    }
  }

  async deleteTestScore(data: any) {
    try {
      const testScore = await this.testScoreModel.findByPk(data.id);
      if (!testScore) {
        throw new NotFoundException('Test score not found');
      }

      await testScore.destroy();

      // Log audit
      await this.auditService.logAction({
        userId: data.userId,
        userRole: data.userRole,
        action: 'DELETE',
        entityType: 'DummyTestScore',
        entityId: data.id.toString(),
        details: `Deleted test score: ${testScore.title}`,
        ipAddress: data.ipAddress,
        userAgent: data.userAgent
      });

      return { success: true };
    } catch (error) {
      this.logger.error('Failed to delete test score', error.stack);
      throw error;
    }
  }

  async listTestScores(data: any) {
    try {
      const { page = 1, limit = 10, search, includeInactive = false } = data;
      const offset = (page - 1) * limit;

      const whereClause: any = {};
      
      if (!includeInactive) {
        whereClause.isActive = true;
      }

      if (search) {
        whereClause.title = {
          [Op.iLike]: `%${search}%`,
        };
      }

      const { rows: testScores, count: total } = await this.testScoreModel.findAndCountAll({
        where: whereClause,
        limit,
        offset,
        order: [['createdAt', 'DESC']],
      });

      return {
        data: testScores,
        total,
        page,
        limit,
      };
    } catch (error) {
      this.logger.error('Failed to list test scores', error.stack);
      throw error;
    }
  }

  // ===== Application Step Methods =====
  async createApplicationStep(data: any) {
    try {
      const applicationStep = await this.applicationStepModel.create({
        title: data.title,
        steps: data.steps,
        isActive: true,
      });

      // Log audit
      await this.auditService.logAction({
        userId: data.userId,
        userRole: data.userRole,
        action: 'CREATE',
        entityType: 'DummyApplicationStep',
        entityId: applicationStep.id.toString(),
        details: `Created application step: ${data.title}`,
        ipAddress: data.ipAddress,
        userAgent: data.userAgent
      });

      return applicationStep;
    } catch (error) {
      this.logger.error('Failed to create application step', error.stack);
      throw error;
    }
  }

  async deleteApplicationStep(data: any) {
    try {
      const applicationStep = await this.applicationStepModel.findByPk(data.id);
      if (!applicationStep) {
        throw new NotFoundException('Application step not found');
      }

      await applicationStep.destroy();

      // Log audit
      await this.auditService.logAction({
        userId: data.userId,
        userRole: data.userRole,
        action: 'DELETE',
        entityType: 'DummyApplicationStep',
        entityId: data.id.toString(),
        details: `Deleted application step: ${applicationStep.title}`,
        ipAddress: data.ipAddress,
        userAgent: data.userAgent
      });

      return { success: true };
    } catch (error) {
      this.logger.error('Failed to delete application step', error.stack);
      throw error;
    }
  }

  async listApplicationSteps(data: any) {
    try {
      const { page = 1, limit = 10, search, includeInactive = false } = data;
      const offset = (page - 1) * limit;

      const whereClause: any = {};
      
      if (!includeInactive) {
        whereClause.isActive = true;
      }

      if (search) {
        whereClause.title = {
          [Op.iLike]: `%${search}%`,
        };
      }

      const { rows: applicationSteps, count: total } = await this.applicationStepModel.findAndCountAll({
        where: whereClause,
        limit,
        offset,
        order: [['createdAt', 'DESC']],
      });

      return {
        data: applicationSteps,
        total,
        page,
        limit,
      };
    } catch (error) {
      this.logger.error('Failed to list application steps', error.stack);
      throw error;
    }
  }

  // ===== Lecture Language Methods =====
  async createLectureLanguage(data: any) {
    try {
      const lectureLanguage = await this.lectureLanguageModel.create({
        languages: data.languages,
        isActive: true,
      });

      // Log audit
      await this.auditService.logAction({
        userId: data.userId,
        userRole: data.userRole,
        action: 'CREATE',
        entityType: 'DummyLectureLanguage',
        entityId: lectureLanguage.id.toString(),
        details: `Created lecture language with ${data.languages.length} languages`,
        ipAddress: data.ipAddress,
        userAgent: data.userAgent
      });

      return lectureLanguage;
    } catch (error) {
      this.logger.error('Failed to create lecture language', error.stack);
      throw error;
    }
  }

  async deleteLectureLanguage(data: any) {
    try {
      const lectureLanguage = await this.lectureLanguageModel.findByPk(data.id);
      if (!lectureLanguage) {
        throw new NotFoundException('Lecture language not found');
      }

      await lectureLanguage.destroy();

      // Log audit
      await this.auditService.logAction({
        userId: data.userId,
        userRole: data.userRole,
        action: 'DELETE',
        entityType: 'DummyLectureLanguage',
        entityId: data.id.toString(),
        details: `Deleted lecture language`,
        ipAddress: data.ipAddress,
        userAgent: data.userAgent
      });

      return { success: true };
    } catch (error) {
      this.logger.error('Failed to delete lecture language', error.stack);
      throw error;
    }
  }

  async listLectureLanguages(data: any) {
    try {
      const { page = 1, limit = 10, includeInactive = false } = data;
      const offset = (page - 1) * limit;

      const whereClause: any = {};

      if (!includeInactive) {
        whereClause.isActive = true;
      }

      const { rows: lectureLanguages, count: total } = await this.lectureLanguageModel.findAndCountAll({
        where: whereClause,
        limit,
        offset,
        order: [['createdAt', 'DESC']],
      });

      return {
        data: lectureLanguages,
        total,
        page,
        limit,
      };
    } catch (error) {
      this.logger.error('Failed to list lecture languages', error.stack);
      throw error;
    }
  }

  // ===== Social Platform Methods =====
  async createSocialPlatform(data: any) {
    try {
      const socialPlatform = await this.socialPlatformModel.create({
        platforms: data.platforms,
        isActive: true,
      });

      // Log audit
      await this.auditService.logAction({
        userId: data.userId,
        userRole: data.userRole,
        action: 'CREATE',
        entityType: 'DummySocialPlatform',
        entityId: socialPlatform.id.toString(),
        details: `Created social platform with ${data.platforms.length} platforms`,
        ipAddress: data.ipAddress,
        userAgent: data.userAgent
      });

      return socialPlatform;
    } catch (error) {
      this.logger.error('Failed to create social platform', error.stack);
      throw error;
    }
  }

  async deleteSocialPlatform(data: any) {
    try {
      const socialPlatform = await this.socialPlatformModel.findByPk(data.id);
      if (!socialPlatform) {
        throw new NotFoundException('Social platform not found');
      }

      await socialPlatform.destroy();

      // Log audit
      await this.auditService.logAction({
        userId: data.userId,
        userRole: data.userRole,
        action: 'DELETE',
        entityType: 'DummySocialPlatform',
        entityId: data.id.toString(),
        details: `Deleted social platform`,
        ipAddress: data.ipAddress,
        userAgent: data.userAgent
      });

      return { success: true };
    } catch (error) {
      this.logger.error('Failed to delete social platform', error.stack);
      throw error;
    }
  }

  async listSocialPlatforms(data: any) {
    try {
      const { page = 1, limit = 10, includeInactive = false } = data;
      const offset = (page - 1) * limit;

      const whereClause: any = {};

      if (!includeInactive) {
        whereClause.isActive = true;
      }

      const { rows: socialPlatforms, count: total } = await this.socialPlatformModel.findAndCountAll({
        where: whereClause,
        limit,
        offset,
        order: [['createdAt', 'DESC']],
      });

      return {
        data: socialPlatforms,
        total,
        page,
        limit,
      };
    } catch (error) {
      this.logger.error('Failed to list social platforms', error.stack);
      throw error;
    }
  }
}
