import {
  Table,
  Column,
  DataType,
  Model,
  PrimaryKey,
  AutoIncrement,
} from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';

@Table({
  tableName: 'dummy_social_platforms',
  timestamps: true,
})
export class DummySocialPlatform extends BaseModel {
  @Column({
    type: DataType.JSON,
    allowNull: false,
  })
  platforms!: string[];

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: true,
  })
  isActive!: boolean;
}
