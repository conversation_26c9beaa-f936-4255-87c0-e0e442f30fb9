import { Controller, Logger } from '@nestjs/common';
import { GrpcMethod } from '@nestjs/microservices';
import { DummyService } from './dummy.service';

@Controller()
export class DummyGrpcController {
  private readonly logger = new Logger(DummyGrpcController.name);

  constructor(private readonly dummyService: DummyService) {}

  // ===== Test Score Methods =====
  @GrpcMethod('UniversityService', 'CreateTestScore')
  async createTestScore(data: any) {
    try {
      this.logger.log(`Creating test score: ${data.title}`);
      const result = await this.dummyService.createTestScore(data);
      
      return {
        status: 200,
        message: 'Test score created successfully',
        data: result,
        error: null,
      };
    } catch (error) {
      this.logger.error('Failed to create test score', error.stack);
      return {
        status: 500,
        message: 'Failed to create test score',
        data: null,
        error: {
          code: 'INTERNAL_ERROR',
          message: error.message,
          details: error.stack,
        },
      };
    }
  }

  @GrpcMethod('UniversityService', 'DeleteTestScore')
  async deleteTestScore(data: any) {
    try {
      this.logger.log(`Deleting test score with ID: ${data.id}`);
      await this.dummyService.deleteTestScore(data);
      
      return {
        status: 200,
        message: 'Test score deleted successfully',
        error: null,
      };
    } catch (error) {
      this.logger.error('Failed to delete test score', error.stack);
      return {
        status: error.status || 500,
        message: 'Failed to delete test score',
        error: {
          code: 'INTERNAL_ERROR',
          message: error.message,
          details: error.stack,
        },
      };
    }
  }

  @GrpcMethod('UniversityService', 'ListTestScores')
  async listTestScores(data: any) {
    try {
      this.logger.log('Listing test scores');
      const result = await this.dummyService.listTestScores(data);
      
      return {
        status: 200,
        message: 'Test scores retrieved successfully',
        data: result.data,
        total: result.total,
        error: null,
      };
    } catch (error) {
      this.logger.error('Failed to list test scores', error.stack);
      return {
        status: 500,
        message: 'Failed to list test scores',
        data: [],
        total: 0,
        error: {
          code: 'INTERNAL_ERROR',
          message: error.message,
          details: error.stack,
        },
      };
    }
  }

  // ===== Application Step Methods =====
  @GrpcMethod('UniversityService', 'CreateApplicationStep')
  async createApplicationStep(data: any) {
    try {
      this.logger.log(`Creating application step: ${data.title}`);
      const result = await this.dummyService.createApplicationStep(data);
      
      return {
        status: 200,
        message: 'Application step created successfully',
        data: result,
        error: null,
      };
    } catch (error) {
      this.logger.error('Failed to create application step', error.stack);
      return {
        status: 500,
        message: 'Failed to create application step',
        data: null,
        error: {
          code: 'INTERNAL_ERROR',
          message: error.message,
          details: error.stack,
        },
      };
    }
  }

  @GrpcMethod('UniversityService', 'DeleteApplicationStep')
  async deleteApplicationStep(data: any) {
    try {
      this.logger.log(`Deleting application step with ID: ${data.id}`);
      await this.dummyService.deleteApplicationStep(data);
      
      return {
        status: 200,
        message: 'Application step deleted successfully',
        error: null,
      };
    } catch (error) {
      this.logger.error('Failed to delete application step', error.stack);
      return {
        status: error.status || 500,
        message: 'Failed to delete application step',
        error: {
          code: 'INTERNAL_ERROR',
          message: error.message,
          details: error.stack,
        },
      };
    }
  }

  @GrpcMethod('UniversityService', 'ListApplicationSteps')
  async listApplicationSteps(data: any) {
    try {
      this.logger.log('Listing application steps');
      const result = await this.dummyService.listApplicationSteps(data);
      
      return {
        status: 200,
        message: 'Application steps retrieved successfully',
        data: result.data,
        total: result.total,
        error: null,
      };
    } catch (error) {
      this.logger.error('Failed to list application steps', error.stack);
      return {
        status: 500,
        message: 'Failed to list application steps',
        data: [],
        total: 0,
        error: {
          code: 'INTERNAL_ERROR',
          message: error.message,
          details: error.stack,
        },
      };
    }
  }

  // ===== Lecture Language Methods =====
  @GrpcMethod('UniversityService', 'CreateLectureLanguage')
  async createLectureLanguage(data: any) {
    try {
      this.logger.log(`Creating lecture language with ${data.languages.length} languages`);
      const result = await this.dummyService.createLectureLanguage(data);

      return {
        status: 200,
        message: 'Lecture language created successfully',
        data: result,
        error: null,
      };
    } catch (error) {
      this.logger.error('Failed to create lecture language', error.stack);
      return {
        status: 500,
        message: 'Failed to create lecture language',
        data: null,
        error: {
          code: 'INTERNAL_ERROR',
          message: error.message,
          details: error.stack,
        },
      };
    }
  }

  @GrpcMethod('UniversityService', 'DeleteLectureLanguage')
  async deleteLectureLanguage(data: any) {
    try {
      this.logger.log(`Deleting lecture language with ID: ${data.id}`);
      await this.dummyService.deleteLectureLanguage(data);

      return {
        status: 200,
        message: 'Lecture language deleted successfully',
        error: null,
      };
    } catch (error) {
      this.logger.error('Failed to delete lecture language', error.stack);
      return {
        status: error.status || 500,
        message: 'Failed to delete lecture language',
        error: {
          code: 'INTERNAL_ERROR',
          message: error.message,
          details: error.stack,
        },
      };
    }
  }

  @GrpcMethod('UniversityService', 'ListLectureLanguages')
  async listLectureLanguages(data: any) {
    try {
      this.logger.log('Listing lecture languages');
      const result = await this.dummyService.listLectureLanguages(data);

      return {
        status: 200,
        message: 'Lecture languages retrieved successfully',
        data: result.data,
        total: result.total,
        error: null,
      };
    } catch (error) {
      this.logger.error('Failed to list lecture languages', error.stack);
      return {
        status: 500,
        message: 'Failed to list lecture languages',
        data: [],
        total: 0,
        error: {
          code: 'INTERNAL_ERROR',
          message: error.message,
          details: error.stack,
        },
      };
    }
  }

  // ===== Social Platform Methods =====
  @GrpcMethod('UniversityService', 'CreateSocialPlatform')
  async createSocialPlatform(data: any) {
    try {
      this.logger.log(`Creating social platform with ${data.platforms.length} platforms`);
      const result = await this.dummyService.createSocialPlatform(data);

      return {
        status: 200,
        message: 'Social platform created successfully',
        data: result,
        error: null,
      };
    } catch (error) {
      this.logger.error('Failed to create social platform', error.stack);
      return {
        status: 500,
        message: 'Failed to create social platform',
        data: null,
        error: {
          code: 'INTERNAL_ERROR',
          message: error.message,
          details: error.stack,
        },
      };
    }
  }

  @GrpcMethod('UniversityService', 'DeleteSocialPlatform')
  async deleteSocialPlatform(data: any) {
    try {
      this.logger.log(`Deleting social platform with ID: ${data.id}`);
      await this.dummyService.deleteSocialPlatform(data);

      return {
        status: 200,
        message: 'Social platform deleted successfully',
        error: null,
      };
    } catch (error) {
      this.logger.error('Failed to delete social platform', error.stack);
      return {
        status: error.status || 500,
        message: 'Failed to delete social platform',
        error: {
          code: 'INTERNAL_ERROR',
          message: error.message,
          details: error.stack,
        },
      };
    }
  }

  @GrpcMethod('UniversityService', 'ListSocialPlatforms')
  async listSocialPlatforms(data: any) {
    try {
      this.logger.log('Listing social platforms');
      const result = await this.dummyService.listSocialPlatforms(data);

      return {
        status: 200,
        message: 'Social platforms retrieved successfully',
        data: result.data,
        total: result.total,
        error: null,
      };
    } catch (error) {
      this.logger.error('Failed to list social platforms', error.stack);
      return {
        status: 500,
        message: 'Failed to list social platforms',
        data: [],
        total: 0,
        error: {
          code: 'INTERNAL_ERROR',
          message: error.message,
          details: error.stack,
        },
      };
    }
  }
}
