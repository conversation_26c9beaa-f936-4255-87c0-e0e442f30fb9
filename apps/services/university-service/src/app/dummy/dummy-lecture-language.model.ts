import {
  Table,
  Column,
  DataType,
  Model,
  PrimaryKey,
  AutoIncrement,
} from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';

@Table({
  tableName: 'dummy_lecture_languages',
  timestamps: true,
})
export class DummyLectureLanguage extends BaseModel {
  @Column({
    type: DataType.JSON,
    allowNull: false,
  })
  languages!: string[];

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: true,
  })
  isActive!: boolean;
}
