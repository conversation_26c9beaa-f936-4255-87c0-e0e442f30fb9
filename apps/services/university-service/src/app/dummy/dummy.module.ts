import { <PERSON>du<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { DummyService } from './dummy.service';
import { DummyGrpcController } from './dummy.grpc.controller';
import { AuditClientService } from '../audit.service';
import { DummyTestScore } from './dummy-test-score.model';
import { DummyApplicationStep } from './dummy-application-step.model';
import { DummyLectureLanguage } from './dummy-lecture-language.model';
import { DummySocialPlatform } from './dummy-social-platform.model';

@Module({
  imports: [
    SequelizeModule.forFeature([
      DummyTestScore,
      DummyApplicationStep,
      DummyLectureLanguage,
      DummySocialPlatform,
    ]),
  ],
  controllers: [DummyGrpcController],
  providers: [DummyService, AuditClientService],
  exports: [DummyService],
})
export class DummyModule {}
