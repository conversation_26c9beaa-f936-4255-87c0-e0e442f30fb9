import {
  Table,
  Column,
  DataType,
  Model,
  PrimaryKey,
  AutoIncrement,
} from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';

@Table({
  tableName: 'dummy_application_steps',
  timestamps: true,
})
export class DummyApplicationStep extends BaseModel {

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  title!: string;

  @Column({
    type: DataType.JSON,
    allowNull: false,
  })
  steps!: string[];

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: true,
  })
  isActive!: boolean;
}
