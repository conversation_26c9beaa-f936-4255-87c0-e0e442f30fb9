import { Controller, Logger } from '@nestjs/common';
import { GrpcMethod } from '@nestjs/microservices';
import { IntakeService } from './intake.service';
import { AppService } from '../app.service';
import { AuditClientService } from '../audit.service';

@Controller()
export class IntakeGrpcController {
  private readonly logger = new Logger(IntakeGrpcController.name);

  constructor(
    private readonly intakeService: IntakeService,
    private readonly appService: AppService,
    private readonly auditService: AuditClientService
  ) {}

  @GrpcMethod('UniversityService', 'CreateIntake')
  async createIntake(request: any) {
    const startTime = Date.now();
    try {
      this.logger.log(
        `gRPC CreateIntake request for universityId: ${request.universityId}`
      );
      const result = await this.intakeService.create(request);

      this.appService.trackProcessingDuration(
        'intake_create_grpc_success',
        (Date.now() - startTime) / 1000
      );

      this.auditService.logAction({
        action: 'CREATE_INTAKE',
        entityType: 'Intake',
        entityId: request.universityId.toString(), // Could log multiple IDs
        userId: request.userId || 'undefined',
        userRole: request.userRole || 'system',
        details: request,
      });

      return {
        status: 200,
        message: result.message,
        data: null,
        error: null,
      };
    } catch (error) {
      this.logger.error(`CreateIntake error: ${error.message}`, error.stack);
      return {
        status: error?.response?.statusCode || 500,
        message: error?.response?.message || 'Failed to create intake',
        data: null,
        error: { details: error.message },
      };
    }
  }

  @GrpcMethod('UniversityService', 'GetIntake')
  async getIntake(request: { id: number }) {
    const startTime = Date.now();
    try {
      const result = await this.intakeService.findOne(request.id);
      this.appService.trackProcessingDuration(
        'intake_get_grpc_success',
        (Date.now() - startTime) / 1000
      );

      return {
        status: 200,
        message: 'Intake retrieved successfully',
        data: {
          id: result.id,
          name: result.name,
          startDate: result.startDate.toISOString(),
          endDate: result.endDate.toISOString(),
          universityId: result.universityId,
          applicationDates: result.studentTypes.map((date) => ({
            studentType: date.studentType,
            applicationOpen: date.applicationOpen.toISOString(),
            applicationEnd: date.applicationDeadline.toISOString(),
            enrollmentDeadline: date.enrollmentDeadline.toISOString(),
            // actions: date.actions.toString(),
          })),
        },
        error: null,
      };
    } catch (error) {
      this.logger.error(`GetIntake error: ${error.message}`, error.stack);
      this.appService.trackProcessingDuration(
        'intake_get_grpc_error',
        (Date.now() - startTime) / 1000
      );

      return {
        status: error?.response?.statusCode || 500,
        message: error?.response?.message || 'Failed to retrieve intake',
        data: null,
        error: { details: error.message },
      };
    }
  }

  @GrpcMethod('UniversityService', 'UpdateIntake')
  async updateIntake(request: {
    universityId: number;
    userId: string;
    intakes: {
      id?: number;
      name: string;
      startDate: string;
      endDate: string;
      applicationDates: {
        studentType: string;
        applicationOpen: string;
        applicationEnd: string;
        enrollmentDeadline: string;
        actions: string;
      }[];
    }[];
  }) {
    const startTime = Date.now();
    try {
      console.log('fullIntake in grpc  service', request);
      const results = await this.intakeService.bulkUpsertForUniversity(
        request.universityId,
        request.intakes
      );

      // Audit each change
      for (const result of results) {
        await this.auditService.logAction({
          action: result._isNew ? 'CREATE_INTAKE' : 'UPDATE_INTAKE',
          entityType: 'Intake',
          entityId: result.id.toString(),
          userId: request.userId,
          details: {
            id: result.id,
            name: result.name,
            startDate: result.startDate,
            endDate: result.endDate,
          },
        });
      }

      this.appService.trackProcessingDuration(
        'intake_upsert_grpc_success',
        (Date.now() - startTime) / 1000
      );

      return {
        status: 200,
        message: 'Intakes processed successfully',
        data: results.map((r) => ({
          id: r.id,
          universityId: r.universityId,
          name: r.name,
          startDate: r.startDate.toISOString(),
          endDate: r.endDate.toISOString(),
          createdAt: r.createdAt?.toISOString?.() ?? '',
          updatedAt: r.updatedAt?.toISOString?.() ?? '',
          applicationDates:
            r.studentTypes?.map((a) => ({
              studentType: a.studentType,
              applicationOpen: a.applicationOpen?.toISOString?.() ?? '',
              applicationEnd: a.applicationDeadline?.toISOString?.() ?? '',
              enrollmentDeadline: a.enrollmentDeadline?.toISOString?.() ?? '',
            })) ?? [],
        })),
        error: null,
      };
    } catch (error) {
      this.logger.error(`UpdateIntake error: ${error.message}`, error.stack);
      this.appService.trackProcessingDuration(
        'intake_upsert_grpc_error',
        (Date.now() - startTime) / 1000
      );

      return {
        status: error?.response?.statusCode || 500,
        message: error?.response?.message || 'Failed to upsert intakes',
        data: null,
        error: { details: error.message },
      };
    }
  }

  @GrpcMethod('UniversityService', 'DeleteIntake')
  async deleteIntake(request: { id: number }) {
    const startTime = Date.now();
    try {
      await this.intakeService.remove(request.id);
      this.appService.trackProcessingDuration(
        'intake_delete_grpc_success',
        (Date.now() - startTime) / 1000
      );

      this.auditService.logAction({
        action: 'DELETE_INTAKE',
        entityType: 'Intake',
        entityId: request.id.toString(),
        userId: 'system',
        details: { id: request.id },
      });

      return {
        status: 200,
        message: 'Intake deleted successfully',
        data: { success: true },
        error: null,
      };
    } catch (error) {
      this.logger.error(`DeleteIntake error: ${error.message}`, error.stack);
      this.appService.trackProcessingDuration(
        'intake_delete_grpc_error',
        (Date.now() - startTime) / 1000
      );

      return {
        status: error?.response?.statusCode || 500,
        message: error?.response?.message || 'Failed to delete intake',
        data: null,
        error: { details: error.message },
      };
    }
  }

  @GrpcMethod('UniversityService', 'ListIntakesByUniversity')
  async listIntakesByUniversity(request: {
    universityId: number;
    page?: number;
    limit?: number;
  }) {
    const startTime = Date.now();
    try {
      const page = request.page || 1;
      const limit = request.limit || 10;
      const result = await this.intakeService.findAllByUniversity(
        request.universityId,
        page,
        limit
      );

      console.log('IntakeLists', result);
      this.appService.trackProcessingDuration(
        'intake_list_grpc_success',
        (Date.now() - startTime) / 1000
      );
      return {
        status: 200,
        message: 'Intakes retrieved successfully',
        data: result.rows.map((intake) => ({
          id: intake.id,
          name: intake.name,
          startDate: intake.startDate.toISOString(),
          endDate: intake.endDate.toISOString(),
          applicationDates: intake.studentTypes.map((date) => ({
            studentType: date.studentType,
            applicationOpen: date.applicationOpen?.toISOString?.() ?? '',
            applicationEnd: date.applicationDeadline?.toISOString?.() ?? '',
            enrollmentDeadline: date.enrollmentDeadline?.toISOString?.() ?? '',
            // actions: date.actions || '', // default if needed
          })),
        })),
      };
    } catch (error) {
      this.logger.error(
        `ListIntakesByUniversity error: ${error.message}`,
        error.stack
      );
      this.appService.trackProcessingDuration(
        'intake_list_grpc_error',
        (Date.now() - startTime) / 1000
      );

      return {
        status: error?.response?.statusCode || 500,
        message: error?.response?.message || 'Failed to retrieve intakes',
        data: null,
        error: { details: error.message },
      };
    }
  }
}
