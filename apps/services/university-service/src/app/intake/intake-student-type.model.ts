import { Table, Column, DataType, ForeignKey, BelongsTo } from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { Intake } from './intake.model';

@Table({ tableName: 'intake_student_types' })
export class IntakeStudentType extends BaseModel {
  @ForeignKey(() => Intake)
  @Column(DataType.BIGINT)
  intakeId!: number;

  @BelongsTo(() => Intake)
  intake!: Intake;

  @Column(DataType.STRING)
  studentType!: string;

  @Column(DataType.DATE)
  applicationOpen!: Date;

  @Column(DataType.DATE)
  applicationDeadline!: Date;

  @Column(DataType.DATE)
  enrollmentDeadline!: Date;
}
