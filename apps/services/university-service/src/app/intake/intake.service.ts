import {
  Injectable,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Intake } from './intake.model';
import { IntakeStudentType } from './intake-student-type.model';
import { Op } from 'sequelize';
import { AppService } from '../app.service';

@Injectable()
export class IntakeService {
  constructor(
    @InjectModel(Intake)
    private intakeModel: typeof Intake,

    @InjectModel(IntakeStudentType)
    private studentTypeModel: typeof IntakeStudentType,

    private readonly appService: AppService
  ) {}

  async create(payload: {
    universityId: number;
    intake: {
      name: string;
      startDate: string;
      endDate: string;
      applicationDates: {
        studentType: string;
        applicationOpen: string;
        applicationEnd: string;
        enrollmentDeadline: string;
        actions: boolean | string;
      }[];
    }[];
  }): Promise<{ success: boolean; message: string }> {
    const startTime = Date.now();

    try {
      for (const intake of payload.intake) {
        // Check for name conflict for same university
        const existing = await this.intakeModel.findOne({
          where: {
            name: intake.name,
            universityId: payload.universityId,
          },
        });

        if (existing) {
          throw new ConflictException(
            `Intake with name "${intake.name}" already exists for this university`
          );
        }

        const newIntake = await this.intakeModel.create({
          universityId: payload.universityId,
          name: intake.name,
          startDate: new Date(intake.startDate),
          endDate: new Date(intake.endDate),
        });

        if (intake.applicationDates?.length > 0) {
          const dateRecords = intake.applicationDates.map((app) => ({
            intakeId: newIntake.id,
            studentType: app.studentType,
            applicationOpen: new Date(app.applicationOpen),
            applicationDeadline: new Date(app.applicationEnd),
            enrollmentDeadline: new Date(app.enrollmentDeadline),
            actions: app.actions === true || app.actions === 'true',
          }));

          await this.studentTypeModel.bulkCreate(dateRecords);
        }
      }

      this.appService.trackProcessingDuration(
        'intake_create_success',
        (Date.now() - startTime) / 1000
      );

      return {
        success: true,
        message: 'Intakes created successfully',
      };
    } catch (error) {
      this.appService.trackProcessingDuration(
        'intake_create_error',
        (Date.now() - startTime) / 1000
      );
      throw error;
    }
  }

  async findAllByUniversity(
    universityId: number,
    page: number = 1,
    limit: number = 10
  ): Promise<{
    rows: Intake[];
    count: number;
  }> {
    return this.intakeModel.findAndCountAll({
      where: { universityId },
      offset: (page - 1) * limit,
      limit,
      include: [IntakeStudentType],
      order: [['createdAt', 'DESC']],
    });
  }

  async findOne(id: number): Promise<Intake> {
    const intake = await this.intakeModel.findByPk(id, {
      include: [IntakeStudentType],
    });

    if (!intake) {
      throw new NotFoundException(`Intake with ID ${id} not found`);
    }

    return intake;
  }

  async bulkUpsertForUniversity(
    universityId: number,
    intakes: Array<{
      id?: number;
      name: string;
      startDate: string;
      endDate: string;
      applicationDates: {
        studentType: string;
        applicationOpen: string;
        applicationEnd: string;
        enrollmentDeadline: string;
        actions: string;
      }[];
    }>
  ): Promise<(Intake & { _isNew?: boolean })[]> {
    const results = [];

    for (const intake of intakes) {
      const isNew = !intake.id || intake.id === 0;
      let intakeRecord;
      if (!isNew) {
        intakeRecord = await this.intakeModel.findOne({
          where: { id: intake.id, universityId },
        });
        if (!intakeRecord)
          throw new NotFoundException(`Intake with ID ${intake.id} not found`);

        const existing = await this.intakeModel.findOne({
          where: {
            id: { [Op.ne]: intake.id },
            name: intake.name,
            universityId,
          },
        });
        if (existing) {
          throw new ConflictException(
            `Intake with name "${intake.name}" already exists`
          );
        }

        await intakeRecord.update({
          name: intake.name,
          startDate: new Date(intake.startDate),
          endDate: new Date(intake.endDate),
        });

        await this.studentTypeModel.destroy({
          where: { intakeId: intakeRecord.id },
        });
      } else {
        intakeRecord = await this.intakeModel.create({
          universityId,
          name: intake.name,
          startDate: new Date(intake.startDate),
          endDate: new Date(intake.endDate),
        });
      }

      const applicationDates = intake.applicationDates.map((entry) => ({
        intakeId: intakeRecord.id,
        studentType: entry.studentType,
        applicationOpen: new Date(entry.applicationOpen),
        applicationEnd: new Date(entry.applicationEnd),
        enrollmentDeadline: new Date(entry.enrollmentDeadline),
        actions: entry.actions,
      }));

      await this.studentTypeModel.bulkCreate(applicationDates);

      // ✅ Reload with applicationDates
      await intakeRecord.reload({ include: ['studentTypes'] });

      (intakeRecord as any)._isNew = isNew;
      intakeRecord.universityId = universityId; // Just in case
      results.push(intakeRecord);
    }

    return results;
  }

  async remove(id: number): Promise<void> {
    const deletedCount = await this.intakeModel.destroy({ where: { id } });

    if (deletedCount === 0) {
      throw new NotFoundException(`Intake with ID ${id} not found`);
    }

    await this.studentTypeModel.destroy({ where: { intakeId: id } });
  }
}
