import {
  Table,
  Column,
  DataType,
  ForeignKey,
  BelongsTo,
  HasMany,
  BelongsToMany,
} from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { University } from '../university/university.model';
import { IntakeStudentType } from './intake-student-type.model';
import { ProgramLevel } from '../program-level/programLevel.model';
import { ProgramLevelIntake } from '../program-level/programLevelIntake.model';

@Table({ tableName: 'intakes' })
export class Intake extends BaseModel {
  @ForeignKey(() => University)
  @Column(DataType.BIGINT)
  universityId!: number;

  @BelongsTo(() => University)
  university!: University;

  @Column(DataType.STRING)
  name!: string;

  @Column(DataType.DATE)
  startDate!: Date;

  @Column(DataType.DATE)
  endDate!: Date;

  @HasMany(() => IntakeStudentType, {
    as: 'studentTypes',
    foreignKey: 'intakeId',
  })
  studentTypes!: IntakeStudentType[];

  @BelongsToMany(() => ProgramLevel, {
    through: () => ProgramLevelIntake,
    foreignKey: 'intakeId',
    otherKey: 'programLevelId',
  })
  programLevels!: ProgramLevel[];
}
