import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { Intake } from './intake.model';
import { IntakeStudentType } from './intake-student-type.model';
import { IntakeGrpcController } from './intake.grpc.controller';
import { AuditClientService } from '../audit.service';
import { IntakeService } from './intake.service';

@Module({
  imports: [
    SequelizeModule.forFeature([Intake, IntakeStudentType]),
  ],
  controllers: [IntakeGrpcController],
  providers: [IntakeService, AuditClientService,Intake],
  exports: [IntakeService],
})
export class IntakeModule {}
