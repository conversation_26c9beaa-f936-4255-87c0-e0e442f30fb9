import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { Client, ClientGrpc, Transport } from '@nestjs/microservices';
import { Observable, throwError } from 'rxjs';
import { catchError, timeout } from 'rxjs/operators';
import { join } from 'path';

// gRPC service interface matching your proto definition
interface AuthService {
  CreateUniversity(data: {
    name: string;
    address: string;
    country: string;
    phone: string;
    email: string;
    ipAddress?: string;
    userAgent?: string;
  }): Observable<{ success: boolean; message: string }>;
}

@Injectable()
export class AuthClientService implements OnModuleInit {
  private readonly logger = new Logger(AuthClientService.name);

  @Client({
    transport: Transport.GRPC,
    options: {
      package: 'auth',
      protoPath: join(
        process.cwd(),
        'libs/shared/dto/src/lib/auth/auth.proto'
      ),
      url: process.env.AUTH_SERVICE_URL || 'auth-service:50052',
      loader: {
        keepCase: true,
        longs: String,
        enums: String,
        defaults: true,
        oneofs: true,
      },
    },
  })
  private readonly client: ClientGrpc;

  private authService: AuthService;

  onModuleInit() {
    this.authService = this.client.getService<AuthService>('AuthService');
  }

  private handleError(operation = 'operation') {
    return (error: any) => {
      this.logger.error(`${operation} failed: ${error.message}`, error.stack);
      return throwError(() => error);
    };
  }

  createUniversity(data: {
    name: string;
    address: string;
    country: string;
    phone: string;
    email: string;
    ipAddress?: string;
    userAgent?: string;
  }): Observable<{ success: boolean; message: string }> {
    return this.authService
      .CreateUniversity(data)
      .pipe(
        timeout(5000), 
        catchError(this.handleError('createUniversity'))
      );
  }
}
