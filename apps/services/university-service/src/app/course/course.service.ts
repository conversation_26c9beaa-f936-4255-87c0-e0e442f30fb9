import {
  Injectable,
  NotFoundException,
  InternalServerErrorException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Sequelize } from 'sequelize-typescript';
import { Course } from './course.model';
import { CourseTestScore } from './courseTestScore.model';
import { ProgramLevelTestScore } from '../program-level/applicationTestScore.model';
import { CourseFeeCategory } from './courseFee.model';
import { ProgramLevelIntakeFee } from '../fee/fee.model';
import { ProgramLevelIntake } from '../program-level/programLevelIntake.model';
import { ProgramLevel } from '../program-level/programLevel.model';
import { Intake } from '../intake/intake.model';

@Injectable()
export class CourseService {
  constructor(
    @InjectModel(Course)
    private readonly courseModel: typeof Course,

    @InjectModel(CourseTestScore)
    private readonly courseTestScoreModel: typeof CourseTestScore,

    @InjectModel(CourseFeeCategory)
    private readonly courseFeeCategoryModel: typeof CourseFeeCategory,

    @InjectModel(ProgramLevelTestScore)
    private readonly testScoreModel: typeof ProgramLevelTestScore,

    @InjectModel(ProgramLevelIntakeFee)
    private readonly feeCategoryModel: typeof ProgramLevelIntakeFee,

    @InjectModel(ProgramLevelIntake)
    private readonly programLevelIntakeModel: typeof ProgramLevelIntake,

    @InjectModel(ProgramLevel)
    private readonly programLevelModel: typeof ProgramLevel,

    @InjectModel(Intake)
    private readonly intakeModel: typeof Intake,

    private readonly sequelize: Sequelize
  ) {}

  async create(payload: any) {
    const { testScores = [], feeCategories = [], ...courseData } = payload;
    const transaction = await this.sequelize.transaction();

    try {
      const newCoursePayload = {
        universityId: courseData.universityId,
        courseTitle: courseData.courseTitle,
        fieldOfStudyId: courseData.fieldOfStudy, // 👈 CORRECT KEY
        format: courseData.format,
        programLevelId: courseData.programLevelId,
        lastAcademic: courseData.lastAcademic,
        minimumGpa: courseData.minimumGpa,
        courseRank: courseData.courseRank,
        acceptanceRate: courseData.acceptanceRate,
        lectureLanguage: courseData.lectureLanguage,
        additionalRequirements: courseData.additionalRequirements,
      };

      const course = await this.courseModel.create(newCoursePayload, {
        transaction,
      });


      // ----- Handle Test Scores -----
      for (const ts of testScores) {
        let testScoreId = ts.id;

        if (ts.isNew) {
          const created = await this.testScoreModel.create(
            {
              test: ts.test,
              testScore: ts.testScore,
              programLevelId: course.programLevelId,
            },
            { transaction }
          );
          testScoreId = created.id;
        }

        await this.courseTestScoreModel.create(
          {
            courseId: course.id,
            testScoreId,
          },
          { transaction }
        );
      }

      // ----- Handle Fee Categories -----
      for (const fc of feeCategories) {
       let feeCategoryIds: (number | bigint)[] = [];

        if (fc.isNew) {
          for (const intakeId of fc.intakeIds) {
            // ✅ Find programLevelIntake for this programLevelId + intakeId
            const programLevelIntake =
              await this.programLevelIntakeModel.findOne({
                where: {
                  programLevelId: fc.programLevelId,
                  intakeId,
                },
                transaction,
              });

            if (!programLevelIntake) {
              throw new NotFoundException(
                `ProgramLevelIntake not found for programLevelId ${fc.programLevelId} and intakeId ${intakeId}`
              );
            }

            // ✅ Create ProgramLevelIntakeFee using programLevelIntakeId
            const createdFee = await this.feeCategoryModel.create(
              {
                universityId: fc.universityId,
                programLevelIntakeId: programLevelIntake.id,
                feeTitle: fc.feeTitle,
                tuitionFee: fc.tuitionFee,
                applicationFee: fc.applicationFee,
                applicationFeeChargedByUniversity:
                  fc.applicationFeeChargedByUniversity,
                applicationFeeChargedToStudent:
                  fc.applicationFeeChargedToStudent,
                paymentDueInDays: fc.paymentDueInDays,
                feeEffectiveDate: fc.feeEffectiveDate,
                applicationYearStart: fc.applicationYearStart,
                applicationYearEnd: fc.applicationYearEnd,
                isActive: fc.isActive,
                isRefundableToStudent: fc.isRefundableToStudent,
                isVisibleToStudent: fc.isVisibleToStudent,
              },
              { transaction }
            );

            feeCategoryIds.push(createdFee.id);
          }
        } else {
          feeCategoryIds.push(fc.id);
        }

        // ✅ Link fees to course
        for (const id of feeCategoryIds) {
          await this.courseFeeCategoryModel.create(
            {
              courseId: course.id,
              feeCategoryId: id,
            },
            { transaction }
          );
        }
      }

      await transaction.commit();
      const courseInfo = await this.courseModel.findByPk(course.id, {
        include: [
          {
            model: ProgramLevelTestScore,
            through: { attributes: [] },
            as: 'testScores', // MUST match your model
          },
          {
            model: ProgramLevelIntakeFee,
            through: { attributes: [] },
            as: 'feeCategories', // MUST match your model
          },
          // {
          //   model: FieldOfStudy,
          // },
        ],
      });

      return {
        id: Number(courseInfo.id),
        universityId: Number(courseInfo.universityId),
        courseTitle: courseInfo.courseTitle,
        fieldOfStudy: Number(courseInfo.fieldOfStudyId),
        format: courseInfo.format,
        programLevelId: Number(courseInfo.programLevelId),
        lastAcademic: courseInfo.lastAcademic,
        minimumGpa: courseInfo.minimumGpa,
        courseRank: courseInfo.courseRank,
        acceptanceRate: courseInfo.acceptanceRate,
        lectureLanguage: courseInfo.lectureLanguage,
        additionalRequirements: courseInfo.additionalRequirements,
        createdAt: courseInfo.createdAt?.toISOString(),
        updatedAt: courseInfo.updatedAt?.toISOString(),
        testScores:
          courseInfo.testScores?.map((ts) => ({
            id: Number(ts.id),
            test: ts.test,
            testScore: ts.testScore,
            programLevelId: Number(ts.programLevelId),
            isActive: ts.isActive,
            createdAt: ts.createdAt?.toISOString(),
            updatedAt: ts.updatedAt?.toISOString(),
          })) || [],
        feeCategories:
          courseInfo.feeCategories?.map((fc) => ({
            id: Number(fc.id),
            universityId: Number(fc.universityId),
            feeTitle: fc.feeTitle,
            tuitionFee: fc.tuitionFee,
            applicationFee: fc.applicationFee,
            applicationFeeChargedByUniversity:
              fc.applicationFeeChargedByUniversity,
            applicationFeeChargedToStudent: fc.applicationFeeChargedToStudent,
            paymentDueInDays: fc.paymentDueInDays,
            feeEffectiveDate: fc.feeEffectiveDate,
            applicationYearStart: fc.applicationYearStart,
            applicationYearEnd: fc.applicationYearEnd,
            isRefundableToStudent: fc.isRefundableToStudent,
            isVisibleToStudent: fc.isVisibleToStudent,
            isActive: fc.isActive,
            createdAt: fc.createdAt?.toISOString(),
            updatedAt: fc.updatedAt?.toISOString(),
          })) || [],
      };
    } catch (error) {
      if (!(transaction as any).finished) {
        await transaction.rollback();
      }

      console.error('Course creation failed:', error);
      if (error.errors) {
        console.error('Sequelize Validation Errors:', error.errors);
      }

      throw new InternalServerErrorException(
        `Course creation failed: ${error.message}`
      );
    }
  }

  async update(id: string, data: any) {
    const course = await this.courseModel.findByPk(id);
    if (!course) throw new NotFoundException('Course not found');
    return await course.update(data);
  }

  async findOne(id: string) {
    const course = await this.courseModel.findByPk(id);
    if (!course) throw new NotFoundException('Course not found');
    return course;
  }
  async findAllByUniversity(
    universityId: number,
    page: number,
    pageSize: number
  ) {
    const offset = (page - 1) * pageSize;

    const programLevels = await this.programLevelModel.findAll({
      where: { universityId },
      include: [
        {
          model: this.courseModel,
          as: 'courses',
          required: false,
          include: [
            {
              model: this.testScoreModel,
              as: 'testScores',
              through: { attributes: [] },
            },
            {
              model: this.feeCategoryModel,
              as: 'feeCategories',
              through: { attributes: [] },
              include: [
                {
                  model: this.programLevelIntakeModel,
                  as: 'programLevelIntake',
                  include: [
                    {
                      model: this.intakeModel,
                      as: 'intake',
                    },
                  ],
                },
              ],
            },
          ],
        },
      ],
      offset,
      limit: pageSize,
    });

    return programLevels;
  }

  async remove(id: string) {
    const course = await this.courseModel.findByPk(id);
    if (!course) throw new NotFoundException('Course not found');
    await this.courseModel.destroy({ where: { id } });
    return { success: true };
  }
}
