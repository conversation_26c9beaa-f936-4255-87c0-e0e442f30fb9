import { Table, Column, DataType, ForeignKey, BelongsTo } from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { Course } from './course.model';
import { ProgramLevelIntakeFee } from '../fee/fee.model';

@Table({ tableName: 'course_fee_categories' })
export class CourseFeeCategory extends BaseModel {
  @ForeignKey(() => Course)
  @Column(DataType.BIGINT)
  courseId!: number;

  @BelongsTo(() => Course)
  course!: Course;

  @ForeignKey(() => ProgramLevelIntakeFee)
  @Column(DataType.BIGINT)
  feeCategoryId!: number;

  @BelongsTo(() => ProgramLevelIntakeFee)
  feeCategory!: ProgramLevelIntakeFee;
}
