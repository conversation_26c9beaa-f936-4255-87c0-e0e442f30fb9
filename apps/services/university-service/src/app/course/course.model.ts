import {
  Table,
  Column,
  DataType,
  ForeignKey,
  BelongsTo,
  HasMany,
  BelongsToMany,
  Unique,
} from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { University } from '../university/university.model';
import { ProgramLevel } from '../program-level/programLevel.model';
import { FieldOfStudy } from '../field-of-study/field-of-study.model';
import { CourseTestScore } from './courseTestScore.model';
import { ProgramLevelTestScore } from '../program-level/applicationTestScore.model';
import { CourseFeeCategory } from './courseFee.model';
import { ProgramLevelIntakeFee } from '../fee/fee.model';

@Table({ tableName: 'courses' })
export class Course extends BaseModel {
  @ForeignKey(() => University)
  @Column(DataType.BIGINT)
  universityId!: number;

  @BelongsTo(() => University)
  university!: University;

  @Unique
  @Column(DataType.STRING)
  courseTitle!: string;

  @ForeignKey(() => FieldOfStudy)
  @Column(DataType.BIGINT)
  fieldOfStudyId!: number;

  @BelongsTo(() => FieldOfStudy)
  fieldOfStudy!: FieldOfStudy;

  @Column(DataType.STRING)
  format!: string;

  @ForeignKey(() => ProgramLevel)
  @Column(DataType.BIGINT)
  programLevelId!: number;

  @BelongsTo(() => ProgramLevel)
  programLevel!: ProgramLevel;

  @Column(DataType.STRING)
  lastAcademic!: string;

  @Column(DataType.STRING)
  minimumGpa!: string;

  @Column(DataType.STRING)
  courseRank!: string;

  @Column(DataType.STRING)
  acceptanceRate!: string;

  @Column(DataType.STRING)
  lectureLanguage!: string;

  @Column(DataType.TEXT)
  additionalRequirements!: string;

  // ✅ Pivot relations to fetch CourseTestScore entries (courseId <-> testScoreId)
  @HasMany(() => CourseTestScore, { as: 'testScoreRelations' })
  testScoreRelations!: CourseTestScore[];

  @BelongsToMany(() => ProgramLevelTestScore, {
    through: () => CourseTestScore,
    as: 'testScores',
  })
  testScores!: ProgramLevelTestScore[];

  // ✅ Pivot relations to fetch CourseFeeCategory entries (courseId <-> feeCategoryId)
  @HasMany(() => CourseFeeCategory, { as: 'feeCategoryRelations' })
  feeCategoryRelations!: CourseFeeCategory[];

  @BelongsToMany(() => ProgramLevelIntakeFee, {
    through: () => CourseFeeCategory,
    as: 'feeCategories',
  })
  feeCategories!: ProgramLevelIntakeFee[];
}
