import { Modu<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { Course } from './course.model';
import { CourseTestScore } from './courseTestScore.model';
import { CourseService } from './course.service';
import { CourseGrpcController } from './course.grpc.controller';
import { AuditClientService } from '../audit.service';

import { University } from '../university/university.model';
import { ProgramLevel } from '../program-level/programLevel.model';
import { ProgramLevelTestScore } from '../program-level/applicationTestScore.model';
import { CourseFeeCategory } from './courseFee.model';
import { ProgramLevelIntakeFee } from '../fee/fee.model';
import { ProgramLevelIntake } from '../program-level/programLevelIntake.model';
import { Intake } from '../intake/intake.model';

@Module({
  imports: [
    SequelizeModule.forFeature([
      Course,
      CourseFeeCategory,
      CourseTestScore,
      University,
      Intake,
      ProgramLevel,
      ProgramLevelTestScore,
      ProgramLevelIntake,
      ProgramLevelIntakeFee,
    ]),
  ],
  controllers: [CourseGrpcController],
  providers: [CourseService, AuditClientService],
  exports: [CourseService],
})
export class CourseModule {}
