import { Table, Column, DataType, Foreign<PERSON>ey, BelongsTo } from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { Course } from './course.model';
import { ProgramLevelTestScore } from '../program-level/applicationTestScore.model';

@Table({ tableName: 'course_test_scores' })
export class CourseTestScore extends BaseModel {
  @ForeignKey(() => Course)
  @Column(DataType.BIGINT)
  courseId!: number;

  @BelongsTo(() => Course)
  course!: Course;

  @ForeignKey(() => ProgramLevelTestScore)
  @Column(DataType.BIGINT)
  testScoreId!: number;

  @BelongsTo(() => ProgramLevelTestScore)
  testScore!: ProgramLevelTestScore;
}
