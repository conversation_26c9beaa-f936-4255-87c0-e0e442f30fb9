import { ConflictEx<PERSON>, Controller, Logger } from '@nestjs/common';
import { GrpcMethod } from '@nestjs/microservices';
import { CountryService } from './country.service';
import { AppService } from '../app.service';
import { AuditClientService } from '../audit.service';

@Controller()
export class CountryGrpcController {
  private readonly logger = new Logger(CountryGrpcController.name);

  constructor(
    private readonly countryService: CountryService,
    private readonly appService: AppService,
    private readonly auditService: AuditClientService
  ) {}

  @GrpcMethod('UniversityService', 'CreateCountry')
  async createCountry(request: any) {
    const startTime = Date.now();

    try {
      const { universityId, countryNames, isActive, userId } = request;

      const result = await this.countryService.createMultiple({
        universityId,
        countryNames,
        isActive,
      });
      this.appService.trackProcessingDuration(
        'country_create_grpc_success',
        (Date.now() - startTime) / 1000
      );

      await this.auditService.logAction({
        action: 'CREATE_COUNTRY',
        entityType: 'Country',
        entityId: universityId.toString(),
        userId: userId ?? 'system',
        details: request,
      });

      // Handle different response scenarios
      if (result.conflictCount > 0) {
        // Partial success with conflicts - return conflict info in error
        return {
          status: 207, // Multi-Status for partial success
          message: `${result.insertedCountries.length} countries created successfully`,
          data: result.insertedCountries,
          error: {
            validationErrors: [],
            code: 'PARTIAL_CONFLICT',
            message: `${result.conflictCount} country(ies) already exist`,
            details: `Country(ies) "${result.conflictingCountries.join(
              ', '
            )}" already exist(s) for this university`,
          },
        };
      } else {
        // Full success (all countries inserted)
        return {
          status: 201, // Created
          message: 'Countries created successfully',
          data: result.insertedCountries,
          error: null,
        };
      }
    } catch (error) {
      this.logger.error(`CreateCountry error: ${error.message}`, error.stack);

      // Handle ConflictException (all countries already exist)
      if (error instanceof ConflictException) {
        return {
          status: 409,
          message: 'This  countries already exist',
          data: null,
          error: {
            validationErrors: [],
            code: 'CONFLICT',
            message: 'All provided countries already exist for this university',
            details: error.message,
          },
        };
      }

      // Handle other errors
      return {
        status: 500,
        message: 'Failed to create countries',
        data: null,
        error: {
          validationErrors: [],
          code: 'INTERNAL_ERROR',
          message: 'An unexpected error occurred',
          details: error.message,
        },
      };
    }
  }
  @GrpcMethod('UniversityService', 'UpdateCountry')
  async updateCountry(request: {
    universityId: number;
    countryNames: string[];
    userId?: string;
  }) {
    const startTime = Date.now();

    try {
      const { universityId, countryNames, userId } = request;

      // Call service to sync countries and get final list
      const updatedList = await this.countryService.bulkUpdate({
        universityId,
        countryNames,
      });

      // Track duration
      this.appService.trackProcessingDuration(
        'country_update_grpc_success',
        (Date.now() - startTime) / 1000
      );

      // Audit log
      await this.auditService.logAction({
        action: 'UPDATE_COUNTRY',
        entityType: 'Country',
        entityId: universityId.toString(),
        userId: userId ?? 'system',
        details: { updatedCount: updatedList.length },
      });

      return {
        status: 200,
        message: 'Country list updated successfully',
        data: updatedList.map((country) => ({
          id: country.id,
          universityId: country.universityId,
          countryName: country.countryName,
          isActive: country.isActive,
          createdAt: country.createdAt.toISOString(),
          updatedAt: country.updatedAt.toISOString(),
        })),
        error: null,
      };
    } catch (error) {
      this.logger.error(`UpdateCountry error: ${error.message}`, error.stack);

      return {
        status: 500,
        message: 'Failed to update countries',
        data: null,
        error: {
          validationErrors: [],
          code: 'INTERNAL_ERROR',
          message: 'An unexpected error occurred',
          details: error.message,
        },
      };
    }
  }
  @GrpcMethod('UniversityService', 'ListCountries')
  async listCountries(request: {
    universityId: number;
    page?: number;
    limit?: number;
  }) {
    const startTime = Date.now();
    try {
      const page = request.page || 1;
      const limit = request.limit || 10;

      const result = await this.countryService.findAll(
        request.universityId,
        page,
        limit
      );

      return {
        status: 200,
        message: 'Countries retrieved successfully',
        data: result.rows.map((c) => ({
          id: c.id,
          universityId: c.universityId,
          countryName: c.countryName,
          isActive: c.isActive,
          createdAt: c.createdAt.toISOString(),
          updatedAt: c.updatedAt.toISOString(),
        })),
        error: null,
      };
    } catch (error) {
      this.logger.error(`ListCountries failed: ${error.message}`, error.stack);
      return {
        status: 500,
        message: error.message,
        data: null,
        error: { details: error.message },
      };
    }
  }
  @GrpcMethod('UniversityService', 'GetCountry')
  async getCountry(request: { id: number }) {
    try {
      const result = await this.countryService.findOne(request.id);
      return {
        status: 200,
        message: 'Country retrieved',
        data: {
          id: result.id,
          universityId: result.universityId,
          countryName: result.countryName,
          isActive: result.isActive,
          createdAt: result.createdAt.toISOString(),
          updatedAt: result.updatedAt.toISOString(),
        },
        error: null,
      };
    } catch (error) {
      return {
        status: 404,
        message: 'Country not found',
        data: null,
        error: { details: error.message },
      };
    }
  }


  @GrpcMethod('UniversityService', 'DeleteCountry')
  async deleteCountry(request: {
    id: number;
    universityId: number;
    userId: string;
  }) {
    try {
      await this.countryService.remove(request.id);

      this.auditService.logAction({
        action: 'DELETE_COUNTRY',
        entityType: 'Country',
        entityId: request.id.toString(),
        userId: request.userId,
        details: request,
      });

      return {
        status: 200,
        message: 'Country deleted successfully',
        success: true,
        error: null,
      };
    } catch (error) {
      return {
        status: 404,
        message: 'Country not found',
        success: false,
        error: { details: error.message },
      };
    }
  }


}
