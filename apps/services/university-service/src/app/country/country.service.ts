import {
  Injectable,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Country } from './country.model';
import { Op } from 'sequelize';

@Injectable()
export class CountryService {
  constructor(
    @InjectModel(Country)
    private readonly model: typeof Country
  ) {}

  async create(data: {
    universityId: number;
    countryName: string;
    isActive: boolean;
  }): Promise<Country> {
    const existing = await this.model.findOne({
      where: {
        countryName: data.countryName,
        universityId: data.universityId,
      },
    });

    if (existing) {
      throw new ConflictException(
        `Country "${data.countryName}" already exists for this university`
      );
    }

    return this.model.create(data);
  }
  async createMultiple(data: {
    universityId: number;
    countryNames: string[];
    isActive?: boolean;
  }) {
    // Find existing countries for this university
    const existingCountries = await this.model.findAll({
      where: {
        universityId: data.universityId,
        countryName: data.countryNames,
      },
    });
    const existingNames = existingCountries.map((c) => c.countryName);

    // Find countries that don't exist yet
    const newCountryNames = data.countryNames.filter(
      (name) => !existingNames.includes(name)
    );

    let insertedCountries = [];

    // Insert only new countries if any
    if (newCountryNames.length > 0) {
      const toInsert = newCountryNames.map((name) => ({
        universityId: data.universityId,
        countryName: name,
        isActive: data.isActive ?? true,
      }));

      insertedCountries = await this.model.bulkCreate(toInsert);
    }

    // Handle different scenarios
   // Handle different scenarios
  if (existingNames.length > 0 && newCountryNames.length === 0) {
    // All countries already exist - throw exception
    throw new ConflictException(
      `All country(ies) "${existingNames.join(
        ', '
      )}" already exist(s) for this university`
    );
  } else if (existingNames.length > 0 && newCountryNames.length > 0) {
    // Partial conflict - some exist, some were inserted
    throw new ConflictException(
      `Some countries already exist: "${existingNames.join(
        ', '
      )}". Successfully inserted: "${newCountryNames.join(', ')}"`
    );
  }

    // Return response for successful insertion (full or partial)
    const response = {
      insertedCountries: insertedCountries,
      conflictCount: existingNames.length,
      conflictingCountries:
        existingNames.length > 0 ? existingNames : undefined,
    };
    return response;
  }

  async findOne(id: number): Promise<Country> {
    const record = await this.model.findByPk(id);
    if (!record) throw new NotFoundException(`Country with ID ${id} not found`);
    return record;
  }

async bulkUpdate(data: { 
  universityId: number; 
  countryNames: string[];
}): Promise<Country[]> {
  const { universityId, countryNames } = data;

  // Fetch current countries for the university
  const existingCountries = await this.model.findAll({ where: { universityId } });
  const existingNames = existingCountries.map(c => c.countryName);

  // Determine names to insert and remove
  const toInsert = countryNames.filter(name => !existingNames.includes(name));
  const toRemove = existingCountries.filter(c => !countryNames.includes(c.countryName));

  // Insert new countries
  if (toInsert.length > 0) {
    const insertPayload = toInsert.map(name => ({
      universityId,
      countryName: name,
      isActive: true,
    }));
    await this.model.bulkCreate(insertPayload);
  }

  // Remove old countries
  if (toRemove.length > 0) {
    await this.model.destroy({
      where: {
        id: { [Op.in]: toRemove.map(c => c.id) },
      },
    });
  }

  // Return the updated country list
  return this.model.findAll({
    where: { universityId },
    order: [['createdAt', 'ASC']],
  });
}



  async remove(id: number): Promise<void> {
    const deleted = await this.model.destroy({ where: { id } });
    if (!deleted)
      throw new NotFoundException(`Country with ID ${id} not found`);
  }

  async findAll(
    universityId: number,
    page: number = 1,
    limit: number = 10
  ): Promise<{ rows: Country[]; count: number }> {
    return this.model.findAndCountAll({
      where: { universityId },
      offset: (page - 1) * limit,
      limit,
      order: [['createdAt', 'DESC']],
    });
  }
}
