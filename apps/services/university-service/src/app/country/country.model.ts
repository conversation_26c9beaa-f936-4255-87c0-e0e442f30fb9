import { Table, Column, DataType, HasMany } from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { Campus } from '../campus/campus.model';

@Table({ tableName: 'countries' })
export class Country extends BaseModel {
  @Column(DataType.BIGINT)
  universityId!: number;

  @Column(DataType.STRING)
  countryName!: string;

  @Column({ type: DataType.BOOLEAN, defaultValue: true })
  isActive!: boolean;
  
  @HasMany(() => Campus)
  campuses: Campus[]; // ✅ One-to-many relationship
}
