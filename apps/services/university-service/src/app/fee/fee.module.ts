import { Modu<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { FeeService } from './fee.service';
import { FeeGrpcController } from './fee.grpc.controller';
import { AuditClientService } from '../audit.service';
import { Intake } from '../intake/intake.model';
import { ProgramLevel } from '../program-level/programLevel.model';
import { ProgramLevelIntakeFee } from './fee.model';
import { ProgramLevelIntake } from '../program-level/programLevelIntake.model'; // ✅ Import model

@Module({
  imports: [
    SequelizeModule.forFeature([
      ProgramLevelIntakeFee,
      ProgramLevelIntake, // ✅ Register this model
      Intake,
      ProgramLevel,
    ]),
  ],
  controllers: [FeeGrpcController],
  providers: [FeeService, AuditClientService],
  exports: [FeeService],
})
export class FeeModule {}
