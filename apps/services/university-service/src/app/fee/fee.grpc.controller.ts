import { <PERSON>, Logger } from '@nestjs/common';
import { GrpcMethod } from '@nestjs/microservices';
import { FeeService } from './fee.service';
import { AuditClientService } from '../audit.service';


@Controller()
export class FeeGrpcController {
  private readonly logger = new Logger(FeeGrpcController.name);

  constructor(
    private readonly service: FeeService,
    private readonly auditService: AuditClientService,
  ) {}

  @GrpcMethod('UniversityService', 'CreateProgramLevelIntakeFee')
  async create(data: any) {
    try {
      const created = await this.service.create(data);
      // for (const item of created) {
      //   await this.auditService.logAction({
      //     action: 'CREATE_FEE',
      //     entityType: 'ProgramLevelIntakeFee',
      //     entityId: item.id.toString(),
      //     userId: data.userId || '',
      //     details: item,
      //   });
      // }
      return {
        status: 200,
        message: 'Fees created successfully',
        data: created,
        error: null,
      };
    } catch (error) {
      this.logger.error('Create <PERSON>e failed', error.stack);
      return {
        status: 500,
        message: error.message,
        data: null,
        error: { details: error.message },
      };
    }
  }
  @GrpcMethod('UniversityService', 'ListProgramLevelIntakeFees')
  async list(data: any) {
    console.log("Query values",data)
    try {

      const result = await this.service.findAllByUniversity(data);
      return {
        status: 200,
        message: 'Fee list retrieved',
        data: result,
        error: null,
      };
    } catch (error) {
      this.logger.error('List Fees failed', error.stack);
      return {
        status: 500,
        message: error.message,
        data: null,
        error: { details: error.message },
      };
    }
  }
  // @GrpcMethod('UniversityService', 'UpdateProgramLevelIntakeFee')
  // async update(data: any) {
  //   try {
  //     const updated = await this.service.update(data);
  //     await this.auditService.logAction({
  //       action: 'UPDATE_FEE',
  //       entityType: 'ProgramLevelIntakeFee',
  //       entityId: updated.id.toString(),
  //       userId: data.userId || '',
  //       details: updated,
  //     });
  //     return {
  //       status: 200,
  //       message: 'Fee updated successfully',
  //       data: updated,
  //       error: null,
  //     };
  //   } catch (error) {
  //     this.logger.error('Update Fee failed', error.stack);
  //     return {
  //       status: 500,
  //       message: error.message,
  //       data: null,
  //       error: { details: error.message },
  //     };
  //   }
  // }

  // @GrpcMethod('UniversityService', 'GetProgramLevelIntakeFee')
  // async get(data: any) {
  //   try {
  //     const result = await this.service.findOne(data.id);
  //     return {
  //       status: 200,
  //       message: 'Fee retrieved',
  //       data: result,
  //       error: null,
  //     };
  //   } catch (error) {
  //     this.logger.error('Get Fee failed', error.stack);
  //     return {
  //       status: 500,
  //       message: error.message,
  //       data: null,
  //       error: { details: error.message },
  //     };
  //   }
  // }

  // @GrpcMethod('UniversityService', 'DeleteProgramLevelIntakeFee')
  // async delete(data: any & { userId?: string }) {
  //   try {
  //     const deleted = await this.service.remove(data.id);
  //     await this.auditService.logAction({
  //       action: 'DELETE_FEE',
  //       entityType: 'ProgramLevelIntakeFee',
  //       entityId: data.id.toString(),
  //       userId: data.userId || '',
  //       details: deleted,
  //     });
  //     return {
  //       status: 200,
  //       message: 'Fee deleted successfully',
  //       success: true,
  //       error: null,
  //     };
  //   } catch (error) {
  //     this.logger.error('Delete Fee failed', error.stack);
  //     return {
  //       status: 500,
  //       message: error.message,
  //       success: false,
  //       error: { details: error.message },
  //     };
  //   }
  // }


}
