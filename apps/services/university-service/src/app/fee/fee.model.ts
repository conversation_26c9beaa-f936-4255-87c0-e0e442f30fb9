// src/app/program-level/programLevelIntakeFee.model.ts

import {
  Table,
  Column,
  DataType,
  ForeignKey,
  Model,
  BelongsTo,
} from 'sequelize-typescript';
import { University } from '../university/university.model';
import { BaseModel } from '@apply-goal-backend/database';
import { ProgramLevelIntake } from '../program-level/programLevelIntake.model';

@Table({tableName: 'program_level_intake_fees'})
export class ProgramLevelIntakeFee extends BaseModel {
  @ForeignKey(() => University)
  @Column(DataType.BIGINT)
  universityId!: number;

  @BelongsTo(() => University)
  university!: University;

  @ForeignKey(() => ProgramLevelIntake)
  @Column({ type: DataType.BIGINT })
  programLevelIntakeId!: number;

  @BelongsTo(() => ProgramLevelIntake)
  programLevelIntake!: ProgramLevelIntake;

  @Column(DataType.STRING)
  feeTitle!: string;

  @Column(DataType.DOUBLE)
  tuitionFee!: number;

  @Column(DataType.DOUBLE)
  applicationFee!: number;

  @Column(DataType.DOUBLE)
  applicationFeeChargedByUniversity!: number;

  @Column(DataType.DOUBLE)
  applicationFeeChargedToStudent!: number;

  @Column(DataType.INTEGER)
  paymentDueInDays!: number;

  @Column(DataType.DATEONLY)
  feeEffectiveDate!: string;

  @Column(DataType.DATEONLY)
  applicationYearStart!: string;

  @Column(DataType.DATEONLY)
  applicationYearEnd!: string;

  @Column({ type: DataType.BOOLEAN, defaultValue: false })
  isActive!: boolean;

  @Column({ type: DataType.BOOLEAN, defaultValue: false })
  isRefundableToStudent!: boolean;

  @Column({ type: DataType.BOOLEAN, defaultValue: false })
  isVisibleToStudent!: boolean;
}
