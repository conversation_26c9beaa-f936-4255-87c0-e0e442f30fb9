import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { AppService } from '../app.service';
import { Intake } from '../intake/intake.model';
import { ProgramLevel } from '../program-level/programLevel.model';
import { ProgramLevelIntake } from '../program-level/programLevelIntake.model';
import { ProgramLevelIntakeFee } from './fee.model';
import { Sequelize } from 'sequelize-typescript';

@Injectable()
export class FeeService {
  constructor(
    @InjectModel(ProgramLevelIntakeFee)
    private readonly feeModel: typeof ProgramLevelIntakeFee,

    @InjectModel(ProgramLevelIntake)
    private readonly programIntakeModel: typeof ProgramLevelIntake,

    @InjectModel(Intake)
    private readonly intakeModel: typeof Intake,

    @InjectModel(ProgramLevel)
    private readonly programLevelModel: typeof ProgramLevel,

    private readonly appService: AppService,
    private readonly sequelize: Sequelize,
  ) {}

  async create(data: any) {
    const start = Date.now();
    const createdOrUpdated = [];

    const transaction = await this.sequelize.transaction();

    try {
      for (const intakeId of data.intakeIds) {
        let programIntake = await this.programIntakeModel.findOne({
          where: {
            universityId: data.universityId,
            programLevelId: data.programLevelId,
            intakeId: intakeId,
          },
          transaction,
        });

        if (!programIntake) {
          programIntake = await this.programIntakeModel.create(
            {
              universityId: data.universityId,
              programLevelId: data.programLevelId,
              intakeId: intakeId,
            },
            { transaction }
          );
        }

        const existingFee = await this.feeModel.findOne({
          where: {
            programLevelIntakeId: programIntake.id,
            feeTitle: data.feeTitle,
          },
          transaction,
        });

        const feePayload = {
          programLevelIntakeId: programIntake.id,
          universityId: data.universityId,
          feeTitle: data.feeTitle,
          applicationYearStart: data.applicationYearStart,
          applicationYearEnd: data.applicationYearEnd,
          tuitionFee: data.tuitionFee,
          applicationFee: data.applicationFee,
          applicationFeeChargedByUniversity: data.applicationFeeChargedByUniversity,
          applicationFeeChargedToStudent: data.applicationFeeChargedToStudent,
          paymentDueInDays: data.paymentDueInDays,
          feeEffectiveDate: data.feeEffectiveDate,
          isActive: data.isActive,
          isRefundableToStudent: data.isRefundableToStudent,
          isVisibleToStudent: data.isVisibleToStudent,
        };

        if (existingFee) {
          await existingFee.update(feePayload, { transaction });
          createdOrUpdated.push(existingFee);
        } else {
          const createdFee = await this.feeModel.create(feePayload, { transaction });
          createdOrUpdated.push(createdFee);
        }
      }

      await transaction.commit();

      this.appService.trackProcessingDuration(
        'fee_create_success',
        (Date.now() - start) / 1000
      );

      const result = await this.programLevelModel.findAll({
        where: {
          id: data.programLevelId,
          universityId: data.universityId,
        },
        attributes: ['id', 'programLevelName', 'durationNumber', 'durationType', 'createdAt'],
        include: [
          {
            model: this.programIntakeModel,
            include: [
              {
                model: this.intakeModel,
              },
              {
                model: this.feeModel,
                attributes: [
                  'id',
                  'feeTitle',
                  'tuitionFee',
                  'applicationFee',
                  'applicationFeeChargedByUniversity',
                  'applicationFeeChargedToStudent',
                  'paymentDueInDays',
                  'feeEffectiveDate',
                  'applicationYearStart',
                  'applicationYearEnd',
                  'isRefundableToStudent',
                  'isVisibleToStudent',
                  'isActive',
                ],
              },
            ],
          },
        ],
      });

      return result;
    } catch (error) {
      await transaction.rollback();
      this.appService.trackProcessingDuration(
        'fee_create_error',
        (Date.now() - start) / 1000
      );
      throw error;
    }
  }

  async findAllByUniversity(data: {
    universityId: number;
    page?: number;
    pageSize?: number;
  }) {
    const { universityId, page = 1, pageSize = 10 } = data;
    const offset = (page - 1) * pageSize;

    const result = await this.programLevelModel.findAll({
      where: { universityId },
      attributes: ['id', 'programLevelName', 'durationNumber', 'durationType', 'createdAt'],
      include: [
        {
          model: this.programIntakeModel,
          include: [
            {
              model: this.intakeModel,
              attributes: ['id', 'name', 'startDate', 'endDate'],
            },
            {
              model: this.feeModel,
              attributes: [
                'id',
                'feeTitle',
                'tuitionFee',
                'applicationFee',
                'applicationFeeChargedByUniversity',
                'applicationFeeChargedToStudent',
                'paymentDueInDays',
                'feeEffectiveDate',
                'applicationYearStart',
                'applicationYearEnd',
                'isRefundableToStudent',
                'isVisibleToStudent',
                'isActive',
              ],
            },
          ],
        },
      ],
      offset,
      limit: pageSize,
      order: [['createdAt', 'DESC']],
    });

    return result;
  }
}
