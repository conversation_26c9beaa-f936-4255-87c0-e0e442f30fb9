import { Table, Column, DataType, ForeignKey, BelongsTo } from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { University } from '../university/university.model';

@Table({ tableName: 'university_files' })
export class UniversityFile extends BaseModel {
  @ForeignKey(() => University)
  @Column(DataType.BIGINT)
  universityId!: number;

  @BelongsTo(() => University)
  university!: University;

  @Column(DataType.STRING)
  fileType!: 'agreement' | 'feature' | 'prospectus'| 'logo'; // use enum in TS

  @Column(DataType.STRING)
  fileUrl!: string;
}
