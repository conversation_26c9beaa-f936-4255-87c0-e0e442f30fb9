import { Modu<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { UniversityGeneralService } from './university-general.service';
import { UniversityGeneralGrpcController } from './university-general.grpc.controller';
import { AuditClientService } from '../audit.service';
import { UniversityGeneral } from './university-general.model';
import { UniversityFile } from './university-files.model';

@Module({
  imports: [SequelizeModule.forFeature([UniversityGeneral, UniversityFile])],
  controllers: [UniversityGeneralGrpcController],
  providers: [UniversityGeneralService, AuditClientService],
  exports: [UniversityGeneralService],
})
export class UniversityGeneralModule {}
