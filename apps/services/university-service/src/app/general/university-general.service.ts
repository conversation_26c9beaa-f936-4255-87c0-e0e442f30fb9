import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { UniversityGeneral } from './university-general.model';
import { AppService } from '../app.service';
import { UniversityFile } from './university-files.model';

@Injectable()
export class UniversityGeneralService {
  constructor(
    @InjectModel(UniversityGeneral)
    private readonly generalModel: typeof UniversityGeneral,
    @InjectModel(UniversityFile)
    private readonly fileModel: typeof UniversityFile,
    private readonly appService: AppService
  ) {}

  async create(data: any) {
    const { universityId, ...rest } = data;

    // Create or update general settings
    let general = await this.generalModel.findOne({ where: { universityId } });
    if (general) {
      await general.update(rest);
    } else {
      general = await this.generalModel.create({ universityId, ...rest });
    }

    // Clear previous files if any
    await this.fileModel.destroy({ where: { universityId } });

    // Prepare files for bulk insert
    const files = [
      ...(data.universityAgreement || []).map((url: string) => ({
        universityId,
        fileType: 'agreement',
        fileUrl: url,
      })),
      ...(data.universityFeatures || []).map((url: string) => ({
        universityId,
        fileType: 'feature',
        fileUrl: url,
      })),
      ...(data.universityProspectus || []).map((url: string) => ({
        universityId,
        fileType: 'prospectus',
        fileUrl: url,
      })),
    ];

    await this.fileModel.bulkCreate(files);

    this.appService.trackProcessingDuration('general_create_success', 0);
    return {
      ...general.toJSON(),
      universityAgreement: data.universityAgreement,
      universityFeatures: data.universityFeatures,
      universityProspectus: data.universityProspectus,
    };
  }

  async update(data: any) {
    return this.create(data); // reuse create for upsert behavior
  }

  async findOne(universityId: number) {
    const general = await this.generalModel.findOne({
      where: { universityId },
    });
    const files = await this.fileModel.findAll({ where: { universityId } });

    const grouped = {
      universityAgreement: files
        .filter((f) => f.fileType === 'agreement')
        .map((f) => f.fileUrl),
      universityFeatures: files
        .filter((f) => f.fileType === 'feature')
        .map((f) => f.fileUrl),
      universityProspectus: files
        .filter((f) => f.fileType === 'prospectus')
        .map((f) => f.fileUrl),
    };

    return { ...general?.toJSON(), ...grouped };
  }

  async remove(universityId: number) {
    await this.generalModel.destroy({ where: { universityId } });
    await this.fileModel.destroy({ where: { universityId } });

    this.appService.trackProcessingDuration('general_delete_success', 0);
    return { universityId };
  }

  async findAll({
    page = 1,
    pageSize = 10,
  }: {
    page: number;
    pageSize: number;
  }) {
    return this.generalModel.findAll({
      offset: (page - 1) * pageSize,
      limit: pageSize,
      order: [['createdAt', 'DESC']],
      include: [UniversityFile],
    });
  }
}
