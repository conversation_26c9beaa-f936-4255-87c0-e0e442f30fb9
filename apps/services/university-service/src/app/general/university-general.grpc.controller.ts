import { Controller, Logger } from '@nestjs/common';
import { GrpcMethod } from '@nestjs/microservices';
import { UniversityGeneralService } from './university-general.service';
import { AuditClientService } from '../audit.service';

@Controller()
export class UniversityGeneralGrpcController {
  private readonly logger = new Logger(UniversityGeneralGrpcController.name);

  constructor(
    private readonly service: UniversityGeneralService,
    private readonly auditService: AuditClientService,
  ) {}

  @GrpcMethod('UniversityService', 'CreateUniversityGeneral')
  async create(data: any) {
    try {
      const result = await this.service.create(data);
      await this.auditService.logAction({
        action: 'CREATE_UNIVERSITY_GENERAL',
        entityType: 'UniversityGeneral',
        entityId: data.universityId.toString(),
        userId: data.userId,
        details: result,
      });
      return { status: 200, message: 'General info created', data: result, error: null };
    } catch (error) {
      this.logger.error('Create failed', error.stack);
      return { status: 500, message: error.message, data: null, error: { details: error.message } };
    }
  }

  @GrpcMethod('UniversityService', 'UpdateUniversityGeneral')
  async update(data: any) {
    try {
      const result = await this.service.update(data);
      await this.auditService.logAction({
        action: 'UPDATE_UNIVERSITY_GENERAL',
        entityType: 'UniversityGeneral',
        entityId: data.universityId.toString(),
        userId: data.userId,
        details: result,
      });
      return { status: 200, message: 'General info updated', data: result, error: null };
    } catch (error) {
      this.logger.error('Update failed', error.stack);
      return { status: 500, message: error.message, data: null, error: { details: error.message } };
    }
  }

  @GrpcMethod('UniversityService', 'GetUniversityGeneral')
  async get(data: { universityId: number }) {
    try {
      const result = await this.service.findOne(data.universityId);
      return { status: 200, message: 'General info fetched', data: result, error: null };
    } catch (error) {
      this.logger.error('Get failed', error.stack);
      return { status: 500, message: error.message, data: null, error: { details: error.message } };
    }
  }

  @GrpcMethod('UniversityService', 'DeleteUniversityGeneral')
  async delete(data: { universityId: number; userId: string }) {
    try {
      const result = await this.service.remove(data.universityId);
      await this.auditService.logAction({
        action: 'DELETE_UNIVERSITY_GENERAL',
        entityType: 'UniversityGeneral',
        entityId: data.universityId.toString(),
        userId: data.userId,
        details: result,
      });
      return { status: 200, message: 'General info deleted', success: true, error: null };
    } catch (error) {
      this.logger.error('Delete failed', error.stack);
      return { status: 500, message: error.message, success: false, error: { details: error.message } };
    }
  }

  @GrpcMethod('UniversityService', 'ListUniversityGeneral')
  async list(data: { page: number; pageSize: number }) {
    try {
      const result = await this.service.findAll(data);
      return { status: 200, message: 'University general list', data: result, error: null };
    } catch (error) {
      this.logger.error('List failed', error.stack);
      return { status: 500, message: error.message, data: null, error: { details: error.message } };
    }
  }
}
