import { Table, Column, DataType, ForeignKey, BelongsTo, HasMany } from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { University } from '../university/university.model';

@Table({ tableName: 'university_general' })
export class UniversityGeneral extends BaseModel {
  @ForeignKey(() => University)
  @Column(DataType.BIGINT)
  universityId!: number;

  @BelongsTo(() => University)
  university!: University;

  @Column({ type: DataType.BOOLEAN, defaultValue: false })
  tuitionFeeDiscount!: boolean;

  @Column({ type: DataType.BOOLEAN, defaultValue: false })
  financialAidAcceptance!: boolean;

  @Column({ type: DataType.BOOLEAN, defaultValue: false })
  scholarshipOpportunity!: boolean;

  @Column({ type: DataType.BOOLEAN, defaultValue: false })
  accommodationStatus!: boolean;

  @Column({ type: DataType.BOOLEAN, defaultValue: false })
  employmentOpportunities!: boolean;

  @Column({ type: DataType.BOOLEAN, defaultValue: false })
  activeEnrollment!: boolean;
}
