import { Modu<PERSON>, MiddlewareConsumer, NestModule } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { getDatabaseConfig } from '@apply-goal-backend/database';
import { AppController } from './app.controller';
import { MetricsController } from './metrics.controller';
import { MetricsMiddleware } from './middleware/metrics.middleware';
import { CountryModule } from './country/country.module';
import { UniversityModule } from './university/university.module';
import { CampusModule } from './campus/campus.module';
import { CoreModule } from './core.module';
import { IntakeModule } from './intake/intake.module';
import { ProgramLevelModule } from './program-level/programLevel.module';
import { FieldOfStudyModule } from './field-of-study/field-of-study.module';
import { CommissionModule } from './commission/commission.module';
import { PaymentDetailsModule } from './payment/payment.module';
import { UniversityContactModule } from './university-contact/university-contact.module';
import { UniversityGeneralModule } from './general/university-general.module';
import { FeeModule } from './fee/fee.module';
import { CourseModule } from './course/course.module';
import { AlumniModule } from './alumni/alumni.module';

@Module({
  imports: [
    CoreModule,
    SequelizeModule.forRoot({
      ...getDatabaseConfig('university'),
      autoLoadModels: true,
      synchronize: true,
    }),
    CountryModule,
    UniversityModule,
    CampusModule,
    IntakeModule,
    ProgramLevelModule,
    CommissionModule,
    FieldOfStudyModule,
    PaymentDetailsModule,
    UniversityContactModule,
    UniversityGeneralModule,
    FeeModule,
    CourseModule,
    CampusModule,
    AlumniModule
    
  ],
  controllers: [AppController, MetricsController],

})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(MetricsMiddleware).exclude('/metrics').forRoutes('*');
  }
}
