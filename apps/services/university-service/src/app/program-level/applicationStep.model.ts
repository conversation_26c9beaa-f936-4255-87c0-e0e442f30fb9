import {
  Table,
  Column,
  DataType,
  Foreign<PERSON>ey,
  BelongsTo,
} from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { ProgramLevel } from './programLevel.model';

@Table({ tableName: 'program_level_application_steps' })
export class ProgramLevelApplicationStep extends BaseModel {
  @ForeignKey(() => ProgramLevel)
  @Column(DataType.BIGINT)
  programLevelId!: number;

  @BelongsTo(() => ProgramLevel)
  programLevel!: ProgramLevel;

  @Column(DataType.STRING)
  title!: string;

  @Column(DataType.BOOLEAN)
  required!: boolean;

  @Column(DataType.INTEGER)
  weight!: number;
}
