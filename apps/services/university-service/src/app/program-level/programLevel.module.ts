import { Modu<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import {ProgramLevelGrpcController } from './programLevel.grpc.controller';
import { AuditClientService } from '../audit.service';
import { ProgramLevel } from './programLevel.model';
import { ProgramLevelApplicationStep } from './applicationStep.model';
import { ProgramLevelTestScore } from './applicationTestScore.model';
import { ProgramLevelService } from './programLevel.service';
import { Intake } from '../intake/intake.model';
import { ProgramLevelIntake } from './programLevelIntake.model';

@Module({
  imports: [
    SequelizeModule.forFeature([
      ProgramLevel,
      ProgramLevelApplicationStep,
      ProgramLevelTestScore,
      ProgramLevelIntake,
      Intake
    ]),
  ],
  controllers: [ProgramLevelGrpcController],
  providers: [ProgramLevelService, AuditClientService],
  exports: [ProgramLevelService],
})
export class ProgramLevelModule {}
