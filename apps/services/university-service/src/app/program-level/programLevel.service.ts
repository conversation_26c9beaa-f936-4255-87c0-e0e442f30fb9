import {
  Injectable,
  ConflictException,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Sequelize } from 'sequelize-typescript';
import { InjectConnection } from '@nestjs/sequelize';
import { AppService } from '../app.service';
import { ProgramLevel } from './programLevel.model';
import { ProgramLevelApplicationStep } from './applicationStep.model';
import { ProgramLevelTestScore } from './applicationTestScore.model';
import { Intake } from '../intake/intake.model';
import { ProgramLevelIntakeFee } from '../fee/fee.model';
import { ProgramLevelIntake } from './programLevelIntake.model';

@Injectable()
export class ProgramLevelService {
  constructor(
    @InjectModel(ProgramLevel)
    private readonly programLevelModel: typeof ProgramLevel,

    @InjectModel(ProgramLevelApplicationStep)
    private readonly applicationStepModel: typeof ProgramLevelApplicationStep,

    @InjectModel(ProgramLevelTestScore)
    private readonly testScoreModel: typeof ProgramLevelTestScore,

    @InjectModel(ProgramLevelIntake)
    private readonly programLevelIntakeModel: typeof ProgramLevelIntake,

    @InjectModel(Intake)
    private readonly intakeModel: typeof Intake,

    @InjectConnection()
    private readonly sequelize: Sequelize,
    private readonly appService: AppService
  ) {}

  async create(payload: {
    universityId: number;
    userId?: string;
    programLevelName: string;
    durationNumber: number;
    durationType: string;
    intake: number[];
    testScore: {
      test: string;
      testScore: string;
    }[];
    applicationStep: {
      title: string;
      required: boolean;
      weight: number;
    }[];
  }) {
    const start = Date.now();
    const transaction = await this.sequelize.transaction();

    try {
      const exists = await this.programLevelModel.findOne({
        where: {
          universityId: payload.universityId,
          programLevelName: payload.programLevelName,
        },
        transaction,
      });

      if (exists) {
        throw new ConflictException(
          'Program level with this name already exists'
        );
      }

      const programLevel = await this.programLevelModel.create(
        {
          universityId: payload.universityId,
          programLevelName: payload.programLevelName,
          durationNumber: payload.durationNumber,
          durationType: payload.durationType,
        },
        { transaction }
      );

      await this.applicationStepModel.bulkCreate(
        payload.applicationStep.map((step) => ({
          programLevelId: programLevel.id,
          ...step,
        })),
        { transaction }
      );

      await this.testScoreModel.bulkCreate(
        payload.testScore.map((item) => ({
          programLevelId: programLevel.id,
          test: item.test,
          testScore: item.testScore,
        })),
        { transaction }
      );

      // Set intake associations
      // Manually insert into ProgramLevelIntakeFee with universityId
      if (payload.intake?.length) {
        await this.programLevelIntakeModel.bulkCreate(
          payload.intake.map((intakeId) => ({
            universityId:payload.universityId,
            programLevelId: programLevel.id,
            intakeId,
            isActive: true,
          })),
          { transaction }
        );
      }

      await transaction.commit();
      this.appService.trackProcessingDuration(
        'program_level_create_success',
        (Date.now() - start) / 1000
      );

      // Reload with all relations
      const fullProgramLevel = await this.programLevelModel.findByPk(
        programLevel.id,
        {
          include: [
            this.applicationStepModel,
            this.testScoreModel,
            {
              model: this.intakeModel,
              through: { attributes: [] },
            },
          ],
        }
      );
      return fullProgramLevel;
    } catch (error) {
      await transaction.rollback();
      this.appService.trackProcessingDuration(
        'program_level_create_error',
        (Date.now() - start) / 1000
      );
      throw error;
    }
  }

  async findOne(id: number): Promise<ProgramLevel> {
    const record = await this.programLevelModel.findByPk(id, {
      include: [ProgramLevelApplicationStep, ProgramLevelTestScore, Intake],
    });

    if (!record) throw new NotFoundException(`Program level ${id} not found`);

    return record;
  }

  async findAllByUniversity(universityId: number) {
    return this.programLevelModel.findAll({
      where: { universityId },
      include: [
        ProgramLevelApplicationStep,
        ProgramLevelTestScore,
        {
          model: Intake,
          through: { attributes: [] }, // This excludes junction table data
          attributes: ['id', 'name', 'startDate', 'endDate'],
          include: [
            {
              association: 'studentTypes',
              attributes: [
                'studentType',
                'applicationOpen',
                'applicationDeadline',
                'enrollmentDeadline',
              ],
            },
          ],
        },
      ],
      order: [['createdAt', 'DESC']],
    });
  }

  async remove(id: number): Promise<void> {
    const transaction = await this.sequelize.transaction();

    try {
      const programLevel = await this.programLevelModel.findByPk(id, {
        transaction,
      });
      if (!programLevel) {
        throw new NotFoundException(`Program level ${id} not found`);
      }

      // Delete related records first
      await this.applicationStepModel.destroy({
        where: { programLevelId: id },
        transaction,
      });
      await this.testScoreModel.destroy({
        where: { programLevelId: id },
        transaction,
      });

      // Delete the program level
      await this.programLevelModel.destroy({ where: { id }, transaction });

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
}
