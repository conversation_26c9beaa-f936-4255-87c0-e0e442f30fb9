// src/app/program-level/programLevelIntake.model.ts

import {
  Table,
  Column,
  DataType,
  ForeignKey,
  Model,
  BelongsTo,
  HasMany,
} from 'sequelize-typescript';
import { ProgramLevel } from './programLevel.model';
import { Intake } from '../intake/intake.model';
import { University } from '../university/university.model';
import { BaseModel } from '@apply-goal-backend/database';
import { ProgramLevelIntakeFee } from '../fee/fee.model';

@Table({
  tableName: 'program_level_intakes',
})
export class ProgramLevelIntake extends BaseModel {
  @ForeignKey(() => University)
  @Column(DataType.BIGINT)
  universityId!: number;

  @BelongsTo(() => University)
  university!: University;

  @ForeignKey(() => ProgramLevel)
  @Column(DataType.BIGINT)
  programLevelId!: number;

  @BelongsTo(() => ProgramLevel)
  programLevel!: ProgramLevel;

  @ForeignKey(() => Intake)
  @Column(DataType.BIGINT)
  intakeId!: number;

  @BelongsTo(() => Intake)
  intake!: Intake;

  @Column({ type: DataType.BOOLEAN, defaultValue: true })
  isActive!: boolean;

  @HasMany(() => ProgramLevelIntakeFee)
  fees!: ProgramLevelIntakeFee[];
}
