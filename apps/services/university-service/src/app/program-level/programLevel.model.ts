import {
  Table,
  Column,
  DataType,
  Has<PERSON>any,
  BelongsToMany,
} from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { ProgramLevelApplicationStep } from './applicationStep.model';
import { ProgramLevelTestScore } from './applicationTestScore.model';
import { Intake } from '../intake/intake.model';
import { ProgramLevelIntake } from './programLevelIntake.model';
import { Course } from '../course/course.model';

@Table({ tableName: 'program_levels' })
export class ProgramLevel extends BaseModel {
  @Column(DataType.BIGINT)
  universityId!: number;

  @Column(DataType.STRING)
  programLevelName!: string;

  @Column(DataType.INTEGER)
  durationNumber!: number;

  @Column(DataType.STRING)
  durationType!: string;

  @HasMany(() => ProgramLevelApplicationStep)
  applicationSteps!: ProgramLevelApplicationStep[];

  @HasMany(() => ProgramLevelTestScore)
  testScores!: ProgramLevelTestScore[];

  @BelongsToMany(() => Intake, {
    through: () => ProgramLevelIntake,
    foreignKey: 'programLevelId',
    otherKey: 'intakeId',
  })
  intakes!: Intake[];

  // ✅ Needed for eager loading
  @HasMany(() => ProgramLevelIntake)
  programLevelIntakes!: ProgramLevelIntake[];

  @HasMany(() => Course, { as: 'courses' })
  courses!: Course[];
}
