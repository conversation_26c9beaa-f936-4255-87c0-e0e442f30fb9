import {
  Table,
  Column,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { ProgramLevel } from './programLevel.model';

@Table({ tableName: 'program_level_test_scores' })
export class ProgramLevelTestScore extends BaseModel {
  @ForeignKey(() => ProgramLevel)
  @Column(DataType.BIGINT)
  programLevelId!: number;

  @BelongsTo(() => ProgramLevel)
  programLevel!: ProgramLevel;

  @Column(DataType.STRING)
  test!: string; // e.g., "IELTS", "SAT"

  @Column(DataType.STRING)
  testScore!: string;
}
