import { Controller, Logger } from '@nestjs/common';
import { GrpcMethod } from '@nestjs/microservices';
import { AppService } from '../app.service';
import { AuditClientService } from '../audit.service';
import { ProgramLevelService } from './programLevel.service';

@Controller()
export class ProgramLevelGrpcController {
  private readonly logger = new Logger(ProgramLevelGrpcController.name);

  constructor(
    private readonly programLevelService: ProgramLevelService,
    private readonly appService: AppService,
    private readonly auditService: AuditClientService
  ) {}

  @GrpcMethod('UniversityService', 'CreateProgramLevel')
  async createProgramLevel(request: any) {
    const start = Date.now();
    console.log('ApplicationLevel RequestData', request);
    try {
      const result = await this.programLevelService.create(request);

      await this.auditService.logAction({
        action: 'CREATE_PROGRAM_LEVEL',
        entityType: 'ProgramLevel',
        entityId: result.id.toString(),
        userId: request.userId || 'system',
        details: request,
      });
      this.appService.trackProcessingDuration(
        'program_level_create_grpc_success',
        (Date.now() - start) / 1000
      );
      const transformed = {
        id: Number(result.id),
        universityId: Number(result.universityId),
        programLevelName: result.programLevelName,
        durationNumber: result.durationNumber,
        durationType: result.durationType,
        intake:
          result.intakes?.map((i) => ({
            id: Number(i.id),
            name: i.name ?? '',
            startDate: i.startDate?.toISOString() ?? '',
            endDate: i.endDate?.toISOString() ?? '',
          })) || [],
        testScore:
          result.testScores?.map((score) => ({
            test: score.test,
            testScore: score.testScore,
          })) ?? [],
        applicationStep:
          result.applicationSteps?.map((step) => ({
            title: step.title,
            required: step.required,
            weight: step.weight,
          })) ?? [],
        createdAt: result.createdAt,
        updatedAt: result.updatedAt,
      };

      console.log(
        'Response Transform +++++++++++++++++++++++++++++++++',
        transformed
      );
      return {
        status: 200,
        message: 'Program level created successfully',
        data: transformed,
        error: null,
      };
    } catch (error) {
      this.logger.error(
        `CreateProgramLevel failed: ${error.message}`,
        error.stack
      );
      return {
        status: error?.response?.statusCode || 500,
        message: error.message,
        data: null,
        error: { details: error.message },
      };
    }
  }

  @GrpcMethod('UniversityService', 'GetProgramLevel')
  async getProgramLevel(request: { id: number }) {
    try {
      const data = await this.programLevelService.findOne(request.id);
      return {
        status: 200,
        message: 'Program level fetched successfully',
        data,
        error: null,
      };
    } catch (error) {
      this.logger.error(
        `GetProgramLevel failed: ${error.message}`,
        error.stack
      );
      return {
        status: error?.response?.statusCode || 500,
        message: error.message,
        data: null,
        error: { details: error.message },
      };
    }
  }

  @GrpcMethod('UniversityService', 'listProgramLevelByUniversity')
  async listProgramLevelsByUniversity(request: { universityId: number }) {
    try {
      const result = await this.programLevelService.findAllByUniversity(
        request.universityId
      );

      const data = result.map((item) => ({
        id: Number(item.id),
        universityId: Number(item.universityId),
        programLevelName: item.programLevelName ?? '',
        durationNumber: item.durationNumber ?? 0,
        durationType: item.durationType ?? '',
      intake: item.intakes?.map((i) => ({
        id: Number(i.id),
        name: i.name ?? '',
        startDate: i.startDate?.toISOString() ?? '',
        endDate: i.endDate?.toISOString() ?? '',
        studentTypes: i.studentTypes?.map((s) => ({
          studentType: s.studentType ?? '',
          applicationOpen: s.applicationOpen?.toISOString() ?? '',
          applicationDeadline: s.applicationDeadline?.toISOString() ?? '',
          enrollmentDeadline: s.enrollmentDeadline?.toISOString() ?? ''
        })) || [],
      })) || [],
        testScore:
          item.testScores?.map((t) => ({
            test: t.test ?? '',
            testScore: t.testScore ?? '',
          })) || [],
        applicationStep:
          item.applicationSteps?.map((s) => ({
            title: s.title ?? '',
            required: s.required ?? false,
            weight: s.weight ?? 0,
          })) || [],
        createdAt: item.createdAt?.toISOString() ?? '',
        updatedAt: item.updatedAt?.toISOString() ?? '',
      }));
      return {
        status: 200,
        message: 'Program levels retrieved successfully',
        data,
        error: null,
      };
    } catch (error) {
      this.logger.error(
        `ListProgramLevels failed: ${error.message}`,
        error.stack
      );
      return {
        status: error?.response?.statusCode || 500,
        message: error.message,
        data: null,
        error: { details: error.message },
      };
    }
  }

  @GrpcMethod('UniversityService', 'DeleteProgramLevel')
  async deleteProgramLevel(request: { id: number }) {
    try {
      await this.programLevelService.remove(request.id);
      return {
        status: 200,
        message: 'Program level deleted successfully',
        data: { success: true },
        error: null,
      };
    } catch (error) {
      this.logger.error(
        `DeleteProgramLevel failed: ${error.message}`,
        error.stack
      );
      return {
        status: error?.response?.statusCode || 500,
        message: error.message,
        data: null,
        error: { details: error.message },
      };
    }
  }
}
