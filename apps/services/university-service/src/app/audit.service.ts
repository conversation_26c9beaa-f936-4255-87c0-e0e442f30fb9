import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { Client, ClientGrpc } from '@nestjs/microservices';
import { Transport } from '@nestjs/microservices';
import { join } from 'path';
import { Observable, throwError } from 'rxjs';
import { catchError, timeout } from 'rxjs/operators';
import { AppService } from './app.service';

// Define the interface for the audit service
interface AuditService {
  createAuditLog(data: {
    userId: string;
    userRole: string;
    actions: string;
    serviceName: string;
    resourceType: string;
    resourceId: string;
    description?: string;
    metadata?: { [key: string]: string };
    ipAddress?: string;
    userAgent?: string;
    source?: string;
  }): Observable<{ success: boolean; id: string }>;
}

@Injectable()
export class AuditClientService implements OnModuleInit {
  private readonly logger = new Logger(AuditClientService.name);

  @Client({
    transport: Transport.GRPC,
    options: {
      package: 'audit',
      protoPath: join(
        process.cwd(),
        'libs/shared/dto/src/lib/audit/audit.proto'
      ),
      // url: 'localhost:50051',
      url:'audit-logging:50051',
      loader: {
        keepCase: true,
        longs: String,
        enums: String,
        defaults: true,
        oneofs: true,
      },
    },
  })
  private readonly client: ClientGrpc;

  private auditService: AuditService;

  constructor(private readonly appService: AppService) {}

  onModuleInit() {
    this.auditService = this.client.getService<AuditService>('AuditService');
  }

  private handleError(operation = 'operation') {
    return (error: any) => {
      this.logger.error(`${operation} failed: ${error.message}`, error.stack);
      return throwError(() => error);
    };
  }

  logAction(data: {
    action: string;
    entityType: string;
    entityId: string;
    userId: number | string; // Accept both number and string
    userRole?:string;
    details?: any;
    ipAddress?: string;
    userAgent?: string;
  }) {
    const startTime = Date.now();

    try {
      // Create a clean audit log object with only the fields defined in the proto
      const auditLog = {
        // Ensure userId is a number but never 0
        userId: typeof data.userId === 'string' ? parseInt(data.userId, 10) || 1 : (data.userId || 1),
        userRole: data.userRole,
        actions: data.action,
        serviceName: 'university-service',
        resourceType: data.entityType,
        resourceId: parseInt(data.entityId, 10) || 0, // Ensure resourceId is a number
        description: data.details ? JSON.stringify(data.details).substring(0, 500) : '',
        metadata: {
          source: 'university-service'
        },
        ipAddress: data.ipAddress || '',
        userAgent: data.userAgent || '',
        source: 'api'
      };

      this.logger.debug(`Sending audit log: ${JSON.stringify(auditLog)}`);

      this.auditService
        .createAuditLog({
          userId: String(auditLog.userId || 1), // Ensure userId is never empty
          userRole: auditLog.userRole,
          actions: auditLog.actions,
          serviceName: auditLog.serviceName,
          resourceType: auditLog.resourceType,
          resourceId: String(auditLog.resourceId),
          description: auditLog.description,
          metadata: auditLog.metadata,
          ipAddress: auditLog.ipAddress,
          userAgent: auditLog.userAgent,
          source: auditLog.source
        })
        .pipe(
          timeout(5000),
          catchError(this.handleError('createAuditLog'))
        )
        .subscribe({
          next: (response) => {
            this.appService.trackProcessingDuration(
              'audit_log_success',
              (Date.now() - startTime) / 1000
            );
            this.logger.debug(`Audit log created successfully: ${response.id}`);
          },
          error: (error) => {
            this.appService.trackProcessingDuration(
              'audit_log_error',
              (Date.now() - startTime) / 1000
            );
            this.logger.error(
              `Failed to create audit log: ${error.message}`,
              error.stack
            );
          },
        });
    } catch (error) {
      this.appService.trackProcessingDuration(
        'audit_log_error',
        (Date.now() - startTime) / 1000
      );
      this.logger.error(
        `Error preparing audit log: ${error.message}`,
        error.stack
      );
    }
  }
}
