import { Modu<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { Alumni } from './alumni.model';
import { AlumniService } from './alumni.service';
import { AlumniGrpcController } from './alumni.grpc.controller';
import { University } from '../university/university.model';

@Module({
  imports: [SequelizeModule.forFeature([Alumni, University])],
  controllers: [AlumniGrpcController],
  providers: [AlumniService],
  exports: [AlumniService],
})
export class AlumniModule {}
