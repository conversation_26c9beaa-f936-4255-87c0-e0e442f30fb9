import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { University } from '../university/university.model';

@Table({ tableName: 'alumni', timestamps: true })
export class Alumni extends Model {
  @Column({ type: DataType.INTEGER, autoIncrement: true, primaryKey: true })
  id: number;

  @ForeignKey(() => University)
  @Column({ type: DataType.INTEGER, allowNull: false })
  universityId: number;

  @Column({ type: DataType.STRING, allowNull: false })
  name: string;

  @Column({ type: DataType.STRING })
  organizationName: string;

  @Column({ type: DataType.STRING })
  designation: string;

  @Column({ type: DataType.STRING })
  imageUrl: string;

  @BelongsTo(() => University)
  university: University;
}
