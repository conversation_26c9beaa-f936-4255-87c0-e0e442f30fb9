import {
  Injectable,
  Logger,
  NotFoundException,
  InternalServerErrorException,
  BadRequestException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Sequelize } from 'sequelize-typescript';
import { Alumni } from './alumni.model';

@Injectable()
export class AlumniService {
  private readonly logger = new Logger(AlumniService.name);

  constructor(
    @InjectModel(Alumni)
    private alumniRepository: typeof Alumni,
    private readonly sequelize: Sequelize
  ) {}

  async create(data: { data: any[] }): Promise<Alumni[]> {
    const transaction = await this.sequelize.transaction();
    try {
      const alumniRecords = [];

      for (const item of data.data) {
        if (!item.universityId) {
          throw new BadRequestException(
            'universityId is required for each record'
          );
        }
        if (!item.name) {
          throw new BadRequestException('name is required for each record');
        }

        const alumni = await this.alumniRepository.create(item, {
          transaction,
        });
        alumniRecords.push(alumni);
      }

      await transaction.commit();
      return alumniRecords;
    } catch (error) {
      await transaction.rollback();
      this.logger.error('Bulk create alumni failed', error.stack);
      throw new InternalServerErrorException({
        message: 'Bulk create failed',
        details: error.message,
      });
    }
  }

  async update(id: number, data: any): Promise<Alumni> {
    try {
      const alumni = await this.findOne(id);
      await alumni.update(data);
      return alumni;
    } catch (error) {
      this.logger.error('Update alumni failed', error.stack);
      throw new InternalServerErrorException('Update failed');
    }
  }

  async findOne(id: number): Promise<Alumni> {
    const alumni = await this.alumniRepository.findByPk(id);
    if (!alumni) throw new NotFoundException('Alumni not found');
    return alumni;
  }

  async remove(id: number): Promise<void> {
    const alumni = await this.findOne(id);
    await alumni.destroy();
  }

  async findAll(
    universityId: number,
    page = 1,
    pageSize = 10
  ): Promise<Alumni[]> {
    // if (page <= 0) page = 1;
    // if (pageSize <= 0) pageSize = 10;
    return this.alumniRepository.findAll({
      where: { universityId },
      offset: (page - 1) * pageSize,
      limit: pageSize,
      order: [['createdAt', 'DESC']],
    });
  }
}
