import { Controller, Logger } from '@nestjs/common';
import { GrpcMethod } from '@nestjs/microservices';
import { AlumniService } from './alumni.service';

@Controller()
export class AlumniGrpcController {
  private readonly logger = new Logger(AlumniGrpcController.name);

  constructor(private readonly alumniService: AlumniService) {}

  @GrpcMethod('UniversityService', 'CreateAlumni')
  async createAlumni(data: any) {
    try {
      const alumni = await this.alumniService.create(data);
      return { status: 200, message: 'Alumni created successfully', data: alumni };
    } catch (error) {
      return {
        status: 500,
        message: error.message,
        error: { details: error.message },
      };
    }
  }
  @GrpcMethod('UniversityService', 'GetAlumniList')
  async listAlumni(data: {
    universityId: number;
    page?: number;
    pageSize?: number;
  }) {
    try {
      const result = await this.alumniService.findAll(
        data.universityId,
        data.page>0?data.page:1,
        data.pageSize>0?data.pageSize:10
      );
      return {
        status: 200,
        message: 'Alumni list fetched',
        data: result,
        count: result.length,
      };
    } catch (error) {
      return {
        status: 500,
        message: error.message,
        error: { details: error.message },
      };
    }
  }

  // @GrpcMethod('UniversityService', 'UpdateAlumni')
  // async updateAlumni(data: any) {
  //   try {
  //     const alumni = await this.alumniService.update(data.id, data);
  //     return { status: 200, message: 'Alumni updated', data: alumni };
  //   } catch (error) {
  //     return {
  //       status: 500,
  //       message: error.message,
  //       error: { details: error.message },
  //     };
  //   }
  // }

  // @GrpcMethod('UniversityService', 'GetAlumni')
  // async getAlumni(data: { id: number }) {
  //   try {
  //     const alumni = await this.alumniService.findOne(data.id);
  //     return { status: 200, message: 'Alumni fetched', data: alumni };
  //   } catch (error) {
  //     return {
  //       status: 404,
  //       message: error.message,
  //       error: { details: error.message },
  //     };
  //   }
  // }

  // @GrpcMethod('UniversityService', 'DeleteAlumni')
  // async deleteAlumni(data: { id: number }) {
  //   try {
  //     await this.alumniService.remove(data.id);
  //     return { status: 200, message: 'Alumni deleted', success: true };
  //   } catch (error) {
  //     return {
  //       status: 500,
  //       message: error.message,
  //       error: { details: error.message },
  //       success: false,
  //     };
  //   }
  // }
}
