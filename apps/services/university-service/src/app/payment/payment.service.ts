import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { BankDetails } from './payment.model';
import { AppService } from '../app.service';

@Injectable()
export class BankDetailsService {
  constructor(
    @InjectModel(BankDetails) private readonly model: typeof BankDetails,
    private readonly appService: AppService
  ) {}

  async create(data: any) {
    const start = Date.now();
    try {
      const record = await this.model.create(data);
      this.appService.trackProcessingDuration(
        'bank_create_success',
        (Date.now() - start) / 1000
      );
      return record;
    } catch (error) {
      this.appService.trackProcessingDuration(
        'bank_create_error',
        (Date.now() - start) / 1000
      );
      throw error;
    }
  }

  async update(data: any) {
    const start = Date.now();
    const record = await this.model.findByPk(data.id);
    if (!record) throw new NotFoundException('Bank Details not found');

    await record.update(data);
    this.appService.trackProcessingDuration(
      'bank_update_success',
      (Date.now() - start) / 1000
    );
    return record;
  }

  async findOne(id: string) {
    return this.model.findByPk(id);
  }

  async remove(id: string) {
    const start = Date.now();
    const record = await this.model.findByPk(id);
    if (!record) throw new NotFoundException('Bank Details not found');

    await this.model.destroy({ where: { id } });
    this.appService.trackProcessingDuration(
      'bank_delete_success',
      (Date.now() - start) / 1000
    );
    return record;
  }

  async findAll({
    page = 1,
    pageSize = 10,
    universityId,
  }: {
    page: number;
    pageSize: number;
    universityId?: number;
  }) {
    const whereClause = universityId ? { universityId } : {};
    try {
      const result = await this.model.findAll({
        where: whereClause,
        offset: (page - 1) * pageSize,
        limit: pageSize,
        order: [['createdAt', 'DESC']],
      });

      return result;
    } catch (error) {
      console.error('Query failed:', error);
      throw error;
    }
  }
}
