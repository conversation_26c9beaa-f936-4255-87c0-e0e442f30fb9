import { Table, Column, DataType, ForeignKey, BelongsTo } from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { University } from '../university/university.model'; // Adjust the path if needed

@Table({ tableName: 'university_bank_details' })
export class BankDetails extends BaseModel {
  @ForeignKey(() => University)
  @Column(DataType.BIGINT)
  universityId!: number;

  @BelongsTo(() => University)
  university!: University;

  @Column(DataType.STRING)
  bankName!: string;

  @Column(DataType.STRING)
  accountName!: string;

  @Column(DataType.STRING)
  accountHolderName!: string;

  @Column(DataType.STRING)
  ibanSwiftCode!: string;

  @Column(DataType.STRING)
  taxId!: string;

  @Column(DataType.STRING)
  vatNumber!: string;
}
