import { <PERSON>, Logger } from '@nestjs/common';
import { GrpcMethod } from '@nestjs/microservices';
import { BankDetailsService } from './payment.service';
import { AuditClientService } from '../audit.service';

@Controller()
export class BankDetailsGrpcController {
  private readonly logger = new Logger(BankDetailsGrpcController.name);

  constructor(
    private readonly service: BankDetailsService,
    private readonly auditService: AuditClientService,
  ) {}

  @GrpcMethod('UniversityService', 'CreateBankDetails')
  async create(data: any) {
    try {
      const result = await this.service.create(data);
      await this.auditService.logAction({
        action: 'CREATE_BANK_DETAILS',
        entityType: 'BankDetails',
        entityId: result.id.toString(),
        userId: data.userId,
        details: result,
      });
      return { status: 200, message: 'Bank details created', data: result, error: null };
    } catch (error) {
      this.logger.error(`<PERSON><PERSON> failed: ${error.message}`, error.stack);
      return { status: 500, message: error.message, data: null, error: { details: error.message } };
    }
  }

  @GrpcMethod('UniversityService', 'UpdateBankDetails')
  async update(data: any) {
    try {
      const result = await this.service.update(data);
      await this.auditService.logAction({
        action: 'UPDATE_BANK_DETAILS',
        entityType: 'BankDetails',
        entityId: result.id.toString(),
        userId: data.userId,
        details: result,
      });
      return { status: 200, message: 'Bank details updated', data: result, error: null };
    } catch (error) {
      this.logger.error(`Update failed: ${error.message}`, error.stack);
      return { status: 500, message: error.message, data: null, error: { details: error.message } };
    }
  }

  @GrpcMethod('UniversityService', 'GetBankDetails')
  async get(data: { bankDetailsId: string }) {
    try {
      const result = await this.service.findOne(data.bankDetailsId);
      return { status: 200, message: 'Bank details fetched', data: result, error: null };
    } catch (error) {
      this.logger.error(`Get failed: ${error.message}`, error.stack);
      return { status: 500, message: error.message, data: null, error: { details: error.message } };
    }
  }

  @GrpcMethod('UniversityService', 'DeleteBankDetails')
  async delete(data: { bankDetailsId: string; userId: string }) {
    try {
      const result = await this.service.remove(data.bankDetailsId);
      await this.auditService.logAction({
        action: 'DELETE_BANK_DETAILS',
        entityType: 'BankDetails',
        entityId: data.bankDetailsId,
        userId: data.userId,
        details: result,
      });
      return {
        status: 200,
        message: 'Bank details deleted successfully',
        success: true,
        error: null,
      };
    } catch (error) {
      this.logger.error(`Delete failed: ${error.message}`, error.stack);
      return {
        status: 500,
        message: error.message,
        success: false,
        error: { details: error.message },
      };
    }
  }

  @GrpcMethod('UniversityService', 'ListBankDetails')
  async list(data: { universityId:number;page: number; pageSize: number }) {
    try {
      console.log("inputQueryInformation",data)
      const result = await this.service.findAll(data);
      console.log(data,"universityBankInformation",result)
      return { status: 200, message: 'University Payment Information.', data: result, error: null };
    } catch (error) {
      this.logger.error(`List failed: ${error.message}`, error.stack);
      return { status: 500, message: error.message, data: null, error: { details: error.message } };
    }
  }
}
