import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { BankDetails } from './payment.model';
import { BankDetailsService } from './payment.service';
import { BankDetailsGrpcController } from './payment.grpc.controller';
import { AuditClientService } from '../audit.service';

@Module({
  imports: [SequelizeModule.forFeature([BankDetails])],
  controllers: [BankDetailsGrpcController],
  providers: [BankDetailsService, AuditClientService],
  exports: [BankDetailsService],
})
export class PaymentDetailsModule {}
