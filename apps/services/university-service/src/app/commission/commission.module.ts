import { Modu<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { CommissionGrpcController } from './commission.grpc.controller';
import { AuditClientService } from '../audit.service';
import { CommissionService } from './commission.service';
import { UniversityCommission } from './university-commission.model';
import { ProgramCommission } from './program-commission.model';
import { StudentBaseCommission } from './student-base-commission.model';

@Module({
  imports: [SequelizeModule.forFeature([UniversityCommission,ProgramCommission,StudentBaseCommission])],
  controllers: [CommissionGrpcController],
  providers: [CommissionService, AuditClientService],
  exports: [CommissionService],
})
export class CommissionModule {}
