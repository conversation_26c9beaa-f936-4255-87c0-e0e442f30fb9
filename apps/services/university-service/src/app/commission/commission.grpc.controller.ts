import { Controller, Logger, ConflictException } from '@nestjs/common';
import { GrpcMethod } from '@nestjs/microservices';
import { CommissionService } from './commission.service';
import { AppService } from '../app.service';
import { AuditClientService } from '../audit.service';

@Controller()
export class CommissionGrpcController {
  private readonly logger = new Logger(CommissionGrpcController.name);

  constructor(
    private readonly commissionService: CommissionService,
    private readonly appService: AppService,
    private readonly auditService: AuditClientService
  ) {}

  @GrpcMethod('UniversityService', 'CreateCommission')
  async createCommission(request: any) {
    console.log('University commission ', request);
    const startTime = Date.now();

    try {
      const existingCommission =
        await this.commissionService.existingCommission(request.universityId);
      if (existingCommission) {
        return {
          status: 409,
          message:
            'Commission configuration already exists for this university. Please update it instead.',
          data: existingCommission,
          error: null,
        };
      }
      const result = await this.commissionService.create(request);

      console.log('University commission create reponse  ', result);

      this.appService.trackProcessingDuration(
        'commission_create_grpc_success',
        (Date.now() - startTime) / 1000
      );

      await this.auditService.logAction({
        action: 'CREATE_COMMISSION',
        entityType: 'Commission',
        entityId: result.commissionId.toString(),
        userId: request.userId ?? 'system',
        details: request,
      });

      return {
        status: 201,
        message: 'Commission created successfully',
        data: result,
        error: null,
      };
    } catch (error) {
      this.logger.error(
        `CreateCommission error: ${error.message}`,
        error.stack
      );
      return {
        status: 500,
        message: 'Failed to create commission',
        data: null,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'An unexpected error occurred',
          details: error.message,
          validationErrors: [],
        },
      };
    }
  }

  @GrpcMethod('UniversityService', 'UpdateCommission')
  async updateCommission(request: any) {
    try {
      console.log("Check console",request)
      const result = await this.commissionService.update(request);
      console.log("|Check response data from grpc",result)

      await this.auditService.logAction({
        action: 'UPDATE_COMMISSION',
        entityType: 'Commission',
        entityId: request.commissionId.toString(),
        userId: request.userId ?? 'system',
        details: request,
      });

      return {
        status: 200,
        message: 'Commission updated successfully',
        data: result,
        error: null,
      };
    } catch (error) {
      this.logger.error(
        `UpdateCommission error: ${error.message}`,
        error.stack
      );
      return {
        status: 500,
        message: 'Failed to update commission',
        data: null,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'An unexpected error occurred',
          details: error.message,
          validationErrors: [],
        },
      };
    }
  }

  @GrpcMethod('UniversityService', 'GetCommission')
  async getCommission(request) {
    try {
      const result = await this.commissionService.existingCommission(request.universityId);
      const data = {
        commissionId: result.id,
        universityId: result.universityId,
        userId: result.userId,
        currency: result.currency,
        paymentFrequency: result.paymentFrequency,
        paymentTerm: result.paymentTerm,
        paymentMethod: result.paymentMethod,
        commissionPayoutCycle: result.commissionPayoutCycle,
        commissionPeriod: result.commissionPeriod,
        createdAt: result.createdAt,
        updatedAt: result.updatedAt,
        commission: result.programCommissions.map((prog) => ({
          programCommissionId: prog.id,
          commissionName: prog.commissionName,
          programLevelId: prog.programLevelId,
          commissionType: prog.commissionType,
          commissionValue: prog.commissionValue,
          startDate: prog.startDate,
          endDate: prog.endDate,
          status: prog.status,
          createdAt: prog.createdAt,
          updatedAt: prog.updatedAt,
          studentBaseCommission: prog.studentBaseCommissions.map((sbc) => ({
            studentNumber: sbc.studentNumber,
            commission: sbc.commission,
          })),
        })),
      };
      return {
        status: 200,
        message: 'Commission retrieved successfully',
        data,
        error: null,
      };
    } catch (error) {
      return {
        status: 404,
        message: 'Commission not found',
        data: null,
        error: { details: error.message },
      };
    }
  }

  @GrpcMethod('UniversityService', 'ListCommissions')
  async listCommissions(request: {
    universityId: string;
    userId: string;
    page?: number;
    pageSize?: number;
  }) {
    try {
      const { rows } = await this.commissionService.findAll(
        request.universityId,
        request.page || 1,
        request.pageSize || 10
      );

      return {
        status: 200,
        message: 'Commissions fetched successfully',
        data: rows,
        error: null,
      };
    } catch (error) {
      return {
        status: 500,
        message: 'Failed to fetch commissions',
        data: null,
        error: { details: error.message },
      };
    }
  }

  @GrpcMethod('UniversityService', 'DeleteCommission')
  async deleteCommission(request: {
    commissionId: string;
    universityId: string;
    userId?: string;
  }) {
    try {
      await this.commissionService.remove(request.commissionId);

      await this.auditService.logAction({
        action: 'DELETE_COMMISSION',
        entityType: 'Commission',
        entityId: request.commissionId,
        userId: request.userId ?? 'system',
        details: request,
      });

      return {
        status: 200,
        message: 'Commission deleted successfully',
        success: true,
        error: null,
      };
    } catch (error) {
      return {
        status: 404,
        message: 'Commission not found',
        success: false,
        error: { details: error.message },
      };
    }
  }
}
