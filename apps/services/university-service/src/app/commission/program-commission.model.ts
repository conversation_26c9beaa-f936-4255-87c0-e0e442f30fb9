// ✅ program-commission.model.ts
import { Table, Column, Model, DataType, ForeignKey, BelongsTo, HasMany } from 'sequelize-typescript';
import { UniversityCommission } from './university-commission.model';
import { StudentBaseCommission } from './student-base-commission.model';

@Table({ tableName: 'program_commissions', timestamps: true })
export class ProgramCommission extends Model {
  @ForeignKey(() => UniversityCommission)
  @Column({ type: DataType.INTEGER })
  commissionId: number;

  @Column({ type: DataType.STRING })
  commissionName: string;

  @Column({ type: DataType.INTEGER })
  programLevelId: number;

  @Column({ type: DataType.STRING })
  commissionType: string;

  @Column({ type: DataType.DECIMAL(10, 2) })
  commissionValue: number;

  @Column({ type: DataType.DATE })
  startDate: Date;

  @Column({ type: DataType.DATE })
  endDate: Date;

  @Column({ type: DataType.STRING, defaultValue: 'active' })
  status: string;

  @BelongsTo(() => UniversityCommission)
  universityCommission: UniversityCommission;

  @HasMany(() => StudentBaseCommission, { as: 'studentBaseCommissions',foreignKey: 'programCommissionId'  })
  studentBaseCommissions: StudentBaseCommission[];
}
