import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { UniversityCommission } from './university-commission.model';
import { ProgramCommission } from './program-commission.model';
import { StudentBaseCommission } from './student-base-commission.model';

@Injectable()
export class CommissionService {
  constructor(
    @InjectModel(UniversityCommission)
    private commissionModel: typeof UniversityCommission,
    @InjectModel(ProgramCommission)
    private programModel: typeof ProgramCommission,
    @InjectModel(StudentBaseCommission)
    private studentModel: typeof StudentBaseCommission
  ) {}

  async create(data: any) {
    const commission = await this.commissionModel.create({
      universityId: data.universityId,
      userId: data.userId,
      currency: data.currency,
      paymentFrequency: data.paymentFrequency,
      paymentTerm: data.paymentTerm,
      paymentMethod: data.paymentMethod,
      commissionPayoutCycle: data.commissionPayoutCycle,
      commissionPeriod: data.commissionPeriod,
    });

    for (const entry of data.commission) {
      const program = await this.programModel.create({
        ...entry,
        commissionId: commission.id,
      });

      if (entry.studentBaseCommission?.length) {
        await this.studentModel.bulkCreate(
          entry.studentBaseCommission.map((sb) => ({
            ...sb,
            programCommissionId: program.id,
          }))
        );
      }
    }

    const result = await this.findOne(commission.id);
    return {
      commissionId: result.id,
      universityId: result.universityId,
      userId: result.userId,
      currency: result.currency,
      paymentFrequency: result.paymentFrequency,
      paymentTerm: result.paymentTerm,
      paymentMethod: result.paymentMethod,
      commissionPayoutCycle: result.commissionPayoutCycle,
      commissionPeriod: result.commissionPeriod,
      createdAt: result.createdAt,
      updatedAt: result.updatedAt,
      commission: result.programCommissions.map((prog) => ({
        programCommissionId: prog.id,
        commissionName: prog.commissionName,
        programLevelId: prog.programLevelId,
        commissionType: prog.commissionType,
        commissionValue: prog.commissionValue,
        startDate: prog.startDate,
        endDate: prog.endDate,
        status: prog.status,
        createdAt: prog.createdAt,
        updatedAt: prog.updatedAt,
        studentBaseCommission: prog.studentBaseCommissions.map((sbc) => ({
          studentNumber: sbc.studentNumber,
          commission: sbc.commission,
        })),
      })),
    };
  }

  async update(data: any) {
    const commission = await this.commissionModel.findByPk(data.commissionId, {
      include: [
        {
          model: this.programModel,
          as: 'programCommissions',
          include: [
            {
              model: this.studentModel,
              as: 'studentBaseCommissions',
            },
          ],
        },
      ],
    });

    if (!commission) throw new NotFoundException('Commission not found');

    await commission.update({
      currency: data.currency,
      paymentFrequency: data.paymentFrequency,
      paymentTerm: data.paymentTerm,
      paymentMethod: data.paymentMethod,
      commissionPayoutCycle: data.commissionPayoutCycle,
      commissionPeriod: data.commissionPeriod,
    });

    const existingPrograms = commission.programCommissions;
    const incomingPrograms = data.commission;

    const incomingIds = incomingPrograms
      .map((p: any) => p.programCommissionId)
      .filter(Boolean);
    const existingIds = existingPrograms.map((p: any) => p.id);

    // Delete removed programs
    const toDeleteIds = existingIds.filter(
      (id: string) => !incomingIds.includes(id)
    );
    if (toDeleteIds.length) {
      await this.studentModel.destroy({
        where: { programCommissionId: toDeleteIds },
      });
      await this.programModel.destroy({ where: { id: toDeleteIds } });
    }

    for (const entry of incomingPrograms) {
      let program;
      if (entry.programCommissionId) {
        program = await this.programModel.findByPk(entry.programCommissionId);
        if (program) {
          await program.update(entry);

          // Delete old studentBaseCommission
          await this.studentModel.destroy({
            where: { programCommissionId: program.id },
          });

          if (entry.studentBaseCommission?.length) {
            await this.studentModel.bulkCreate(
              entry.studentBaseCommission.map((sb) => ({
                ...sb,
                programCommissionId: program.id,
              }))
            );
          }
        }
      } else {
        // New program
        program = await this.programModel.create({
          ...entry,
          commissionId: commission.id,
        });

        if (entry.studentBaseCommission?.length) {
          await this.studentModel.bulkCreate(
            entry.studentBaseCommission.map((sb) => ({
              ...sb,
              programCommissionId: program.id,
            }))
          );
        }
      }
    }

    const result = await this.findOne(commission.id);
    return {
      commissionId: result.id,
      universityId: result.universityId,
      userId: result.userId,
      currency: result.currency,
      paymentFrequency: result.paymentFrequency,
      paymentTerm: result.paymentTerm,
      paymentMethod: result.paymentMethod,
      commissionPayoutCycle: result.commissionPayoutCycle,
      commissionPeriod: result.commissionPeriod,
      createdAt: result.createdAt,
      updatedAt: result.updatedAt,
      commission: result.programCommissions.map((prog) => ({
        programCommissionId: prog.id,
        commissionName: prog.commissionName,
        programLevelId: prog.programLevelId,
        commissionType: prog.commissionType,
        commissionValue: prog.commissionValue,
        startDate: prog.startDate,
        endDate: prog.endDate,
        status: prog.status,
        createdAt: prog.createdAt,
        updatedAt: prog.updatedAt,
        studentBaseCommission: prog.studentBaseCommissions.map((sbc) => ({
          studentNumber: sbc.studentNumber,
          commission: sbc.commission,
        })),
      })),
    };
  }

  async findOne(id: string) {
    return this.commissionModel.findByPk(id, {
      include: [
        {
          model: this.programModel,
          as: 'programCommissions',
          include: [
            {
              model: this.studentModel,
              as: 'studentBaseCommissions',
            },
          ],
        },
      ],
    });
  }
  async existingCommission(universityId: string) {
    return this.commissionModel.findOne({
      where: { universityId },
      include: [
        {
          model: this.programModel,
          as: 'programCommissions',
          include: [
            {
              model: this.studentModel,
              as: 'studentBaseCommissions',
            },
          ],
        },
      ],
    });
  }

  async findAll(universityId: string, page: number = 1, limit: number = 10) {
    return this.commissionModel.findAndCountAll({
      where: { universityId },
      include: [
        {
          model: ProgramCommission,
          include: [StudentBaseCommission],
        },
      ],
      offset: (page - 1) * limit,
      limit,
      order: [['createdAt', 'DESC']],
    });
  }

  async remove(commissionId: string) {
    const commission = await this.commissionModel.findByPk(commissionId);
    if (!commission) throw new NotFoundException('Commission not found');
    await commission.destroy();
  }
}
