// ✅ student-base-commission.model.ts
import { Table, Column, Model, DataType, ForeignKey, BelongsTo } from 'sequelize-typescript';
import { ProgramCommission } from './program-commission.model';

@Table({ tableName: 'student_base_commissions', timestamps: true })
export class StudentBaseCommission extends Model {
  @ForeignKey(() => ProgramCommission)
  @Column({ type: DataType.INTEGER })
  programCommissionId: number;

  @Column({ type: DataType.INTEGER })
  studentNumber: number;

  @Column({ type: DataType.STRING })
  commission: string;

  @BelongsTo(() => ProgramCommission)
  programCommission: ProgramCommission;
}
