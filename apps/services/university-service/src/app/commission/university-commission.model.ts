import { Table, Column, Model, DataType, HasMany } from 'sequelize-typescript';
import { ProgramCommission } from './program-commission.model';

@Table({ tableName: 'university_commission', timestamps: true })
export class UniversityCommission extends Model {
  @Column({ type: DataType.STRING })
  universityId: string;

  @Column({ type: DataType.STRING })
  userId: string;

  @Column({ type: DataType.STRING })
  currency: string;

  @Column({ type: DataType.STRING })
  paymentFrequency: string;

  @Column({ type: DataType.STRING })
  paymentTerm: string;

  @Column({ type: DataType.STRING })
  paymentMethod: string;

  @Column({ type: DataType.STRING })
  commissionPayoutCycle: string;

  @Column({ type: DataType.STRING })
  commissionPeriod: string;

  @HasMany(() => ProgramCommission, { as: 'programCommissions',foreignKey: 'commissionId' })
  programCommissions: ProgramCommission[];
}