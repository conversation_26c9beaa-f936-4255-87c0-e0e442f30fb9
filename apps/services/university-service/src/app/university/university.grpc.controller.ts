import {
  ConflictException,
  Controller,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { GrpcMethod } from '@nestjs/microservices';
import { UniversityService } from './university.service';
import { AppService } from '../app.service';
import { AuditClientService } from '../audit.service';

@Controller()
export class UniversityGrpcController {
  private readonly logger = new Logger(UniversityGrpcController.name);

  constructor(
    private readonly universityService: UniversityService,
    private readonly appService: AppService,
    private readonly auditService: AuditClientService
  ) {}

  private formatErrorResponse(error: any) {
    let status = 500;
    let message = 'Internal server error';

    if (error instanceof ConflictException) {
      status = 409;
      message = error.message || 'Conflict error';
    } else if (error instanceof NotFoundException) {
      status = 404;
      message = error.message || 'Not found';
    } else {
      message = error.message || 'Internal server error';
    }

    return {
      status,
      message,
      data: null,
      error: {
        details: error.stack || 'Unknown error',
      },
    };
  }

  @GrpcMethod('UniversityService', 'CreateUniversity')
  async createUniversity(request: any) {
    const startTime = Date.now();
    try {
      const result = await this.universityService.universityCreate(request);

      this.appService.trackProcessingDuration(
        'university_create_grpc_success',
        (Date.now() - startTime) / 1000
      );

      this.auditService.logAction({
        action: 'CREATE_UNIVERSITY',
        entityType: 'University',
        entityId: result.id.toString(),
        userId: request.userId,
        userRole: request.userRole,
        details: request,
      });

      return {
        status: 200,
        message: 'University created successfully',
        data: {
          id: result.id,
          name: result.name,
          about: result.about,
          type: result.type,
          website: result.website,
          foundedOn: result.foundedOn instanceof Date ? result.foundedOn.toISOString() : result.foundedOn,
          institutionCode: result.institutionCode,
          address: result.address,
          country: result.country,
          state: result.state,
          city: result.city,
          postalCode: result.postalCode,
          logo: result.logo,
          email: result.email,
          primaryContactNumber: result.primaryContactNumber,
          createdAt: result.createdAt.toISOString(),
          updatedAt: result.updatedAt.toISOString(),
        },
        error: null,
      };
    } catch (error) {
      this.appService.trackProcessingDuration(
        'university_create_grpc_error',
        (Date.now() - startTime) / 1000
      );
      return this.formatErrorResponse(error);
    }
  }

  @GrpcMethod('UniversityService', 'GetUniversity')
  async getUniversity(request: { id: number }) {
    const startTime = Date.now();
    try {
      const result = await this.universityService.findOne(request.id);

      this.appService.trackProcessingDuration(
        'university_get_grpc_success',
        (Date.now() - startTime) / 1000
      );

      return {
        status: 200,
        message: 'University retrieved successfully',
        data: {
          id: result.id,
          name: result.name,
          about: result.about,
          type: result.type,
          website: result.website,
          foundedOn: result.foundedOn instanceof Date ? result.foundedOn.toISOString() : result.foundedOn,
          institutionCode: result.institutionCode,
          address: result.address,
          country: result.country,
          state: result.state,
          city: result.city,
          postalCode: result.postalCode,
          logo: result.logo,
          email: result.email,
          primaryContactNumber: result.primaryContactNumber,
          createdAt: result.createdAt.toISOString(),
          updatedAt: result.updatedAt.toISOString(),
        },
        error: null,
      };
    } catch (error) {
      this.appService.trackProcessingDuration(
        'university_get_grpc_error',
        (Date.now() - startTime) / 1000
      );
      return this.formatErrorResponse(error);
    }
  }

  @GrpcMethod('UniversityService', 'UpdateUniversity')
  async updateUniversity(request: any) {
    const startTime = Date.now();
    try {
      const result = await this.universityService.update(request.id, request);

      this.appService.trackProcessingDuration(
        'university_update_grpc_success',
        (Date.now() - startTime) / 1000
      );

      this.auditService.logAction({
        action: 'UPDATE_UNIVERSITY',
        entityType: 'University',
        entityId: request.id.toString(),
        userId: request.userId || 'system',
        userRole: request.userRole,
        details: request,
      });

      return {
        status: 200,
        message: 'University updated successfully',
        data: {
          id: result.id,
          name: result.name,
          about: result.about,
          type: result.type,
          website: result.website,
          foundedOn: result.foundedOn instanceof Date ? result.foundedOn.toISOString() : result.foundedOn,
          institutionCode: result.institutionCode,
          address: result.address,
          country: result.country,
          state: result.state,
          city: result.city,
          postalCode: result.postalCode,
          logo: result.logo,
          email: result.email,
          primaryContactNumber: result.primaryContactNumber,
          createdAt: result.createdAt.toISOString(),
          updatedAt: result.updatedAt.toISOString(),
        },
        error: null,
      };
    } catch (error) {
      this.appService.trackProcessingDuration(
        'university_update_grpc_error',
        (Date.now() - startTime) / 1000
      );
      return this.formatErrorResponse(error);
    }
  }

  @GrpcMethod('UniversityService', 'DeleteUniversity')
  async deleteUniversity(request: { id: number; userId?: string }) {
    const startTime = Date.now();
    try {
      await this.universityService.remove(request.id);

      this.appService.trackProcessingDuration(
        'university_delete_grpc_success',
        (Date.now() - startTime) / 1000
      );

      this.auditService.logAction({
        action: 'DELETE_UNIVERSITY',
        entityType: 'University',
        entityId: request.id.toString(),
        userId: request.userId || 'system',
        details: { id: request.id },
      });

      return {
        status: 200,
        message: 'University deleted successfully',
        data: { success: true },
        error: null,
      };
    } catch (error) {
      this.appService.trackProcessingDuration(
        'university_delete_grpc_error',
        (Date.now() - startTime) / 1000
      );
      return this.formatErrorResponse(error);
    }
  }

  @GrpcMethod('UniversityService', 'ListUniversities')
  async listUniversities(request: { page?: number; limit?: number; isActive?: boolean }) {
    const startTime = Date.now();
    try {
      const page = request.page || 1;
      const limit = request.limit || 10;

      const result = await this.universityService.findAll(page, limit);

      this.appService.trackProcessingDuration(
        'university_list_grpc_success',
        (Date.now() - startTime) / 1000
      );

      return {
        status: 200,
        message: 'Universities retrieved successfully',
        data: {
          total: result.count,
          page,
          limit,
          universities: result.rows.map((university) => ({
            id: university.id,
            name: university.name,
            about: university.about,
            type: university.type,
            website: university.website,
            foundedOn: university.foundedOn instanceof Date ? university.foundedOn.toISOString() : university.foundedOn,
            institutionCode: university.institutionCode,
            address: university.address,
            country: university.country,
            state: university.state,
            city: university.city,
            postalCode: university.postalCode,
            logo: university.logo,
            email: university.email,
            primaryContactNumber: university.primaryContactNumber,
            createdAt: university.createdAt.toISOString(),
            updatedAt: university.updatedAt.toISOString(),
          })),
        },
        error: null,
      };
    } catch (error) {
      this.appService.trackProcessingDuration(
        'university_list_grpc_error',
        (Date.now() - startTime) / 1000
      );
      return this.formatErrorResponse(error);
    }
  }
}
