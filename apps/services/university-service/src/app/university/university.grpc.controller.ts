import {
  ConflictException,
  Controller,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { GrpcMethod } from '@nestjs/microservices';
import { UniversityService } from './university.service';
import { AppService } from '../app.service';
import { AuditClientService } from '../audit.service';

@Controller()
export class UniversityGrpcController {
  private readonly logger = new Logger(UniversityGrpcController.name);

  constructor(
    private readonly universityService: UniversityService,
    private readonly appService: AppService,
    private readonly auditService: AuditClientService
  ) {}

  // Helper method to format error responses consistently
  private formatErrorResponse(error: any) {
    let status = 500;
    let message = 'Internal server error';

    if (error instanceof ConflictException) {
      status = 409;
      message = error.message || 'Conflict error';
    } else if (error instanceof NotFoundException) {
      status = 404;
      message = error.message || 'Not found';
    } else {
      message = error.message || 'Internal server error';
    }

    return {
      status: status,
      message: message,
      data: null,
      error: {
        details: error.stack || 'Unknown error',
      },
    };
  }

  @GrpcMethod('UniversityService', 'CreateUniversity')
  async createUniversity(request: any) {
    const startTime = Date.now();
    try {
      this.logger.log(
        `gRPC CreateUniversity request for name: ${request.name}`
      );
      console.log('==================>Request', request);

      const result = await this.universityService.universityCreate({
        userId: request.userId, // Now expecting string from proto
        name: request.name,
        about: request.about,
        type: request.type,
        website: request.website,
        foundedOn: request.foundedOn,
        dliNumber: request.dliNumber,
        address: request.address,
        countryId: request.countryId,
        state: request.state,
        city: request.city,
        postalCode: request.postalCode,
        logo: request.logo, // ✅ NEW
      });

      this.appService.trackProcessingDuration(
        'university_create_grpc_success',
        (Date.now() - startTime) / 1000
      );

      // Audit logging with string userId
      this.auditService.logAction({
        action: 'CREATE_UNIVERSITY',
        entityType: 'University',
        entityId: result.id.toString(),
        userId: request.userId,
        userRole: request.userRole,
        details: request,
      });

      // Format response according to proto definition
      return {
        status: 200,
        message: 'University created successfully',
        data: {
          id: result.id,
          name: result.name,
          about: result.about,
          type: result.type,
          website: result.website,
          foundedOn:
            result.foundedOn instanceof Date
              ? result.foundedOn.toISOString()
              : result.foundedOn,
          dliNumber: result.dliNumber,
          address: result.address,
          // countryId: result.countryId,
          state: result.state,
          city: result.city,
          postalCode: result.postalCode,
          logo: result.logo, // ✅ NEW
          createdAt:
            result.createdAt instanceof Date
              ? result.createdAt.toISOString()
              : result.createdAt,
          updatedAt:
            result.updatedAt instanceof Date
              ? result.updatedAt.toISOString()
              : result.updatedAt,
        },
        error: null,
      };
    } catch (error) {
      this.appService.trackProcessingDuration(
        'university_create_grpc_error',
        (Date.now() - startTime) / 1000
      );
      return this.formatErrorResponse(error);
    }
  }

  @GrpcMethod('UniversityService', 'GetUniversity')
  async getUniversity(request: { id: number }) {
    const startTime = Date.now();
    try {
      this.logger.log(`gRPC GetUniversity request for ID: ${request.id}`);

      const result = await this.universityService.findOne(request.id);

      this.appService.trackProcessingDuration(
        'university_get_grpc_success',
        (Date.now() - startTime) / 1000
      );
      return {
        status: 200,
        message: 'University retrieved successfully',
        data: {
          id: result.id,
          name: result.name,
          about: result.about,
          type: result.type,
          website: result.website,
          foundedOn:
            result.foundedOn instanceof Date
              ? result.foundedOn.toISOString()
              : result.foundedOn,
          dliNumber: result.dliNumber,
          address: result.address,
          // countryId: result.countryId,
          state: result.state,
          city: result.city,
          postalCode: result.postalCode,

          logo: result.logo, // ✅ NEW
          createdAt:
            result.createdAt instanceof Date
              ? result.createdAt.toISOString()
              : result.createdAt,
          updatedAt:
            result.updatedAt instanceof Date
              ? result.updatedAt.toISOString()
              : result.updatedAt,
        },
        error: null,
      };
    } catch (error) {
      this.appService.trackProcessingDuration(
        'university_get_grpc_error',
        (Date.now() - startTime) / 1000
      );
      return this.formatErrorResponse(error);
    }
  }

  @GrpcMethod('UniversityService', 'UpdateUniversity')
  async updateUniversity(request: any) {
    const startTime = Date.now();
    try {
      this.logger.log(`gRPC UpdateUniversity request for ID: ${request.id}`);

      const updateData: any = {};
      if (request.name !== undefined) updateData.name = request.name;
      if (request.about !== undefined) updateData.about = request.about;
      if (request.type !== undefined) updateData.type = request.type;
      if (request.website !== undefined) updateData.website = request.website;
      if (request.foundedOn !== undefined)
        updateData.foundedOn = request.foundedOn;
      if (request.dliNumber !== undefined)
        updateData.dliNumber = request.dliNumber;
      if (request.address !== undefined) updateData.address = request.address;
      if (request.countryId !== undefined)
        updateData.countryId = request.countryId;
      if (request.state !== undefined) updateData.state = request.state;
      if (request.city !== undefined) updateData.city = request.city;
      if (request.postalCode !== undefined)
        updateData.postalCode = request.postalCode;
      if (request.logo !== undefined) updateData.logo = request.logo;

      const result = await this.universityService.update(
        request.id,
        updateData
      );

      this.appService.trackProcessingDuration(
        'university_update_grpc_success',
        (Date.now() - startTime) / 1000
      );

      // Audit logging
      this.auditService.logAction({
        action: 'UPDATE_UNIVERSITY',
        entityType: 'University',
        entityId: request.id.toString(),
        userId: request.userId || 'system',
        details: request,
      });

      return {
        status: 200,
        message: 'University updated successfully',
        data: {
          id: result.id,
          name: result.name,
          about: result.about,
          type: result.type,
          website: result.website,
          foundedOn:
            result.foundedOn instanceof Date
              ? result.foundedOn.toISOString()
              : result.foundedOn,
          dliNumber: result.dliNumber,
          address: result.address,
          // countryId: result.countryId,
          state: result.state,
          city: result.city,
          postalCode: result.postalCode,
          logo: result.logo, // ✅ NEW
          createdAt:
            result.createdAt instanceof Date
              ? result.createdAt.toISOString()
              : result.createdAt,
          updatedAt:
            result.updatedAt instanceof Date
              ? result.updatedAt.toISOString()
              : result.updatedAt,
        },
        error: null,
      };
    } catch (error) {
      this.appService.trackProcessingDuration(
        'university_update_grpc_error',
        (Date.now() - startTime) / 1000
      );
      return this.formatErrorResponse(error);
    }
  }

  @GrpcMethod('UniversityService', 'DeleteUniversity')
  async deleteUniversity(request: { id: number }) {
    const startTime = Date.now();
    try {
      this.logger.log(`gRPC DeleteUniversity request for ID: ${request.id}`);

      await this.universityService.remove(request.id);

      this.appService.trackProcessingDuration(
        'university_delete_grpc_success',
        (Date.now() - startTime) / 1000
      );

      // Audit logging
      this.auditService.logAction({
        action: 'DELETE_UNIVERSITY',
        entityType: 'University',
        entityId: request.id.toString(),
        userId: 'system',
        details: { id: request.id },
      });

      return {
        status: 200,
        message: 'University deleted successfully',
        data: { success: true },
        error: null,
      };
    } catch (error) {
      this.appService.trackProcessingDuration(
        'university_delete_grpc_error',
        (Date.now() - startTime) / 1000
      );
      return this.formatErrorResponse(error);
    }
  }

  @GrpcMethod('UniversityService', 'ListUniversities')
  async listUniversities(request: {
    page?: number;
    limit?: number;
    isActive?: boolean;
  }) {
    const startTime = Date.now();
    try {
      this.logger.log(
        `gRPC ListUniversities request - page: ${request.page}, limit: ${request.limit}`
      );

      const page = request.page || 1;
      const limit = request.limit || 10;

      const result = await this.universityService.findAll(page, limit);
      this.appService.trackProcessingDuration(
        'university_list_grpc_success',
        (Date.now() - startTime) / 1000
      );

      // Log the response we're sending back
      const response = {
        status: 200,
        message: 'Universities retrieved successfully',
        data: {
          total: result.count,
          page: page,
          limit: limit,
          universities: result.rows.map((university) => ({
            id: university.id,
            name: university.name,
            about: university.about,
            type: university.type,
            website: university.website,
            foundedOn:
              university.foundedOn instanceof Date
                ? university.foundedOn.toISOString()
                : university.foundedOn,
            dliNumber: university.dliNumber,
            address: university.address,
            state: university.state,
            city: university.city,
            postalCode: university.postalCode,
            logo: university.logo, // ✅ NEW
            createdAt:
              university.createdAt instanceof Date
                ? university.createdAt.toISOString()
                : university.createdAt,
            updatedAt:
              university.updatedAt instanceof Date
                ? university.updatedAt.toISOString()
                : university.updatedAt,
          })),
        },
        error: null,
      };

      return response;
    } catch (error) {
      this.appService.trackProcessingDuration(
        'university_list_grpc_error',
        (Date.now() - startTime) / 1000
      );
      return this.formatErrorResponse(error);
    }
  }
}
