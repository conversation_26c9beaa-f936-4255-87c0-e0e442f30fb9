
import { Modu<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { UniversityService } from './university.service';
import { University } from './university.model';
import { UniversityGrpcController } from './university.grpc.controller';
import { AuditClientService } from '../audit.service';

@Module({
  imports: [SequelizeModule.forFeature([University])],
  controllers: [ UniversityGrpcController],
  providers: [UniversityService, AuditClientService],
  exports: [UniversityService]
})
export class UniversityModule {}

