import { Table, Column, <PERSON>Type, HasMany, HasOne, ForeignKey, BelongsTo } from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
export enum UniversityType {
  PUBLIC = 'public',
  PRIVATE = 'private',
}
@Table({ tableName: 'universities' })
export class University extends BaseModel {
  @Column(DataType.BIGINT)
  userId!: number;

  @Column(DataType.TEXT)
  name!: string;

  @Column(DataType.TEXT)
  about!: string;

  @Column({
    type: DataType.ENUM(...Object.values(UniversityType)),
    allowNull: false,
  })
  type!: UniversityType;

  @Column(DataType.STRING)
  website!: string;

  @Column(DataType.DATE)
  foundedOn!: Date;

  @Column(DataType.STRING)
  dliNumber!: string;

  @Column(DataType.STRING)
  address!: string;

  @Column(DataType.STRING)
  state!: string;

  @Column(DataType.STRING)
  city!: string;

  @Column(DataType.STRING)
  postalCode!: string;

  @Column(DataType.STRING)
  logo !:string;

}