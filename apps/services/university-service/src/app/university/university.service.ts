import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { University, UniversityType } from './university.model';
import { BaseRepository } from '@apply-goal-backend/database';
import {
  CreateUniversityDto,
  UpdateUniversityDto,
} from '@apply-goal-backend/dto';
import { Op } from 'sequelize';
import { AppService } from '../app.service';

@Injectable()
export class UniversityService {
  private universityRepository: BaseRepository<University>;

  constructor(
    @InjectModel(University)
    private universityModel: typeof University,
    private readonly appService: AppService,
  ) {
    this.universityRepository = new BaseRepository<University>(universityModel);
  }

  async universityCreate(createUniversityDto: CreateUniversityDto): Promise<University> {
    try {
      const existingUniversity = await this.universityRepository.findOne({
        where: { name: createUniversityDto.name },
      });

      if (existingUniversity) {
        throw new ConflictException(`University with name "${createUniversityDto.name}" already exists`);
      }
      let userId = parseInt(createUniversityDto.userId, 10);
      const university = await this.universityRepository.create({
        ...createUniversityDto,
        userId: userId || null,
        foundedOn: new Date(createUniversityDto.foundedOn) || undefined,
        type: createUniversityDto.type as UniversityType,
      });

      return university;
    } catch (error) {
      if (error instanceof ConflictException) {
        throw error;
      }
      throw new NotFoundException(`Failed to create university: ${error.message}`);
    }
  }
  async findOne(id: number): Promise<University> {
    const university = await this.universityRepository.findById(id);

    if (!university) {
      throw new NotFoundException(`University with ID ${id} not found`);
    }

    return university;
  }

  async update(id: number, updateUniversityDto: UpdateUniversityDto): Promise<University> {
    if (updateUniversityDto.name) {
      const existingUniversity = await this.universityRepository.findOne({
        where: {
          name: updateUniversityDto.name,
          id: { [Op.ne]: id },
        },
      });

      if (existingUniversity) {
        throw new ConflictException(`University with name "${updateUniversityDto.name}" already exists`);
      }
    }

    const university = await this.findOne(id);
    await university.update(updateUniversityDto);
    return university;
  }
async findAll(page = 1, limit = 10): Promise<{ rows: University[]; count: number }> {
  return this.universityRepository.findAndCountAll({
    offset: (page - 1) * limit,
    limit,
    order: [['createdAt', 'DESC']],
    attributes: [
      'id',
      'name',
      'about',
      'type',
      'website',
      'foundedOn',
      'institutionCode',
      'address',
      'country',
      'state',
      'city',
      'postalCode',
      'logo',
      'email',
      'primaryContactNumber',
      'createdAt',
      'updatedAt',
    ],
  });
}

  async remove(id: number): Promise<void> {
    const deleted = await this.universityRepository.delete(id);
    if (deleted === 0) {
      throw new NotFoundException(`University with ID ${id} not found`);
    }
  }
}
