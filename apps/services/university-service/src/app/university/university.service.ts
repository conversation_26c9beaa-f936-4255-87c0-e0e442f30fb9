import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { University, UniversityType } from './university.model';
import { BaseRepository } from '@apply-goal-backend/database';
import { CreateUniversityDto, UpdateUniversityDto, UniversityFullPayloadDto } from '@apply-goal-backend/dto';
import { Op } from 'sequelize';
import { AppService } from '../app.service';

@Injectable()
export class UniversityService {
  private universityRepository: BaseRepository<University>;

  constructor(
    @InjectModel(University)
    private universityModel: typeof University,
    private readonly appService: AppService,
  ) {
    this.universityRepository = new BaseRepository<University>(universityModel);
  }
  async universityCreate(createUniversityDto: CreateUniversityDto): Promise<University> {
    const startTime = Date.now();
    try {
      // Check if university with same name already exists
      const existingUniversity = await this.universityRepository.findOne({
        where: {
          name: createUniversityDto.name
        }
      });
  
      if (existingUniversity) {
        // Use proper NestJS exception for gRPC compatibility
        throw new ConflictException(`University with name "${createUniversityDto.name}" already exists`);
      }
      
      // Convert gRPC timestamp to JavaScript Date for database storage
      let foundedDate = createUniversityDto.foundedOn;

      
      // Convert userId to number if it's a string (from proto)
      let userId = createUniversityDto.userId;
      if (userId && typeof userId === 'string') {
        userId = userId === 'system' ? null : parseInt(userId, 10);
      }
  
      const university = await this.universityRepository.create({
        ...createUniversityDto,
        userId: userId || null,
        foundedOn: new Date(foundedDate) || undefined,
        type: createUniversityDto.type as UniversityType
      });
      
      return university;
    } catch (error) {
      // Ensure we're throwing a proper NestJS exception
      if (error instanceof ConflictException) {
        throw error;
      }
      throw new NotFoundException(`Failed to create university: ${error.message}`);
    }
  }

  async create(universityPayloadDto: UniversityFullPayloadDto): Promise<any> {
    const startTime = Date.now();
    try {
      // Check if university with same name already exists
      // const existingUniversity = await this.universityRepository.findOne({
      //   where: {
      //     name: universityPayloadDto.name
      //   }
      // });

      // if (existingUniversity) {
      //   this.appService.trackProcessingDuration('university_create_conflict', (Date.now() - startTime) / 1000);
      //   throw new ConflictException(`University with name "${universityPayloadDto.name}" already exists`);
      // }

      // 1. Create the university first
          // Filter the full payload to only include University model fields
    const universityData: CreateUniversityDto = {
      userId: universityPayloadDto.userId,
      name: universityPayloadDto.name,
      about: universityPayloadDto.about,
      type: universityPayloadDto.type,
      website: universityPayloadDto.website,
      foundedOn: universityPayloadDto.foundedOn,
      dliNumber: universityPayloadDto.dliNumber,
      address: universityPayloadDto.address,
      countryId: universityPayloadDto.countryId,
      state: universityPayloadDto.state,
      city: universityPayloadDto.city,
      postalCode: universityPayloadDto.postalCode,
      logo:universityPayloadDto.logo
    };
     const university = await this.universityCreate(universityData);
      

      return university;
    } catch (error) {
      throw error;
    }
  }


  async findAll(page: number = 1, limit: number = 10): Promise<{ rows: University[]; count: number }> {
    return this.universityRepository.findAndCountAll({
      offset: (page - 1) * limit,
      limit,
      order: [['createdAt', 'DESC']],
    });
  }

  async findOne(id: number): Promise<University> {
    const university = await this.universityRepository.findById(id, {
      // include: ['campuses', 'applicationSteps']
    });
    
    if (!university) {
      throw new NotFoundException(`University with ID ${id} not found`);
    }
    
    return university;
  }

  async update(id: number, updateUniversityDto: UpdateUniversityDto): Promise<University> {
    if (updateUniversityDto.name) {
      const existingUniversity = await this.universityRepository.findOne({
        where: {
          name: updateUniversityDto.name,
          id: { [Op.ne]: id } // Exclude current university
        }
      });

      if (existingUniversity) {
        throw new ConflictException(`University with name "${updateUniversityDto.name}" already exists`);
      }
    }

    const university = await this.findOne(id);
    await university.update(updateUniversityDto);
    return university;
  }

  async updateWithFullPayload(id: number, payload: UniversityFullPayloadDto): Promise<any> {
    const startTime = Date.now();
    try {
      // 1. Update the university first
      const university = await this.update(id, payload);
      
      // 2. Update application steps (remove existing and create new ones)
      if (payload.applicationStep && payload.applicationStep.length > 0) {
        // await this.applicationStepService.removeAllForUniversity(id);
        // await this.applicationStepService.createBulkForUniversity(id, payload.applicationStep);
      }
      
      // 3. Update campuses (remove existing and create new ones)
      // if (payload.campuses && payload.campuses.length > 0) {
      //   // Get existing campuses
      //   const existingCampuses = await this.campusService.findAll(id, 1, 1000);
        
      //   // Delete existing campuses
      //   for (const campus of existingCampuses.rows) {
      //     await this.campusService.remove(campus.id);
      //   }
        
      //   // Create new campuses
      //   for (const campus of payload.campuses) {
      //     await this.campusService.create({
      //       ...campus,
      //       universityId: id
      //     });
      //   }
      // }
      
      // 4. Return the university with all related data
      const result = await this.findOne(id);
      
      // this.appService.trackProcessingDuration('university_update_full_payload_success', (Date.now() - startTime) / 1000);
      return result;
    } catch (error) {
      this.appService.trackProcessingDuration('university_update_full_payload_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  async remove(id: number): Promise<void> {
    // First remove all related application steps
    // await this.applicationStepService.removeAllForUniversity(id);
    
    // Then remove all related campuses
 
    
    // Then remove the university
    const deleted = await this.universityRepository.delete(id);
    
    if (deleted === 0) {
      throw new NotFoundException(`University with ID ${id} not found`);
    }
  }
}
