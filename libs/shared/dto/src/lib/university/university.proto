syntax = "proto3";

package university;


service UniversityService {

  // University operations
  rpc CreateUniversity(CreateUniversityRequest) returns (UniversityResponse);
  rpc UpdateUniversity(UpdateUniversityRequest) returns (UniversityResponse);
  rpc GetUniversity(GetUniversityRequest) returns (UniversityResponse);
  rpc DeleteUniversity(DeleteUniversityRequest) returns (UniversityResponse);
  rpc ListUniversities(ListUniversitiesRequest) returns (ListUniversitiesResponse);
  
  // Country operations
  rpc CreateCountry(CreateCountryRequest) returns (CreateCountryListResponse);
  rpc GetCountry(GetCountryRequest) returns (CountryResponse);
  rpc UpdateCountry(UpdateCountryRequest) returns (UpdateCountryResponse);
  rpc DeleteCountry(DeleteCountryRequest) returns (DeleteResponse);
  rpc ListCountries(ListCountriesRequest) returns (CountryListResponse);



  // Course Study Field operations
  rpc CreateCourseStudyField(CreateCourseStudyFieldRequest) returns (CourseStudyFieldResponse);
  rpc GetCourseStudyField(GetCourseStudyFieldRequest) returns (CourseStudyFieldResponse);
  rpc UpdateCourseStudyField(UpdateCourseStudyFieldRequest) returns (CourseStudyFieldResponse);
  rpc DeleteCourseStudyField(DeleteCourseStudyFieldRequest) returns (DeleteCourseStudyFieldResponse);
  rpc ListCourseStudyFields(ListCourseStudyFieldsRequest) returns (ListCourseStudyFieldsResponse);
  rpc GetCourseStudyFieldsByCourse(GetCourseStudyFieldsByCourseRequest) returns (ListCourseStudyFieldsResponse);
  rpc GetCourseStudyFieldsByStudyField(GetCourseStudyFieldsByStudyFieldRequest) returns (ListCourseStudyFieldsResponse);

  // Forward intake methods here (optional)
  rpc CreateIntake(CreateIntakeRequest) returns (IntakeResponse);
  rpc UpdateIntake(UpdateIntakeRequest) returns (ListIntakesResponse);
  rpc DeleteIntake(DeleteIntakeRequest) returns (IntakeResponse);
  rpc ListIntakesByUniversity(ListIntakesRequest) returns (ListIntakesResponse);

  // Create a new program level
  rpc CreateProgramLevel(CreateProgramLevelRequest) returns (ProgramLevelResponse);
  rpc UpdateProgramLevel(UpdateProgramLevelRequest) returns (ProgramLevelResponse);
  rpc DeleteProgramLevel(DeleteProgramLevelRequest) returns (ProgramLevelResponse);
  rpc GetProgramLevel(GetProgramLevelRequest) returns (ProgramLevelResponse);
  rpc ListProgramLevelByUniversity(GetProgramLevelListRequest) returns (ProgramLevelListResponse);

  // Create a new fields of study
  rpc CreateFieldOfStudy(CreateFieldOfStudyRequest) returns (FieldOfStudyListResponse);
  rpc UpdateFieldOfStudy(UpdateFieldOfStudyRequest) returns (FieldOfStudyListResponse);
  rpc DeleteFieldOfStudy(DeleteFieldOfStudyRequest) returns (FieldOfStudyResponse);
  rpc GetFieldOfStudy(GetFieldOfStudyRequest) returns (FieldOfStudyResponse);
  rpc listFieldOfStudyByUniversity(ListFieldOfStudyRequest) returns (FieldOfStudyListResponse);

  // Commission Service Definition
  rpc CreateCommission(CreateCommissionRequest) returns (CreateCommissionResponse);
  rpc UpdateCommission(UpdateCommissionRequest) returns (UpdateCommissionResponse);
  rpc GetCommission(GetCommissionRequest) returns (CreateCommissionResponse);
  rpc GetCommissionsList(GetCommissionsListRequest) returns (UpdateCommissionResponse);

  // BankServices
  rpc CreateBankDetails(CreateBankDetailsRequest) returns (CreateBankDetailsResponse);
  rpc GetBankDetails(GetBankDetailsRequest) returns (GetBankDetailsResponse);
  rpc UpdateBankDetails(UpdateBankDetailsRequest) returns (UpdateBankDetailsResponse);
  rpc DeleteBankDetails(DeleteBankDetailsRequest) returns (DeleteBankDetailsResponse);
  rpc ListBankDetails(ListBankDetailsRequest) returns (ListBankDetailsResponse);

  //ContactDetails
  rpc CreateUniversityContact(CreateUniversityContactRequest) returns (UniversityContactResponse);
  rpc UpdateUniversityContact(UpdateUniversityContactRequest) returns (UniversityContactResponse);
  rpc GetUniversityContact(GetUniversityContactRequest) returns (UniversityContactResponse);
  rpc ListUniversityContact(ListUniversityContactRequest) returns (UniversityContactListResponse);
  rpc DeleteUniversityContact(DeleteUniversityContactRequest) returns (DeleteUniversityContactResponse);

  //General setting 
  rpc CreateUniversityGeneral(CreateUniversityGeneralRequest) returns (UniversityGeneralResponse);
  rpc UpdateUniversityGeneral(UpdateUniversityGeneralRequest) returns (UniversityGeneralResponse);
  rpc GetUniversityGeneral(GetUniversityGeneralRequest) returns (UniversityGeneralResponse);
  rpc DeleteUniversityGeneral(DeleteUniversityGeneralRequest) returns (DeleteUniversityGeneralResponse);

  //University fee create 
  rpc CreateProgramLevelIntakeFee(CreateProgramLevelIntakeFeeRequest) returns (ProgramLevelIntakeFeeListResponse);
  rpc UpdateProgramLevelIntakeFee(UpdateProgramLevelIntakeFeeRequest) returns (ProgramLevelIntakeFeeResponse);
  rpc GetProgramLevelIntakeFee(GetProgramLevelIntakeFeeRequest) returns (ProgramLevelIntakeFeeResponse);
  rpc ListProgramLevelIntakeFees(ListProgramLevelIntakeFeeByUniversityRequest) returns (ProgramLevelIntakeFeeListResponse);
  rpc DeleteProgramLevelIntakeFee(DeleteProgramLevelIntakeFeeRequest) returns (DeleteProgramLevelIntakeFeeResponse);

  // Course  
  rpc CreateCourse (Course) returns (CourseResponse);
  rpc UpdateCourse (UpdateCourseRequest) returns (CourseResponse);
  rpc GetCourse (GetCourseRequest) returns (CourseResponse);
  rpc DeleteCourse (DeleteCourseRequest) returns (DeleteCourseResponse);
  rpc ListCourse (ListCourseRequest) returns (ListCourseResponse);


  // Campus Operation
  rpc CreateCampus(CreateCampusRequest) returns (CampusResponse);
  rpc UpdateCampus(UpdateCampusRequest) returns (CampusResponse);
  rpc DeleteCampus(IdRequest) returns (CampusResponse);
  rpc GetCampusById(IdRequest) returns (CampusResponse);
  rpc GetCampusList(ListCampusRequest) returns (CampusListResponse);

  //Alumni Operation 
  rpc CreateAlumni(CreateAlumniRequest) returns (AlumniListResponse);
  rpc UpdateAlumni(UpdateAlumniRequest) returns (AlumniListResponse);
  rpc DeleteAlumni(IdRequest) returns (AlumniResponse);
  rpc GetAlumni(IdRequest) returns (AlumniResponse);
  rpc GetAlumniList(ListAlumniRequest) returns (AlumniListResponse);
 // Dummy configuration 
 
  rpc CreateTestScore(CreateDummyTestScoreRequest) returns (CreateDummyTestScoreResponse);
  rpc DeleteTestScore(DeleteDummyTestScoreRequest) returns (DeleteDummyTestScoreResponse);
  rpc ListTestScores(ListDummyTestScoresRequest) returns (ListDummyTestScoresResponse);

  rpc CreateApplicationStep(CreateDummyApplicationStepRequest) returns (CreateDummyApplicationStepResponse);
  rpc DeleteApplicationStep(DeleteDummyApplicationStepRequest) returns (DeleteDummyApplicationStepResponse);
  rpc ListApplicationSteps(ListDummyApplicationStepsRequest) returns (ListDummyApplicationStepsResponse);

  rpc CreateLectureLanguage(CreateDummyLectureLanguageRequest) returns (CreateDummyLectureLanguageResponse);
  rpc DeleteLectureLanguage(DeleteDummyLectureLanguageRequest) returns (DeleteDummyLectureLanguageResponse);
  rpc ListLectureLanguages(ListDummyLectureLanguagesRequest) returns (ListDummyLectureLanguagesResponse);

  rpc CreateSocialPlatform(CreateDummySocialPlatformRequest) returns (CreateDummySocialPlatformResponse);
  rpc DeleteSocialPlatform(DeleteDummySocialPlatformRequest) returns (DeleteDummySocialPlatformResponse);
  rpc ListSocialPlatforms(ListDummySocialPlatformsRequest) returns (ListDummySocialPlatformsResponse);

}
// ============================== Shared Error Structure ==============================

    // Standard error response
    message ErrorResponse {
      string code = 1;                             // Error code
      string message = 2;                          // Human-readable error
      string details = 3;                          // Developer-focused detail
      repeated ValidationError validationErrors = 4; // Field-specific errors
    }

    // Validation errors for form or field-level feedback
    message ValidationError {
      string field = 1;                            // Name of the field with error
      string message = 2;                          // Error message for the field
      string constraint = 3;                       // Constraint rule name
    }
// ============================== University Messages ==============================

  // University entity containing core attributes
    message University {
      int64 id = 1;                                // Unique university ID
      string name = 2;                             // University name
      string address = 3;                          // Address of the university
      int64 countryId = 4;                         // Linked country ID
      string state = 5;                            // State/Province
      string city = 6;                             // City name
      string postalCode = 7;                       // Postal code
      string about = 8;                            // Description
      string type = 9;                             // University type (public/private)
      string website = 10;                         // Website URL
      string foundedOn = 11;                       // Year or date founded
      string dliNumber = 12;                       // Designated Learning Institution number
      string logo = 13;                            // University logo URL or filename
      string createdAt = 14;                       // Creation timestamp
      string updatedAt = 15;                       // Last updated timestamp
    }

    // Create university request
    message CreateUniversityRequest {
      string userId = 1;
      string userRole = 2;                           // ID of user performing creation
      string name = 3;
      string address = 4;
      int64 countryId = 5;
      string state = 6;
      string city = 7;
      string postalCode = 8;
      string about = 9;
      string type = 10;
      string website = 11;
      string foundedOn = 12;
      string dliNumber = 13;
      string logo = 14;                            // Logo to upload or URL
    }

    // Update university request
    message UpdateUniversityRequest {
      int64 id = 1;                                // University ID to update
      string name = 2;
      string address = 3;
      int64 countryId = 4;
      string state = 5;
      string city = 6;
      string postalCode = 7;
      string about = 8;
      string type = 9;
      string website = 10;
      string foundedOn = 11;
      string dliNumber = 12;
      string logo = 13;                            // Updated logo
    }

  // Get university by ID
    message GetUniversityRequest {
      int64 id = 1;                                // University ID to fetch
    }

  // Delete university request
    message DeleteUniversityRequest {
      int64 id = 1;                                // University ID to delete
    }

  // Response for university operations
    message UniversityResponse {
      int32 status = 1;                            // HTTP-like status code
      string message = 2;                          // Response message
      University data = 3;                         // University object
      ErrorResponse error = 4;                     // Error object if any
    }

  // Paginated university list request
    message ListUniversitiesRequest {
      int32 page = 1;                              // Page number
      int32 limit = 2;                             // Items per page
    }

  // Paginated university list response
    message ListUniversitiesResponse {
      int32 status = 1;                            // Status code
      string message = 2;                          // Message
      UniversitiesData data = 3;                  // University list payload
      ErrorResponse error = 4;                     // Error info
    }

  // Data structure holding paginated university list
    message UniversitiesData {
      int32 total = 1;                             // Total universities
      int32 page = 2;                              // Current page
      int32 limit = 3;                             // Items per page
      repeated University universities = 4;        // Array of universities
    }

// ============================== Country Messages ==============================
  // Country entity structure
  message Country {
    int64 id = 1;
    int64 universityId = 2;
    string countryName = 3;
    bool isActive = 4;
    string createdAt = 5;
    string updatedAt = 6;
  }

  // Request payloads
  message CreateCountryRequest {
    int64 universityId = 1;
    string userId = 2;
    string userRole = 3;
    repeated string countryNames = 4;
    bool isActive = 5;
  }

  message UpdateCountryRequest {
    int64 universityId = 1;
    string userId = 2;
    string userRole = 3;
    repeated string countryNames = 4;
  }

  message GetCountryRequest {
    int64 id = 1;
    string userId = 2;
  }

  message DeleteCountryRequest {
    int64 id = 1;
    int64 universityId = 2;
    string userId = 3;
  }

  message ListCountriesRequest {
    int64 universityId = 1;
    int32 page = 2;
    int32 limit = 3;
    bool isActive = 4;
  }

  message CreateCountryListResponse {
    int32 status = 1;
    string message = 2;
    repeated Country data = 3;
    ErrorResponse error = 4;
  }

  // ✅ UPDATED
  message UpdateCountryResponse {
    int32 status = 1;
    string message = 2;
    repeated Country data = 3; // Only return the updated list of countries
    ErrorResponse error = 4;
  }

  message CountryResponse {
    int32 status = 1;
    string message = 2;
    Country data = 3;
    ErrorResponse error = 4;
  }

  message CountryListResponse {
    int32 status = 1;
    string message = 2;
    repeated Country data = 3;
    ErrorResponse error = 4;
  }

  message DeleteResponse {
    int32 status = 1;
    string message = 2;
    ErrorResponse error = 3;
  }




// ============================== Course Study Field Messages ==============================

  // Request to create a new course-study field link
    message CreateCourseStudyFieldRequest {
      int64 course_id = 1;                        // ID of the course
      int64 study_field_id = 2;                  // ID of the study field
      bool is_active = 3;                        // Whether the link is active
    }

  // Request to get a specific course-study field link by ID
    message GetCourseStudyFieldRequest {
      int64 id = 1;                              // Unique ID of the link
    }

  // Request to update the active status of a course-study field link
    message UpdateCourseStudyFieldRequest {
      int64 id = 1;                              // Unique ID of the link
      bool is_active = 2;                        // New active status
    }

    // Request to delete a course-study field link by ID
    message DeleteCourseStudyFieldRequest {
      int64 id = 1;                              // Unique ID to delete
    }

  // Detailed response for a single course-study field link
    message CourseStudyFieldResponse {
      int64 id = 1;                              // Link ID
      int64 course_id = 2;                       // Course ID
      int64 study_field_id = 3;                  // Study Field ID
      bool is_active = 4;                        // Active status
      CourseInfo course = 5;                     // Nested course info
      StudyFieldInfo study_field = 6;            // Nested study field info
      string created_at = 7;                     // Timestamp of creation
      string updated_at = 8;                     // Timestamp of last update
      ErrorResponse error = 9;                   // Error info if any
    }

  // Basic course information used in nested responses
    message CourseInfo {
      int64 id = 1;                              // Course ID
      string title = 2;                          // Course title
    }

  // Basic study field information used in nested responses
    message StudyFieldInfo {
      int64 id = 1;                              // Study field ID
      string field = 2;                          // Study field name
    }

  // Request to list all course-study field links with filters
    message ListCourseStudyFieldsRequest {
      int32 page = 1;                            // Page number
      int32 limit = 2;                           // Page size
      int64 course_id = 3;                       // Optional course filter
      int64 study_field_id = 4;                  // Optional study field filter
      bool is_active = 5;                        // Filter by active status
    }

  // Response for listing course-study field links
    message ListCourseStudyFieldsResponse {
      int32 total = 1;                           // Total results
      int32 page = 2;                            // Current page
      int32 limit = 3;                           // Results per page
      repeated CourseStudyFieldResponse course_study_fields = 4;  // List of results
      ErrorResponse error = 5;                   // Error details
    }

  // Request to get all course-study field links by course ID
    message GetCourseStudyFieldsByCourseRequest {
      int64 course_id = 1;                       // Course ID
    }

  // Request to get all course-study field links by study field ID
    message GetCourseStudyFieldsByStudyFieldRequest {
      int64 study_field_id = 1;                  // Study Field ID
    }

  // Response after deleting a course-study field link
    message DeleteCourseStudyFieldResponse {
      int32 status = 1;                          // Status code
      string message = 2;                              // Deleted link ID
      ErrorResponse error = 5;                   // Error info
    }



// ============================== Intake Messages ==============================

  // Defines application-specific dates for a given student type (e.g., regular, transfer)
    message IntakeDate {
      string studentType = 1;             // Type of student (e.g., "regular", "transfer")
      string applicationOpen = 2;         // Application open date
      string applicationEnd = 3;          // Application close date
      string enrollmentDeadline = 4;      // Enrollment deadline
      string actions = 5;                 // Optional flags or notes related to this intake (e.g., UI actions or processing status)
    }

  // Represents a specific intake (e.g., Fall 2025), which may have different dates for different student types
    message IntakeItem {
      int32 id = 1;                       // Intake ID (used in updates; optional for creation)
      string name = 2;                    // Intake name (e.g., "Fall", "Spring")
      string startDate = 3;              // Intake start date
      string endDate = 4;                // Intake end date
      repeated IntakeDate applicationDates = 5; // Nested application/enrollment dates per student type
    }

  // Request structure to create multiple intakes under a university
    message CreateIntakeRequest {
      int32 universityId = 1;
      string userId = 2;
      string userRole = 3;         // Foreign key referencing the University
      repeated IntakeItem intake = 4;    // List of intake items to be created
    }

  // Request structure to update one or more intakes under a university
    message UpdateIntakeRequest {
      int32 universityId = 1;
      string userId = 2; 
      string userRole = 3;                       // Add userId for auditing/logging
      repeated IntakeItem intakes = 4;         // Plural for consistency
    }

  // Request structure to delete a specific intake under a university
    message DeleteIntakeRequest {
      int32 universityId = 1;            // Foreign key referencing the University
      int32 intakeId = 2;                // ID of the intake to delete
    }

  // Generic response for create/update/delete operations
    message IntakeResponse {
      int32 status = 1;                  // Operation status flag
      string message = 2;                // Human-readable message
      string error = 3;                  // Optional error detail if operation fails
    }

  // Request structure to fetch all intakes for a university
    message ListIntakesRequest {
      int64 universityId = 1;            // Foreign key referencing the University
    }

  // Detailed data returned for each intake
    message IntakeData {
      int32 id = 1;
      int64 universityId = 2;
      string name = 3;
      string startDate = 4;
      string endDate = 5;
      repeated IntakeDate applicationDates = 6;
      string createdAt = 7;
      string updatedAt = 8;
    }

    message ListIntakesResponse {
      int32 status = 1;
      string message = 2;
      repeated IntakeData data = 3;
      ErrorResponse error = 4;
    }

// ============================== ProgramLevel  Messages ==============================

  // A test and its associated detailed scores
    message ProgramLevelTestScore {
      string test = 1;                  // e.g., "IELTS", "SAT"
      string testScore = 2;  // Score breakdown
    }

  // Steps required in the application process
    message ApplicationStep {
      string title = 1;    // Step name
      bool required = 2;   // Whether this step is mandatory
      int32 weight = 3;    // Display or processing order
    }

  // Request to create a new program level
    message CreateProgramLevelRequest {
      int64 universityId = 1;             // Target university ID
      int64 userId = 2;                   // User performing the creation
      string programLevelName = 3;       // e.g., "Bachelor", "Master"
      int32 durationNumber = 4;           // Duration amount (e.g., 4)
      string durationType = 5;            // e.g., "year", "month"
      repeated int64 intake = 6;           // List of intake IDs
      repeated ProgramLevelTestScore testScore = 7;   // Required test scores
      repeated ApplicationStep applicationStep = 8;   // Application steps
    }

  // Request to update a program level
    message UpdateProgramLevelRequest {
      int64 id = 1;                       
      int64 universityId = 2;         
      int64 userId = 3;               
      string programLevelName = 4;
      int32 durationNumber = 5;
      string durationType = 6;
      repeated int64 intake = 7;
      repeated ProgramLevelTestScore testScore = 8;
      repeated ApplicationStep applicationStep = 9;
    }

  // Request to fetch a program level by ID
    message GetProgramLevelRequest {
      int64 id = 1;
    }

  // Request to fetch all program levels for a university
    message GetProgramLevelListRequest {
      int64 universityId = 1;
    }

  // Request to delete a program level
    message DeleteProgramLevelRequest {
      int64 id = 1;
    }
    // Student type info inside intake
    message StudentTypeInfo {
      string studentType = 1;
      string applicationOpen = 2;
      string applicationDeadline = 3;
      string enrollmentDeadline = 4;
    }
    message IntakeInfo {
      int64 id = 1;
      string name = 2;
      string startDate = 3;
      string endDate = 4;
      repeated StudentTypeInfo studentTypes = 5; // ✅ New nested field
    }
   // Reusable program level details for response
    message ProgramLevel {
      int64 id = 1;
      int64 universityId = 2;
      string programLevelName = 3;
      int32 durationNumber = 4;
      string durationType = 5;
      repeated IntakeInfo intake = 6;
      repeated ProgramLevelTestScore testScore = 7;
      repeated ApplicationStep applicationStep = 8;
      string createdAt = 9;
      string updatedAt = 10;
    }

  // Response for create/update
    message ProgramLevelResponse {
      int32 status = 1;
      string message = 2;
      ProgramLevel data = 3;
      ErrorResponse error = 4;
    }

  // Response for get list
    message ProgramLevelListResponse {
      int32 status = 1;
      string message = 2;
      repeated ProgramLevel data = 3; // ✅ Fixed here
      ErrorResponse error = 4;
    }
// ============================== FieldsOfStudy Messages ==============================

    message FieldOfStudy {
      int64 id = 1;
      int64 universityId = 2;
      string userId = 3;
      string name = 4;
      string createdAt = 5;
      string updatedAt = 6;
    }

    // Create - accepts list of field of study names
    message CreateFieldOfStudyRequest {
      int64 universityId = 1;
      string userId = 2;
      string userRole = 3;
      repeated string names = 4;  
    }

    // Update
    message UpdateFieldOfStudyRequest {
    int64 universityId = 1;
    string userId = 2;
    string userRole = 3;
    repeated string names = 4;  
    }

    // Delete
    message DeleteFieldOfStudyRequest {
      int64 id = 1;
      string userId = 2;
    }

    // Get
    message GetFieldOfStudyRequest {
      int64 id = 1;
    }

    // List with pagination (optional)
    message ListFieldOfStudyRequest {
      int64 universityId = 1;
      int32 page = 2;
      int32 limit = 3;
    }
    // Response messages
      message FieldOfStudyResponse {
        int32 status = 1;
        string message = 2;
        FieldOfStudy data = 3;
        ErrorResponse error = 4;
      }
    message FieldOfStudyListResponse {
      int32 status = 1;
      string message = 2;
      repeated FieldOfStudy data = 3;
      ErrorResponse error = 4;
    }
    
// ============================== Commission Messages ==============================    

  message StudentBaseCommission {
    int32 studentNumber = 1;
    string commission = 2; // e.g., "20%"
  }

  message Commission {
    string programCommissionId = 1;
    string commissionName = 2;
    int32 programLevelId = 3;
    string commissionType = 4;
    string commissionValue = 5;
    string startDate = 6;
    string endDate = 7;
    repeated StudentBaseCommission studentBaseCommission = 8;
    string status = 9;
    string createdAt = 10;
    string updatedAt = 11;
  }
  // Create Commission Request
  message CreateCommissionRequest {
    string universityId = 1;
    string userId = 2;
    string userRole = 3;
    string currency = 4;
    string paymentFrequency = 5;
    string paymentTerm = 6;
    string paymentMethod = 7;
    string commissionPayoutCycle = 8;
    string commissionPeriod = 9;
    repeated CommissionData commission = 10;
  }
  message CommissionData {
    string commissionName = 1;
    int32 programLevelId = 2;
    string commissionType = 3;
    string commissionValue = 4;
    string startDate = 5;
    string endDate = 6;
    repeated StudentBaseCommission studentBaseCommission = 7;
  }
  message UniversityCommission {
    string commissionId=1;
    string universityId = 2;
    string userId = 3;
    string currency = 4;
    string paymentFrequency = 5;
    string paymentTerm = 6;
    string paymentMethod = 7;
    string commissionPayoutCycle = 8;
    string commissionPeriod = 9;
    repeated Commission commission = 10;
  }
  message GetCommissionRequest {
    string universityId = 1;
    string userId = 2;
  }
  // Create Commission Response
  message CreateCommissionResponse {
    int32 status = 1;
    string message = 2;
    UniversityCommission data = 3;
    ErrorResponse error = 4;
  }

  // Update Commission Request
  message UpdateCommissionRequest {
    string commissionId = 1;
    string universityId = 2;
    string userId = 3;
    string userRole = 4;
    string currency = 5;
    string paymentFrequency = 6;
    string paymentTerm = 7;
    string paymentMethod = 8;
    string commissionPayoutCycle = 9;
    string commissionPeriod = 10;
    repeated CommissionUpdateData commission = 11;
  }

  message CommissionUpdateData {
    string programCommissionId = 1; 
    string commissionName = 2;
    int32 programLevelId = 3;
    string commissionType = 4;
    int32 commissionValue = 5;
    string startDate = 6;
    string endDate = 7;
    repeated StudentBaseCommission studentBaseCommission = 8;
    bool isDeleted = 9; // For soft deletes
  }

  // Update Commission Response
  message UpdateCommissionResponse {
    int32 status = 1;
    string message = 2;
    UniversityCommission data = 3;
    string updatedAt = 4;
  }

  // Get Commissions List Request
  message GetCommissionsListRequest {
    string universityId = 1;
    string userId = 2;
    int32 page = 3;
    int32 pageSize = 4;
  }
  

// ============================== Payment Messages ==============================
  message BankDetails {
    string id = 1;
    string universityId = 2; // Newly added
    string bankName = 3;
    string accountName = 4;
    string accountHolderName = 5;
    string ibanSwiftCode = 6;
    string taxId = 7;
    string vatNumber = 8;
    string createdAt = 9;
    string updatedAt = 10;
  }
  // ========== Create ==========
  message CreateBankDetailsRequest {
    string universityId = 1; // Newly added
    string userId = 2;
    string userRole = 3;
    string bankName = 4;
    string accountName = 5;
    string accountHolderName = 6;
    string ibanSwiftCode = 7;
    string taxId = 8;
    string vatNumber = 9;
  }

  message CreateBankDetailsResponse {
    int32 status = 1;
    string message = 2;
    BankDetails data = 3;
    ErrorResponse error = 4;
  }

  // ========== Get ==========
  message GetBankDetailsRequest {
    string universityId = 1;
  }

  message GetBankDetailsResponse {
    int32 status = 1;
    string message = 2;
    BankDetails data = 3;
    ErrorResponse error = 4;
  }

  // ========== Update ==========
  message UpdateBankDetailsRequest {
    string id = 1;
    string universityId = 2; // Newly added
    string bankName = 3;
    string accountName = 4;
    string accountHolderName = 5;
    string ibanSwiftCode = 6;
    string taxId = 7;
    string vatNumber = 8;
  }

  message UpdateBankDetailsResponse {
    int32 status = 1;
    string message = 2;
    BankDetails data = 3;
    ErrorResponse error = 4;
  }

  // ========== Delete ==========
  message DeleteBankDetailsRequest {
    string bankDetailsId = 1;
  }

  message DeleteBankDetailsResponse {
    int32 status = 1;
    string message = 2;
    ErrorResponse error = 3;
  }

  // ========== List ==========
  message ListBankDetailsRequest {
    int32 universityId=1;
    int32 page = 2;
    int32 pageSize = 3;
  }

  message ListBankDetailsResponse {
    int32 status = 1;
    string message = 2;
    repeated BankDetails data = 3;
    ErrorResponse error = 4;
  }


// ===================================Contact Details messages =========================
  message UniversityContactDetails {
    string id = 1;
    string universityId = 2;
    string contactName = 3;
    string designation = 4;
    string email = 5;
    string contactNumber = 6;
    string alternativeEmail = 7;
    string createdAt = 8;
    string updatedAt = 9;
  }

  message CreateUniversityContactRequest {
    string universityId = 1;
    string contactName = 2;
    string designation = 3;
    string email = 4;
    string contactNumber = 5;
    string alternativeEmail = 6;
  }

  message UpdateUniversityContactRequest {
    string id = 1;
    string universityId = 2;
    string contactName = 3;
    string designation = 4;
    string email = 5;
    string contactNumber = 6;
    string alternativeEmail = 7;
  }

  message GetUniversityContactRequest {
    string universityId = 1;
  }

  message DeleteUniversityContactRequest {
    string id = 1;
  }

  message ListUniversityContactRequest {
    string universityId = 1;
    int32 page = 2;
    int32 pageSize = 3;
  }

  message UniversityContactResponse {
    int32 status = 1;
    string message = 2;
    UniversityContactDetails data = 3;
    ErrorResponse error = 4;
  }

  message UniversityContactListResponse {
    int32 status = 1;
    string message = 2;
    repeated UniversityContactDetails data = 3;
    ErrorResponse error = 4;
  }

  message DeleteUniversityContactResponse {
    int32 status = 1;
    string message = 2;
    ErrorResponse error = 3;
  }



// ===================================General setting messages =========================
    message UniversityGeneral {
      string id = 1;
      string universityId = 2;
      bool tuitionFeeDiscount = 3;
      bool financialAidAcceptance = 4;
      bool scholarshipOpportunity = 5;
      bool accommodationStatus = 6;
      bool employmentOpportunities = 7;
      bool activeEnrollment = 8;
      repeated string universityAgreement = 9;
      repeated string universityFeatures = 10;
      repeated string universityProspectus = 11;
      string createdAt = 12;
      string updatedAt = 13;
    }

    message CreateUniversityGeneralRequest {
      string universityId = 1;
      string userId = 2;
      string userRole = 3;
      bool tuitionFeeDiscount = 4;
      bool financialAidAcceptance = 5;
      bool scholarshipOpportunity = 6;
      bool accommodationStatus = 7;
      bool employmentOpportunities = 8;
      bool activeEnrollment = 9;
      repeated string universityAgreement = 10;
      repeated string universityFeatures = 11;
      repeated string universityProspectus = 12;
    }

    message UpdateUniversityGeneralRequest {
      string id = 1;
      string universityId = 2;
      string userId = 3;
      string userRole = 4;
      bool tuitionFeeDiscount = 5;
      bool financialAidAcceptance = 6;
      bool scholarshipOpportunity = 7;
      bool accommodationStatus = 8;
      bool employmentOpportunities = 9;
      bool activeEnrollment = 10;
      repeated string universityAgreement = 11;
      repeated string universityFeatures = 12;
      repeated string universityProspectus = 13;
    }

    message GetUniversityGeneralRequest {
      string universityId = 1;
    }

    message DeleteUniversityGeneralRequest {
      string id = 1;
      string userId = 2;
    }

    message UniversityGeneralResponse {
      int32 status = 1;
      string message = 2;
      UniversityGeneral data = 3;
      ErrorResponse error = 4;
    }

    message UniversityGeneralListResponse {
      int32 status = 1;
      string message = 2;
      repeated UniversityGeneral data = 3;
      ErrorResponse error = 4;
    }

    message DeleteUniversityGeneralResponse {
      int32 status = 1;
      string message = 2;
      ErrorResponse error = 3;
    }




// ===================================University  Fee settings   ======================================
    message IntakeFee {
      string id = 1;
      string name = 2;
      string startDate = 3;
      string endDate = 4;
      string createdAt = 5;
    }

    message ProgramLevelIntakeFee {
      string id = 1;
      string feeTitle = 2;
      double tuitionFee = 3;
      double applicationFee = 4;
      double applicationFeeChargedByUniversity = 5;
      double applicationFeeChargedToStudent = 6;
      int32 paymentDueInDays = 7;
      string feeEffectiveDate = 8;
      string applicationYearStart = 9;
      string applicationYearEnd = 10;
      bool isRefundableToStudent = 11;
      bool isVisibleToStudent = 12;
      bool isActive = 13;
    }

    message ProgramLevelIntake {
      string id = 1;
      string universityId = 2;
      string programLevelId = 3;
      IntakeFee intake = 4;
      repeated ProgramLevelIntakeFee fees = 5;
    }

    message ProgramLevelFeeInfo {
      string id = 1;
      string programLevelName = 2;
      int32 durationNumber = 3;
      string durationType = 4;
      string createdAt = 5;
      repeated ProgramLevelIntake programLevelIntakes = 6; 
    }

    // ========== Requests ==========

    message CreateProgramLevelIntakeFeeRequest {
      int64 universityId = 1;
      string userId = 2;
      string userRole = 3;
      string feeTitle = 4;
      int64 programLevelId = 5;
      repeated int64 intakeIds = 6;
      string applicationYearStart = 7;
      string applicationYearEnd = 8;
      double tuitionFee = 9;
      double applicationFee = 10;
      double applicationFeeChargedByUniversity = 11;
      double applicationFeeChargedToStudent = 12;
      int32 paymentDueInDays = 13;
      string feeEffectiveDate = 14;
      bool isActive = 15;
      bool isRefundableToStudent = 16;
      bool isVisibleToStudent = 17;
    }

    message UpdateProgramLevelIntakeFeeRequest {
      string id = 1;
      int64 universityId = 2;
      string userId = 3;
      string userRole = 4;
      string feeTitle = 5;
      int64 programLevelId = 6;
      int64 intakeId = 7;
      string applicationYearStart = 8;
      string applicationYearEnd = 9;
      double tuitionFee = 10;
      double applicationFee = 11;
      double applicationFeeChargedByUniversity = 12;
      double applicationFeeChargedToStudent = 13;
      int32 paymentDueInDays = 14;
      string feeEffectiveDate = 15;
      bool isActive = 16;
      bool isRefundableToStudent = 17;
      bool isVisibleToStudent = 18;
    }

    message GetProgramLevelIntakeFeeRequest {
      string id = 1;
    }

    message ListProgramLevelIntakeFeeByUniversityRequest {
      int64 universityId = 1;
      int32 page = 2;
      int32 pageSize = 3;
    }

    message DeleteProgramLevelIntakeFeeRequest {
      string id = 1;
      string userId = 2;
    }

    // ========== Error and Responses ==========

    message ProgramLevelIntakeFeeResponse {
      int32 status = 1;
      string message = 2;
      ProgramLevelIntakeFee data = 3;
      ErrorResponse error = 4;
    }

    message ProgramLevelIntakeFeeListResponse {
      int32 status = 1;
      string message = 2;
      repeated ProgramLevelFeeInfo data = 3; // ✅ returns program levels with nested intakes & fees
      ErrorResponse error = 4;
    }

    message DeleteProgramLevelIntakeFeeResponse {
      int32 status = 1;
      string message = 2;
      ErrorResponse error = 3;
    }


// ===================================Course related messages  ======================================
    message Course {
      int64 id = 1;
      int64 universityId = 2;
      string userId = 3;
      string userRole = 4;
      string courseTitle = 5;
      int64 fieldOfStudy = 6;
      string format = 7;
      int64 programLevelId = 8;

      string lastAcademic = 9;
      string minimumGpa = 10;
      string courseRank = 11;
      string acceptanceRate = 12;
      string lectureLanguage = 13;
      string additionalRequirements = 14;

      repeated TestScore testScores = 15;
      repeated FeeCategory feeCategories = 16;

      string createdAt = 17;
      string updatedAt = 18;
    }
    message CourseResponseForList {
      int64 id = 1;
      string courseTitle = 2;
      int64 fieldOfStudy = 3;
      string format = 4;
      int64 programLevelId = 5;

      string lastAcademic = 6;
      string minimumGpa = 7;
      string courseRank = 8;
      string acceptanceRate = 9;
      string lectureLanguage = 10;
      string additionalRequirements = 11;

      repeated TestScoreResponse testScores = 12;
      repeated FeeCategoryResponse feeCategories = 13;

      string createdAt = 14;
      string updatedAt = 15;
    }

    // ===================== Sub-models =====================
    message TestScore {
      int64 id = 1;
      string test = 2;
      string testScore = 3; // JSON string
      bool isNew = 4;
    }
    message TestScoreResponse {
      int64 id = 1;
      string test = 2;
      string testScore = 3; // JSON string
    }
    message Intake {
      int64 id = 1;
      string name = 2;
      string startDate = 3;
      string endDate = 4;
      string createdAt = 5;
      string updatedAt = 6;
    }

    message CourseTestScore {
      int64 id = 1;
      string test = 2;
      string testScore = 3; // JSON string
      string createdAt = 4;
      string updatedAt = 5;
      bool isActive = 6;
      string deletedAt = 7;
    }
    
    message ProgramLevelCourse {
      int64 id = 1;
      string programLevelName = 2;
      int32 durationNumber = 3;
      string durationType = 4;
      int64 universityId = 5;
      repeated CourseResponseForList courses = 6;
      string createdAt = 7;
      string updatedAt = 8;
      bool isActive = 9;
    }
    message ProgramLevelIntakeForCourse {
      // int64 id = 1;
      // int64 universityId = 2;
      // int64 programLevelId = 3;
      // int64 intakeId = 4;
      // string createdAt = 5;
      // string updatedAt = 6;
      // bool isActive = 7;

      Intake intake = 1; // ✅ nested intake info
    }

    message FeeCategory {
      int64 id = 1;
      int64 universityId = 2;
      string feeTitle = 3;
      int64 programLevelId = 4;
      repeated int64 intakeIds = 5;

      string applicationYearStart = 6;
      string applicationYearEnd = 7;
      double tuitionFee = 8;
      double applicationFee = 9;
      double applicationFeeChargedByUniversity = 10;
      double applicationFeeChargedToStudent = 11;
      int32 paymentDueInDays = 12;
      string feeEffectiveDate = 13;
      bool isActive = 14;
      bool isRefundableToStudent = 15;
      bool isVisibleToStudent = 16;
      bool isNew = 17;
    }
    message FeeCategoryResponse {
      int64 id = 1;
      string feeTitle = 2;
      string applicationYearStart = 3;
      string applicationYearEnd = 4;
      double tuitionFee = 5;
      double applicationFee = 6;
      double applicationFeeChargedByUniversity = 7;
      double applicationFeeChargedToStudent = 8;
      int32 paymentDueInDays = 9;
      string feeEffectiveDate = 10;
      bool isActive = 11;
      bool isRefundableToStudent = 12;
      bool isVisibleToStudent = 13;

      ProgramLevelIntakeForCourse programLevelIntake = 14; // ✅ new field
    }

    // ===================== Requests =====================
    message UpdateCourseRequest {
      string id = 1;
      string userId = 2;
      string userRole = 3;
      Course course = 4;
    }

    message GetCourseRequest {
      string id = 1;
    }

    message DeleteCourseRequest {
      string id = 1;
      string userId = 2;
    }

    message ListCourseRequest {
      int64 universityId = 1;
      int32 page = 2;
      int32 pageSize = 3;
    }

    // ===================== Responses =====================
    message CourseResponse {
      int32 status = 1;
      string message = 2;
      CourseResponseForList data = 3;
      ErrorResponse error = 4;
    }

    message ListCourseResponse {
      repeated ProgramLevelCourse data = 1;
      int32 status = 2;
      string message = 3;
      ErrorResponse error = 4;
    }

    message DeleteCourseResponse {
      int32 status = 1;
      string message = 2;
      ErrorResponse error = 3;
    }



// ===================================Campus related messages  ======================================    
    message ProgramLevelInfoForCampus {
      int32 id = 1;
      string programLevelName = 2;
      int32 durationNumber = 3;
    }
    message Campus {
      int64 id = 1;
      string campusName = 2;
      repeated ProgramLevelInfoForCampus programLevels = 3;
      string address = 4;
      int64 countryId = 5;
      string state = 6;
      string city = 7;
      string postalCode = 8;
      string contactNumber = 9;
      string email = 10;
      bool isActive = 11;
      string createdAt = 12;
      string updatedAt = 13;
    }

    // ========================== Create & Update ==========================

    message ListCampusRequest {
      int64 universityId = 1;
      int32 page = 2;
      int32 pageSize = 3;
    }
    message CreateCampusRequest {
      int64 universityId = 1; // 
      string userId = 2;
      string userRole = 3;
      string campusName = 4;
      repeated int32 programLevelIds = 5;
      string address = 6;
      int64 countryId = 7;
      string state = 8;
      string city = 9;
      string postalCode = 10;
      string contactNumber = 11;
      string email = 12;
    }

    message UpdateCampusRequest {
      int64 id = 1;
      int64 universityId = 2; // ✅ Required
      string userId = 3;
      string userRole = 4;
      string campusName = 5;
      repeated int32 programLevelIds = 6;
      string address = 7;
      int64 countryId = 8;
      string state = 9;
      string city = 10;
      string postalCode = 11;
      string contactNumber = 12;
      string email = 13;
    }
    // ========================== Delete & Fetch ==========================
    message IdRequest {
      int64 id = 1;
    }

    message CampusResponse {
      Campus data = 1;
      int32 status = 2;
      string message = 3;
      ErrorResponse error = 4;
    }

    // ========================== Course Reuse from Course Module ==========================
    message ProgramLevelCourses {
      int32 programLevelId = 1;
      string programLevelName = 2;
      repeated CourseResponseForList courses = 3;
    }

    message CampusWithPrograms {
      int64 id = 1;
      string campusName = 2;
      string address = 3;
      int64 countryId = 4;
      string state = 5;
      string city = 6;
      string postalCode = 7;
      string contactNumber = 8;
      string email = 9;
      repeated ProgramLevelCourses programCourses = 10;
    }

    message CampusListResponse {
      repeated CampusWithPrograms data = 1;
      int32 status = 2;
      string message = 3;
      string error = 4;
    }

// ===================================Alumni related messages  ======================================
    message Alumni {
      int64 id = 1;
      int64 universityId = 2;
      string name = 3;
      string organizationName = 4;
      string designation = 5;
      string imageUrl = 6;
      string createdAt = 7;
      string updatedAt = 8;
    }
    // ===================== CRUD Requests =====================
    message CreateAlumni {
      string userId = 1;
      int64 universityId = 2;
      string name = 3;
      string organizationName = 4;
      string designation = 5;
      string imageUrl = 6;
    }

    message CreateAlumniRequest{
      string userId = 1;
      string userRole = 2;
      repeated CreateAlumni data = 3;
    }

    message UpdateAlumniRequest {
      int64 id = 1;
      int64 universityId = 2;
      string userId = 3;
      string userRole = 4;
      string name = 5;
      string organizationName = 6;
      string designation = 7;
      string imageUrl = 8;
    }

    message ListAlumniRequest {
      int64 universityId = 1;
      int32 page = 2;
      int32 pageSize = 3;
    }

    // ===================== Responses =====================
    message AlumniResponse {
      Alumni data = 1;
      int32 status = 2;
      string message = 3;
      ErrorResponse error = 4;
    }

    message AlumniListResponse {
      repeated Alumni data = 1;
      int32 count = 2;
      int32 status = 3;
      string message = 4;
      string error = 5;
    }


// ===================================Univeristy Dummy Configuration Related messages======================================
    message DummyTestScore {
      int64 id = 1;
      string title = 2;
      repeated string modules = 3;
      bool isActive = 4;
      string createdAt = 5;
      string updatedAt = 6;
    }

    // === Create ===
    message CreateDummyTestScoreRequest {
      string userId = 1;
      string userRole = 2;          // e.g. "super_admin"
      string title = 3;             // e.g. IELTS
      repeated string modules = 4;  // e.g. ["reading", "writing", "listening"]
    }

    message CreateDummyTestScoreResponse {
      DummyTestScore data = 1;
      int32 status = 2;
      string message = 3;
      ErrorResponse error = 4;
    }

    // === Delete ===
    message DeleteDummyTestScoreRequest {
      int64 id = 1;
      string userId = 2;
      string userRole = 3;
    }

    message DeleteDummyTestScoreResponse {
      int32 status = 1;
      string message = 2;
      ErrorResponse error = 3;
    }

    // === List ===
    message ListDummyTestScoresRequest {
      int32 page = 1;
      int32 limit = 2;
      string search = 3;
      bool includeInactive = 4;
    }

    message ListDummyTestScoresResponse {
      repeated DummyTestScore data = 1;
      int32 total = 2;
      int32 status = 3;
      string message = 4;
      ErrorResponse error = 5;
    }

message DummyApplicationStep {
  int64 id = 1;
  string title = 2;
  repeated string steps = 3;
  bool isActive = 4;
  string createdAt = 5;
  string updatedAt = 6;
}

message CreateDummyApplicationStepRequest {
  string userId = 1;
  string userRole = 2;
  string title = 3;
  repeated string steps = 4;
}

message CreateDummyApplicationStepResponse {
  DummyApplicationStep data = 1;
  int32 status = 2;
  string message = 3;
  ErrorResponse error = 4;
}

message DeleteDummyApplicationStepRequest {
  int64 id = 1;
  string userId = 2;
  string userRole = 3;
}

message DeleteDummyApplicationStepResponse {
  int32 status = 1;
  string message = 2;
  ErrorResponse error = 3;
}

message ListDummyApplicationStepsRequest {
  int32 page = 1;
  int32 limit = 2;
  string search = 3;
  bool includeInactive = 4;
}

message ListDummyApplicationStepsResponse {
  repeated DummyApplicationStep data = 1;
  int32 total = 2;
  int32 status = 3;
  string message = 4;
  ErrorResponse error = 5;
}
message DummyLectureLanguage {
  int64 id = 1;
  repeated string languages = 2;
  bool isActive = 3;
  string createdAt = 4;
  string updatedAt = 5;
}

message CreateDummyLectureLanguageRequest {
  string userId = 1;
  string userRole = 2;
  repeated string languages = 3;
}

message CreateDummyLectureLanguageResponse {
  DummyLectureLanguage data = 1;
  int32 status = 2;
  string message = 3;
  ErrorResponse error = 4;
}

message DeleteDummyLectureLanguageRequest {
  int64 id = 1;
  string userId = 2;
  string userRole = 3;
}

message DeleteDummyLectureLanguageResponse {
  int32 status = 1;
  string message = 2;
  ErrorResponse error = 3;
}

message ListDummyLectureLanguagesRequest {
  int32 page = 1;
  int32 limit = 2;
  bool includeInactive = 3;
}

message ListDummyLectureLanguagesResponse {
  repeated DummyLectureLanguage data = 1;
  int32 total = 2;
  int32 status = 3;
  string message = 4;
  ErrorResponse error = 5;
}
message DummySocialPlatform {
  int64 id = 1;
  repeated string platforms = 2;
  bool isActive = 3;
  string createdAt = 4;
  string updatedAt = 5;
}

message CreateDummySocialPlatformRequest {
  string userId = 1;
  string userRole = 2;
  repeated string platforms = 3;
}

message CreateDummySocialPlatformResponse {
  DummySocialPlatform data = 1;
  int32 status = 2;
  string message = 3;
  ErrorResponse error = 4;
}

message DeleteDummySocialPlatformRequest {
  int64 id = 1;
  string userId = 2;
  string userRole = 3;
}

message DeleteDummySocialPlatformResponse {
  int32 status = 1;
  string message = 2;
  ErrorResponse error = 3;
}

message ListDummySocialPlatformsRequest {
  int32 page = 1;
  int32 limit = 2;
  bool includeInactive = 3;
}

message ListDummySocialPlatformsResponse {
  repeated DummySocialPlatform data = 1;
  int32 total = 2;
  int32 status = 3;
  string message = 4;
  ErrorResponse error = 5;
}
