import {
  IsString,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>tring,
  <PERSON><PERSON><PERSON>al,
  IsBoolean,
  IsEmail,
  IsArray,
  ValidateNested,
  IsEnum,
} from 'class-validator';
import { Type } from 'class-transformer';

// ───── ENUMS ─────────────────────────────────────────────────────

export enum UniversityType {
  PUBLIC = 'public',
  PRIVATE = 'private',
}

// ───── NESTED DTOs ──────────────────────────────────────────────

// export class CampusDto {
//   @IsOptional() @IsNumber() id?: number;
//   @IsString() name!: string;
//   @IsString() country!: string;
//   @IsString() state!: string;
//   @IsString() address!: string;
//   @IsString() city!: string;
//   @IsString() postalCode!: string;
//   @IsOptional() @IsString() contactNumber?: string;
//   @IsOptional() @IsEmail() email?: string;
// }

export class ContactDto {
  @IsString() phoneNumber!: string;
  @IsEmail() email!: string;
}

export class ApplicationStepDto {
  @IsNumber() orderNumber!: number;
  @IsString() step!: string;
}

export class AcceptanceTimeframeDto {
  @IsNumber() semesterNumber!: number;
  @IsString() duration!: string;
  @IsDateString() startDate!: string;
  @IsDateString() endDate!: string;
}

export class FileDto {
  @IsString() file!: string;
  @IsString() fileType!: string;
  @IsString() fileTitle!: string;
  @IsNumber() order!: number;
}

// ───── CREATE DTO (DB Only) ─────────────────────────────────────

export class CreateUniversityDto {
  @IsNumber() userId!: number;

  @IsString() name!: string;
  @IsString() about!: string;
  @IsEnum(UniversityType) type!: UniversityType;
  @IsString() website!: string;

  @IsDateString() foundedOn!: string;
  @IsString() dliNumber!: string;

  @IsString() address!: string;
  @IsNumber() countryId!: number;
  @IsString() state!: string;
  @IsString() city!: string;
  @IsString() postalCode!: string;
  @IsString() logo!:string;
}

// ───── UPDATE DTO ──────────────────────────────────────────────

export class UpdateUniversityDto {
  @IsOptional() @IsString() name?: string;
  @IsOptional() @IsString() about?: string;
  @IsOptional() @IsEnum(UniversityType) type?: UniversityType;
  @IsOptional() @IsString() website?: string;
  @IsOptional() @IsDateString() foundedOn?: string;
  @IsOptional() @IsString() dliNumber?: string;
  @IsOptional() @IsString() address?: string;
  @IsOptional() @IsNumber() countryId?: number;
  @IsOptional() @IsString() state?: string;
  @IsOptional() @IsString() city?: string;
  @IsOptional() @IsString() postalCode?: string;
}

// ───── FULL PAYLOAD DTO (Frontend Incoming) ─────────────────────

export class UniversityFullPayloadDto extends CreateUniversityDto {
  @IsOptional() @IsNumber() minApplicationFee!: number;
  @IsNumber() costOfLiving!: number;
  @IsString() minApplicationCriteria!: string;
  @IsNumber() standardApplicationFee!: number;
  @IsString() avgUndergradeProgram!: string;
  @IsString() avgGraduateProgram!: string;

  @IsString() discountType!: string;
  @IsNumber() discountValue!: number;
  @IsDateString() startDate!: string;
  @IsDateString() endDate!: string;
  @IsString() promoCode!: string;
  @IsNumber() maxRedemptionAllowed!: number;
  @IsBoolean() tuitionFeeDiscount!: boolean;
  @IsBoolean() financialAidAcceptance!: boolean;
  @IsBoolean() scholarshipOpportunity!: boolean;
  @IsBoolean() accommodationStatus!: boolean;

  @ValidateNested() @Type(() => ContactDto)
  primaryContact!: ContactDto;

  @IsArray() @IsNumber({}, { each: true })
  eligibleCountry!: number[];

  @IsArray() @IsNumber({}, { each: true })
  nonEligibleCountry!: number[];

  @IsArray() @ValidateNested({ each: true }) @Type(() => ApplicationStepDto)
  applicationStep!: ApplicationStepDto[];

  @IsArray() @ValidateNested({ each: true }) @Type(() => AcceptanceTimeframeDto)
  receivingAcceptanceLetterTimeframe!: AcceptanceTimeframeDto[];

  @IsArray() @ValidateNested({ each: true }) @Type(() => FileDto)
  files!: FileDto[];
}

// ───── RESPONSE DTO ──────────────────────────────────────────────

export class UniversityResponseDto {
  @IsNumber() id!: number;
  @IsNumber() userId!: number;

  @IsString() name!: string;
  @IsString() about!: string;
  @IsEnum(UniversityType) type!: UniversityType;
  @IsString() website!: string;

  @IsDateString() foundedOn!: string;
  @IsString() dliNumber!: string;

  @IsString() address!: string;
  @IsNumber() countryId!: number;
  @IsString() state!: string;
  @IsString() city!: string;
  @IsString() postalCode!: string;

  @ValidateNested() @Type(() => ContactDto)
  primaryContact!: ContactDto;

  @IsArray() @IsNumber({}, { each: true })
  eligibleCountry!: number[];

  @IsArray() @IsNumber({}, { each: true })
  nonEligibleCountry!: number[];

  @IsNumber() minApplicationFee!: number;
  @IsNumber() costOfLiving!: number;
  @IsString() minApplicationCriteria!: string;
  @IsNumber() standardApplicationFee!: number;
  @IsString() avgUndergradeProgram!: string;
  @IsString() avgGraduateProgram!: string;

  @IsString() discountType!: string;
  @IsNumber() discountValue!: number;
  @IsDateString() startDate!: string;
  @IsDateString() endDate!: string;
  @IsString() promoCode!: string;
  @IsNumber() maxRedemptionAllowed!: number;

  @IsArray() @ValidateNested({ each: true }) @Type(() => ApplicationStepDto)
  applicationStep!: ApplicationStepDto[];

  @IsArray() @ValidateNested({ each: true }) @Type(() => AcceptanceTimeframeDto)
  receivingAcceptanceLetterTimeframe!: AcceptanceTimeframeDto[];

  @IsBoolean() tuitionFeeDiscount!: boolean;
  @IsBoolean() financialAidAcceptance!: boolean;
  @IsBoolean() scholarshipOpportunity!: boolean;
  @IsBoolean() accommodationStatus!: boolean;

  @IsArray() @ValidateNested({ each: true }) @Type(() => FileDto)
  files!: FileDto[];

  @IsDateString() createdAt!: string;
  @IsDateString() updatedAt!: string;
}
