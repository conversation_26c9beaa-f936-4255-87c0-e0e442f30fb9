import {
  IsString,
  IsDateString,
  IsOptional,
  IsEmail,
} from 'class-validator';

// ───── CREATE DTO ───────────────────────────────

export class CreateUniversityDto {
  @IsString()
  userId!: string;

  @IsString()
  userRole!: string;

  @IsString()
  name!: string;

  @IsString()
  address!: string;

  @IsString() // ✅ changed from number to string
  country!: string;

  @IsString()
  state!: string;

  @IsString()
  city!: string;

  @IsString()
  postalCode!: string;

  @IsString()
  about!: string;

  @IsString()
  type!: string;

  @IsString()
  website!: string;

  @IsDateString()
  foundedOn!: string;

  @IsString()
  institutionCode!: string;

  @IsOptional()
  @IsString()
  logo?: string;

  @IsEmail()
  email!: string;

  @IsString()
  primaryContactNumber!: string;
}

// ───── UPDATE DTO ───────────────────────────────

export class UpdateUniversityDto {
  @IsOptional() @IsString()
  name?: string;

  @IsOptional() @IsString()
  address?: string;

  @IsOptional() @IsString() // ✅ changed from number to string
  country?: string;

  @IsOptional() @IsString()
  state?: string;

  @IsOptional() @IsString()
  city?: string;

  @IsOptional() @IsString()
  postalCode?: string;

  @IsOptional() @IsString()
  about?: string;

  @IsOptional() @IsString()
  type?: string;

  @IsOptional() @IsString()
  website?: string;

  @IsOptional() @IsDateString()
  foundedOn?: string;

  @IsOptional() @IsString()
  institutionCode?: string;

  @IsOptional() @IsString()
  logo?: string;

  @IsOptional() @IsEmail()
  email?: string;

  @IsOptional() @IsString()
  primaryContactNumber?: string;

  @IsOptional() @IsString()
  userId?: string;

  @IsOptional() @IsString()
  userRole?: string;
}

// ───── RESPONSE DTO ─────────────────────────────

export class UniversityResponseDto {
  @IsString()
  id!: string;

  @IsString()
  name!: string;

  @IsString()
  address!: string;

  @IsString()
  country!: string;

  @IsString()
  state!: string;

  @IsString()
  city!: string;

  @IsString()
  postalCode!: string;

  @IsString()
  about!: string;

  @IsString()
  type!: string;

  @IsString()
  website!: string;

  @IsDateString()
  foundedOn!: string;

  @IsString()
  institutionCode!: string;

  @IsString()
  logo!: string;

  @IsEmail()
  email!: string;

  @IsString()
  primaryContactNumber!: string;

  @IsDateString()
  createdAt!: string;

  @IsDateString()
  updatedAt!: string;
}
