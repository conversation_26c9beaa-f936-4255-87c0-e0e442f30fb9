syntax = "proto3";

package agency;



service AgencyService {
  // Agency Operations
  rpc CreateAgency(CreateAgencyRequest) returns (CreateAgencyResponse) {}
  rpc GetAgency(GetAgencyRequest) returns (GetAgencyResponse) {}
  rpc UpdateAgency(UpdateAgencyRequest) returns (UpdateAgencyResponse) {}
  rpc DeleteAgency(DeleteAgencyRequest) returns (DeleteAgencyResponse) {}
  rpc ListAgencies(ListAgencyRequest) returns (ListAgencyResponse) {}

  // Agency Branch Operations
  rpc CreateAgencyBranch(CreateAgencyBranchRequest) returns (CreateAgencyBranchResponse) {}
  rpc GetAgencyBranch(GetAgencyBranchRequest) returns (GetAgencyBranchResponse) {}
  rpc UpdateAgencyBranch(UpdateAgencyBranchRequest) returns (UpdateAgencyBranchResponse) {}
  rpc DeleteAgencyBranch(DeleteAgencyBranchRequest) returns (DeleteAgencyBranchResponse) {}
  rpc ListAgencyBranches(ListAgencyBranchRequest) returns (ListAgencyBranchResponse) {}

  // Agency Contact Operations
  rpc CreateAgencyContact(CreateAgencyContactRequest) returns (CreateAgencyContactResponse) {}
  rpc GetAgencyContact(GetAgencyContactRequest) returns (GetAgencyContactResponse) {}
  rpc UpdateAgencyContact(UpdateAgencyContactRequest) returns (UpdateAgencyContactResponse) {}
  rpc DeleteAgencyContact(DeleteAgencyContactRequest) returns (DeleteAgencyContactResponse) {}
  rpc ListAgencyContacts(ListAgencyContactRequest) returns (ListAgencyContactResponse) {}
}


// ============================== Shared Error Structure ==============================

    // Standard error response
    message ErrorResponse {
      string code = 1;                            
      string message = 2;                         
      string details = 3;                         
      repeated ValidationError validationErrors = 4; 
    }

    // Validation errors for form or field-level feedback
    message ValidationError {
      string field = 1;                          
      string message = 2;                         
      string constraint = 3;                     
    }
// ============================== Agency Messages ==============================

    message Agency {
      string id = 1;
      string agencyName = 2;
      string agencyLogo = 3;
      string address = 4;
      string agencySize = 5;
      string about = 6;
      string primaryContactNumber = 7;
      string country = 8;
      string state = 9;
      string city = 10;
      string postalCode = 11;
      string agencyRegistrationNumber = 12;
      string websiteUrl = 13;
      string email = 14;
      string createdAt = 15;
      string updatedAt = 16;
    }

    // ========== Create ==========
    message CreateAgencyRequest {
      string agencyName = 1;
      string agencyLogo = 2;
      string address = 3;
      string agencySize = 4;
      string about = 5;
      string primaryContactNumber = 6;
      string country = 7;
      string state = 8;
      string city = 9;
      string postalCode = 10;
      string agencyRegistrationNumber = 11;
      string websiteUrl = 12;
      string email = 13;
      string userId = 14;
      string userRole = 15;
    }

    message CreateAgencyResponse {
      int32 status = 1;
      string message = 2;
      Agency data = 3;
      ErrorResponse error = 4;
    }

    // ========== Get ==========
    message GetAgencyRequest {
      string id = 1;
    }

    message GetAgencyResponse {
      int32 status = 1;
      string message = 2;
      Agency data = 3;
      ErrorResponse error = 4;
    }

    // ========== Update ==========
    message UpdateAgencyRequest {
      string id = 1;
      string agencyName = 2;
      string agencyLogo = 3;
      string address = 4;
      string agencySize = 5;
      string about = 6;
      string primaryContactNumber = 7;
      string country = 8;
      string state = 9;
      string city = 10;
      string postalCode = 11;
      string agencyRegistrationNumber = 12;
      string websiteUrl = 13;
      string email = 14;
      string userId = 15;
      string userRole = 16;
    }

    message UpdateAgencyResponse {
      int32 status = 1;
      string message = 2;
      Agency data = 3;
      ErrorResponse error = 4;
    }

    // ========== Delete ==========
    message DeleteAgencyRequest {
      string id = 1;
    }

    message DeleteAgencyResponse {
      int32 status = 1;
      string message = 2;
      ErrorResponse error = 3;
    }

    // ========== List ==========
    message ListAgencyRequest {
      int32 page = 1;
      int32 pageSize = 2;
      string search = 3;
    }

    message ListAgencyResponse {
      int32 status = 1;
      string message = 2;
      repeated Agency data = 3;
      ErrorResponse error = 4;
    }


// ============================== Agency Branch Messages ==============================

    // ========== Entity ==========
    message AgencyBranch {
      string id = 1;
      string agencyId = 2;
      string branchName = 3;
      string address = 4;
      string country = 5;
      string state = 6;
      string city = 7;
      string postalCode = 8;
      string managerContactNumber = 9;
      string email = 10;
      string createdAt = 11;
      string updatedAt = 12;
    }

    // ========== Input DTO ==========
    message CreateAgencyBranch {
      string branchName = 1;
      string address = 2;
      string country = 3;
      string state = 4;
      string city = 5;
      string postalCode = 6;
      string managerContactNumber = 7;
      string email = 8;
    }

    // ========== Create ==========
    message CreateAgencyBranchRequest {
      string agencyId = 1;
      string userId = 2;
      string userRole = 3;
      repeated CreateAgencyBranch data = 4;
    }

    message CreateAgencyBranchResponse {
      int32 status = 1;
      string message = 2;
      repeated AgencyBranch data = 3;
      ErrorResponse error = 4;
    }

    // ========== Update ==========
    message UpdateAgencyBranchRequest {
      string agencyId = 1;
      string userId = 2;
      string userRole = 3;
      repeated AgencyBranch data = 4; // full objects with id included
    }

    message UpdateAgencyBranchResponse {
      int32 status = 1;
      string message = 2;
      repeated AgencyBranch data = 3;
      ErrorResponse error = 4;
    }

    // ========== Get ==========
    message GetAgencyBranchRequest {
      string id = 1;
    }

    message GetAgencyBranchResponse {
      int32 status = 1;
      string message = 2;
      AgencyBranch data = 3;
      ErrorResponse error = 4;
    }

    // ========== Delete ==========
    message DeleteAgencyBranchRequest {
      string id = 1;
    }

    message DeleteAgencyBranchResponse {
      int32 status = 1;
      string message = 2;
      ErrorResponse error = 3;
    }

    // ========== List ==========
    message ListAgencyBranchRequest {
      string agencyId = 1;
      int32 page = 2;
      int32 pageSize = 3;
    }

    message ListAgencyBranchResponse {
      int32 status = 1;
      string message = 2;
      repeated AgencyBranch data = 3;
      ErrorResponse error = 4;
    }


// ============================== Agency Contact Messages ==============================

    message AgencyContact {
      string id = 1;
      string agencyId = 2;

      // Agency Owner
      string ownerName = 3;
      string ownerContactNumber = 4;
      string ownerAlternateContactNumber = 5;
      string ownerEmail = 6;

      // Primary Contact
      string primaryPersonName = 7;
      string primaryPersonDesignation = 8;
      string primaryContactNumber = 9;
      string primaryAlternateContactNumber = 10;
      string primaryEmail = 11;

      string createdAt = 12;
      string updatedAt = 13;
    }

    // ========== Create ==========
    message CreateAgencyContactRequest {
      string agencyId = 1;
      string userId = 2;
      string userRole = 3;

      string ownerName = 4;
      string ownerContactNumber = 5;
      string ownerAlternateContactNumber = 6;
      string ownerEmail = 7;

      string primaryPersonName = 8;
      string primaryPersonDesignation = 9;
      string primaryContactNumber = 10;
      string primaryAlternateContactNumber = 11;
      string primaryEmail = 12;
    }

    message CreateAgencyContactResponse {
      int32 status = 1;
      string message = 2;
      AgencyContact data = 3;
      ErrorResponse error = 4;
    }

    // ========== Get ==========
    message GetAgencyContactRequest {
      string id = 1;
    }

    message GetAgencyContactResponse {
      int32 status = 1;
      string message = 2;
      AgencyContact data = 3;
      ErrorResponse error = 4;
    }

    // ========== Update ==========
    message UpdateAgencyContactRequest {
      string id = 1;
      string userId = 2;
      string userRole = 3;

      string ownerName = 4;
      string ownerContactNumber = 5;
      string ownerAlternateContactNumber = 6;
      string ownerEmail = 7;

      string primaryPersonName = 8;
      string primaryPersonDesignation = 9;
      string primaryContactNumber = 10;
      string primaryAlternateContactNumber = 11;
      string primaryEmail = 12;
    }

    message UpdateAgencyContactResponse {
      int32 status = 1;
      string message = 2;
      AgencyContact data = 3;
      ErrorResponse error = 4;
    }

    // ========== Delete ==========
    message DeleteAgencyContactRequest {
      string id = 1;
    }

    message DeleteAgencyContactResponse {
      int32 status = 1;
      string message = 2;
      ErrorResponse error = 3;
    }

    // ========== List ==========
    message ListAgencyContactRequest {
      string agencyId = 1;
      int32 page = 2;
      int32 pageSize = 3;
    }

    message ListAgencyContactResponse {
      int32 status = 1;
      string message = 2;
      repeated AgencyContact data = 3;
      ErrorResponse error = 4;
    }
