syntax = "proto3";

package students;

service StudentService {
  rpc CreateStudent(CreateStudentRequest) returns (StudentResponse) {}
  rpc GetStudent(GetStudentRequest) returns (StudentResponse) {}
  rpc UpdateStudent(UpdateStudentRequest) returns (StudentResponse) {}
  rpc DeleteStudent(DeleteStudentRequest) returns (DeleteStudentResponse) {}
}

// --- Messages ---

message CreateStudentRequest {
  string name = 1;
  string first_name = 2;
  string native_name = 3;
  string email = 4;
  string date_of_birth = 5;
  string gender = 6;
  string father_name = 7;
  string mother_name = 8;
  string national_id = 9;
  string passport_number = 10;
  string marital_status = 11;
  string spouse_name = 12;
  string spouse_passport_number = 13;

  // Present Address
  string present_address = 14;
  string present_country = 15;
  string present_state = 16;
  string present_city = 17;
  string present_postal_code = 18;

  // Permanent Address
  string permanent_address = 19;
  string permanent_country = 20;
  string permanent_state = 21;
  string permanent_city = 22;
  string permanent_postal_code = 23;

  // Sponsor & Guardian
  string sponsor_name = 24;
  string relationship = 25;
  string phone_number = 26;
  string guardian_number = 27;

  // Preferences
  repeated string preferred_subjects = 28;
  repeated string preferred_countries = 29;

  // Social links
  repeated SocialLink social_links = 30;

  string reference = 31;
  string note = 32;
}

message UpdateStudentRequest {
  string id = 1; // Required to identify which student to update
  UpdateStudentPayload payload = 2;
}

message UpdateStudentPayload {
  string name = 1;
  string first_name = 2;
  string native_name = 3;
  string email = 4;
  string date_of_birth = 5;
  string gender = 6;
  string father_name = 7;
  string mother_name = 8;
  string national_id = 9;
  string passport_number = 10;
  string marital_status = 11;
  string spouse_name = 12;
  string spouse_passport_number = 13;

  // Present Address
  string present_address = 14;
  string present_country = 15;
  string present_state = 16;
  string present_city = 17;
  string present_postal_code = 18;

  // Permanent Address
  string permanent_address = 19;
  string permanent_country = 20;
  string permanent_state = 21;
  string permanent_city = 22;
  string permanent_postal_code = 23;

  // Sponsor & Guardian
  string sponsor_name = 24;
  string relationship = 25;
  string phone_number = 26;
  string guardian_number = 27;

  // Preferences
  repeated string preferred_subjects = 28;
  repeated string preferred_countries = 29;

  // Social links
  repeated SocialLink social_links = 30;

  string reference = 31;
  string note = 32;
}

message SocialLink {
  string title = 1;
  string url = 2;
}

message GetStudentRequest {
  string id = 1;
}

message DeleteStudentRequest {
  string id = 1;
}

message DeleteStudentResponse {
  bool success = 1;
  string message = 2;
}

message StudentResponse {
  Student student = 1;
}

// Optionally return full student object for get/update/create
message Student {
  string id = 1;
  string name = 2;
  string first_name = 3;
  string native_name = 4;
  string email = 5;
  string date_of_birth = 6;
  string gender = 7;
  string father_name = 8;
  string mother_name = 9;
  string national_id = 10;
  string passport_number = 11;
  string marital_status = 12;
  string spouse_name = 13;
  string spouse_passport_number = 14;

  string present_address = 15;
  string present_country = 16;
  string present_state = 17;
  string present_city = 18;
  string present_postal_code = 19;

  string permanent_address = 20;
  string permanent_country = 21;
  string permanent_state = 22;
  string permanent_city = 23;
  string permanent_postal_code = 24;

  string sponsor_name = 25;
  string relationship = 26;
  string phone_number = 27;
  string guardian_number = 28;

  repeated string preferred_subjects = 29;
  repeated string preferred_countries = 30;
  repeated SocialLink social_links = 31;

  string reference = 32;
  string note = 33;

  string created_at = 34;
  string updated_at = 35;
}
