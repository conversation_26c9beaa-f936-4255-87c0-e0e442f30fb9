syntax = "proto3";

package audit;

service AuditService {
  rpc CreateAuditLog (CreateAuditLogRequest) returns (AuditLogResponse) {}
  rpc GetAuditLog (GetAuditLogRequest) returns (AuditLogResponse) {}
  rpc ListAuditLogs (ListAuditLogsRequest) returns (ListAuditLogsResponse) {}
  rpc SearchAuditLogs (SearchAuditLogsRequest) returns (ListAuditLogsResponse) {}
}

message AuditLog {
  int32 id = 1;
  int32 userId = 2;
  string userRole = 3;
  string serviceName = 4;
  string actions = 5;
  string resourceType = 6;
  int32 resourceId = 7;
  string description = 8;
  map<string, string> metadata = 9;
  string ipAddress = 10;
  string userAgent = 11;
  int64 createdAt = 12;
  string source = 13;
}

message CreateAuditLogRequest {
  int64 userId = 1;
  string userRole = 2;
  string actions = 3;
  string serviceName = 4;
  string resourceType = 5;
  int64 resourceId = 6;
  string description = 7;
  map<string, string> metadata = 8;
  string ipAddress = 9;
  string userAgent = 10;
  string source = 11;
}


message GetAuditLogRequest {
  int32 id = 1;
}

message AuditLogResponse {
  AuditLog auditLog = 1;
  string message = 2;
}

message ListAuditLogsRequest {
  int32 pageSize = 1;
  string pageToken = 2;
  string orderBy = 3;
}

message ListAuditLogsResponse {
  repeated AuditLog auditLogs = 1;
  string nextPageToken = 2;
  int32 totalSize = 3;
}

message SearchAuditLogsRequest {
  int32 userId = 1;
  string serviceName = 2;
  string actions = 3;
  string resourceType = 4;
  int32 resourceId = 5;
  int64 startDate = 6;
  int64 endDate = 7;
  int32 pageSize = 8;
  string pageToken = 9;
}
