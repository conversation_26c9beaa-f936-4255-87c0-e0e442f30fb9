syntax = "proto3";
package auth;

// bring in everything else:
import "common.proto";
import "authentication.proto";
import "otp.proto";
import "user.proto";
import "departments.proto";
import "modules.proto";
import "roles.proto";

service AuthService {
  // Authentication
  rpc Register(RegisterRequest) returns (RegisterResponse);
  rpc Login(LoginRequest) returns (LoginResponse);
  rpc Logout(LogoutRequest) returns (LogoutResponse);
  rpc ValidateToken(ValidateTokenRequest) returns (ValidateTokenResponse);
  rpc RefreshToken(RefreshTokenRequest) returns (RefreshTokenResponse);
  rpc SsoAuth(SsoAuthRequest) returns (SsoAuthResponse);
  rpc GenerateOtp(GenerateOtpRequest) returns (GenerateOtpResponse);
  rpc VerifyOtp(VerifyOtpRequest) returns (VerifyOtpResponse);

  // Modules
  rpc CreateModule(CreateModuleRequest) returns (CreateModuleResponse);
  rpc BulkCreateModules(BulkCreateModulesRequest) returns (BulkCreateModulesResponse);
  rpc ListModules(GetDataRequest) returns (ListModulesResponse);

  // Departments
  rpc CreateDepartment(CreateDepartmentRequest) returns (CreateDepartmentResponse);
  rpc AssignDepartment(AssignDepartmentRequest) returns (AssignDepartmentResponse);
  rpc ListDepartments(GetDataRequest) returns (ListDepartmentsResponse);
  rpc ListDepartmentsWithUsers(GetDataRequest) returns (ListDepartmentsWithUsersResponse);

  // Roles
  rpc GetRoleDetails(GetRoleDetailsRequest) returns (RoleDetailsResponse);
  rpc GetRolesWithDetails(GetDataRequest) returns (RolesWithDetailsResponse);
  rpc CreateRoleWithDetails(CreateRoleWithDetailsRequest) returns (CreateRoleWithDetailsResponse);
  rpc UpdateRole(UpdateRoleRequest) returns (UpdateRoleResponse);
  rpc DeleteRole(DeleteRoleRequest) returns (DeleteRoleResponse);
}
