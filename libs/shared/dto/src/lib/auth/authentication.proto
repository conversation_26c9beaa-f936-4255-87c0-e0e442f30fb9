syntax = "proto3";
package auth;
import "common.proto";

message LoginRequest {
  string email = 1;
  string password = 2;
  string ipAddress = 3;
  string userAgent = 4;
}
message LoginResponse {
  bool success = 1;
  string message = 2;
  string accessToken = 3;
  string refreshToken = 4;
  UserInfo user = 5;
}

message RegisterRequest {
  string name = 1;
  string nationality = 2;
  string organizationName = 3;
  string email = 4;
  string password = 5;
  string phone = 6;
  string roleName = 7;
  string departmentName = 8;
  string ipAddress = 9;
  string userAgent = 10;
}
message RegisterResponse {
  bool success = 1;
  string message = 2;
}

message LogoutRequest {
  string accessToken = 1;
}
message LogoutResponse {
  bool success = 1;
  string message = 2;
}

message ValidateTokenRequest {
  string token = 1;
}
message ValidateTokenResponse {
  bool valid = 1;
  string message = 2;
  UserInfo user = 3;
}

message RefreshTokenRequest {
  string refreshToken = 1;
}
message RefreshTokenResponse {
  bool success = 1;
  string message = 2;
  string accessToken = 3;
  string refreshToken = 4;
}

message SsoAuthRequest {
  string provider = 1;
  string token = 2;
  string email = 3;
  string name = 4;
  string ipAddress = 5;
  string userAgent = 6;
}
message SsoAuthResponse {
  bool success = 1;
  string message = 2;
  string accessToken = 3;
  string refreshToken = 4;
  UserInfo user = 5;
}

// basic user type (without full nested roles/depts)
message UserInfo {
  int64 id = 1;
  string name = 2;
  string email = 3;
  string phone = 4;
  string socialLink = 5;
  string gender = 6;
  string nationality = 7;
  string presentAddress = 8;
  string presentCountry = 9;
  string presentState = 10;
  string presentCity = 11;
  string presentAddressZipcode = 12;
  string permanentAddress = 13;
  string permanentCountry = 14;
  string permanentState = 15;
  string permanentCity = 16;
  string permanentAddressZipcode = 17;
  string status = 18;
  repeated RoleBasic roles = 19;
  repeated DepartmentInfo departments = 20;
  repeated SocialSiteInfo socialSites = 21;
}
